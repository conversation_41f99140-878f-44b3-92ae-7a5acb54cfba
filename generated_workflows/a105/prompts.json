[{"prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount equals 3731. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name CFO Approval, unique name CFO_Approval, description 'This is for CFO Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_6076” and Unique Name to “FieldUpdate_6076,” then choose the field “Quantity.” Under Final Rejection Actions, add another Field Update: Name “FU_6482” and select “Quantity,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expenditure Report Approval", "uniqueName": "Expenditure_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "equals", "value": "3731"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "First Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_6076", "uniqueName": "FieldUpdate_6076", "fieldToUpdate": "Quantity", "finalRejectionFieldUpdate": {"name": "FU_2221", "fieldToUpdate": "Quantity"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount greater than 758. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name GM Approval, unique name GM_Approval, description 'This is for GM Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_6232” and Unique Name to “FieldUpdate_6232,” then choose the field “Main Competitor(s).” Under Final Rejection Actions, add another Field Update: Name “FU_2735” and select “Main Competitor(s),” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expenditure Report Approval", "uniqueName": "Expenditure_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "greater than", "value": "758"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "First Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_6232", "uniqueName": "FieldUpdate_6232", "fieldToUpdate": "Main Competitor(s)", "finalRejectionFieldUpdate": {"name": "FU_8932", "fieldToUpdate": "Main Competitor(s)"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount less than 4451. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name GM Approval, unique name GM_Approval, description 'This is for GM Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_1396” and Unique Name to “FieldUpdate_1396,” then choose the field “Opportunity Owner.” Under Final Rejection Actions, add another Field Update: Name “FU_9483” and select “Opportunity Owner,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expenditure Report Approval", "uniqueName": "Expenditure_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "less than", "value": "4451"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "Lead Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_1396", "uniqueName": "FieldUpdate_1396", "fieldToUpdate": "Opportunity Owner", "finalRejectionFieldUpdate": {"name": "FU_5807", "fieldToUpdate": "Opportunity Owner"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expense Report Approval.” Set entry criteria where Opportunity: Amount equals 2487. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name CFO Approval, unique name CFO_Approval, description 'This is for CFO Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_4303” and Unique Name to “FieldUpdate_4303,” then choose the field “Next Step.” Under Final Rejection Actions, add another Field Update: Name “FU_9425” and select “Next Step,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expense Report Approval", "uniqueName": "Expense_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "equals", "value": "2487"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "First Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_4303", "uniqueName": "FieldUpdate_4303", "fieldToUpdate": "Next Step", "finalRejectionFieldUpdate": {"name": "FU_1744", "fieldToUpdate": "Next Step"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Spendings Report Approval.” Set entry criteria where Opportunity: Amount equals 3906. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name CEO <PERSON><PERSON><PERSON>al, unique name CEO_Approval, description 'This is for CEO Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_3582” and Unique Name to “FieldUpdate_3582,” then choose the field “Delivery/Installation Status.” Under Final Rejection Actions, add another Field Update: Name “FU_9959” and select “Delivery/Installation Status,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Spendings Report Approval", "uniqueName": "Spendings_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "equals", "value": "3906"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "Lead Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_3582", "uniqueName": "FieldUpdate_3582", "fieldToUpdate": "Delivery/Installation Status", "finalRejectionFieldUpdate": {"name": "FU_4162", "fieldToUpdate": "Delivery/Installation Status"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount less than 1355. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name Manager Approval, unique name Manager_Approval, description 'This is for Manager Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_1850” and Unique Name to “FieldUpdate_1850,” then choose the field “Type.” Under Final Rejection Actions, add another Field Update: Name “FU_1860” and select “Type,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expenditure Report Approval", "uniqueName": "Expenditure_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "less than", "value": "1355"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "First Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_1850", "uniqueName": "FieldUpdate_1850", "fieldToUpdate": "Type", "finalRejectionFieldUpdate": {"name": "FU_7497", "fieldToUpdate": "Type"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount greater than 2014. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name GM Approval, unique name GM_Approval, description 'This is for GM Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_6283” and Unique Name to “FieldUpdate_6283,” then choose the field “Lead Source.” Under Final Rejection Actions, add another Field Update: Name “FU_8830” and select “Lead Source,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expenditure Report Approval", "uniqueName": "Expenditure_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "greater than", "value": "2014"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "First Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_6283", "uniqueName": "FieldUpdate_6283", "fieldToUpdate": "Lead Source", "finalRejectionFieldUpdate": {"name": "FU_8867", "fieldToUpdate": "Lead Source"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Spendings Report Approval.” Set entry criteria where Opportunity: Amount greater than 1397. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name CEO <PERSON><PERSON><PERSON><PERSON>, unique name CEO_Approval, description 'This is for CEO Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_7002” and Unique Name to “FieldUpdate_7002,” then choose the field “Main Competitor(s).” Under Final Rejection Actions, add another Field Update: Name “FU_4480” and select “Main Competitor(s),” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Spendings Report Approval", "uniqueName": "Spendings_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "greater than", "value": "1397"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "Manager <PERSON><PERSON><PERSON><PERSON>", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_7002", "uniqueName": "FieldUpdate_7002", "fieldToUpdate": "Main Competitor(s)", "finalRejectionFieldUpdate": {"name": "FU_9100", "fieldToUpdate": "Main Competitor(s)"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Spendings Report Approval.” Set entry criteria where Opportunity: Amount less or equal 4719. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name CFO Approval, unique name CFO_Approval, description 'This is for CFO Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_3105” and Unique Name to “FieldUpdate_3105,” then choose the field “Next Step.” Under Final Rejection Actions, add another Field Update: Name “FU_3937” and select “Next Step,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Spendings Report Approval", "uniqueName": "Spendings_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "less or equal", "value": "4719"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "Lead Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_3105", "uniqueName": "FieldUpdate_3105", "fieldToUpdate": "Next Step", "finalRejectionFieldUpdate": {"name": "FU_4102", "fieldToUpdate": "Next Step"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount not equal to 1436. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name CEO <PERSON><PERSON><PERSON><PERSON>, unique name CEO_Approval, description 'This is for CEO <PERSON><PERSON><PERSON><PERSON>.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_7804” and Unique Name to “FieldUpdate_7804,” then choose the field “Probability (%).” Under Final Rejection Actions, add another Field Update: Name “FU_7638” and select “Probability (%),” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expenditure Report Approval", "uniqueName": "Expenditure_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "not equal to", "value": "1436"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "Manager <PERSON><PERSON><PERSON><PERSON>", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_7804", "uniqueName": "FieldUpdate_7804", "fieldToUpdate": "Probability (%)", "finalRejectionFieldUpdate": {"name": "FU_2867", "fieldToUpdate": "Probability (%)"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount greater than 4248. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name CEO <PERSON><PERSON><PERSON><PERSON>, unique name CEO_Approval, description 'This is for CEO Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_3529” and Unique Name to “FieldUpdate_3529,” then choose the field “Tracking Number.” Under Final Rejection Actions, add another Field Update: Name “FU_9489” and select “Tracking Number,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expenditure Report Approval", "uniqueName": "Expenditure_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "greater than", "value": "4248"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "First Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_3529", "uniqueName": "FieldUpdate_3529", "fieldToUpdate": "Tracking Number", "finalRejectionFieldUpdate": {"name": "FU_4558", "fieldToUpdate": "Tracking Number"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount not equal to 1161. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name GM Approval, unique name GM_Approval, description 'This is for GM Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_8591” and Unique Name to “FieldUpdate_8591,” then choose the field “Current Generator(s).” Under Final Rejection Actions, add another Field Update: Name “FU_3123” and select “Current Generator(s),” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expenditure Report Approval", "uniqueName": "Expenditure_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "not equal to", "value": "1161"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "First Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_8591", "uniqueName": "FieldUpdate_8591", "fieldToUpdate": "Current Generator(s)", "finalRejectionFieldUpdate": {"name": "FU_2336", "fieldToUpdate": "Current Generator(s)"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Spendings Report Approval.” Set entry criteria where Opportunity: Amount greater or equal 2868. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name GM Approval, unique name GM_Approval, description 'This is for GM Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_8452” and Unique Name to “FieldUpdate_8452,” then choose the field “Close Date.” Under Final Rejection Actions, add another Field Update: Name “FU_6283” and select “Close Date,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Spendings Report Approval", "uniqueName": "Spendings_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "greater or equal", "value": "2868"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "Manager <PERSON><PERSON><PERSON><PERSON>", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_8452", "uniqueName": "FieldUpdate_8452", "fieldToUpdate": "Close Date", "finalRejectionFieldUpdate": {"name": "FU_2949", "fieldToUpdate": "Close Date"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount less than 2009. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name GM Approval, unique name GM_Approval, description 'This is for GM Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_1274” and Unique Name to “FieldUpdate_1274,” then choose the field “Order Number.” Under Final Rejection Actions, add another Field Update: Name “FU_8981” and select “Order Number,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expenditure Report Approval", "uniqueName": "Expenditure_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "less than", "value": "2009"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "Manager <PERSON><PERSON><PERSON><PERSON>", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_1274", "uniqueName": "FieldUpdate_1274", "fieldToUpdate": "Order Number", "finalRejectionFieldUpdate": {"name": "FU_9376", "fieldToUpdate": "Order Number"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Spendings Report Approval.” Set entry criteria where Opportunity: Amount greater than 4433. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name CEO <PERSON><PERSON><PERSON><PERSON>, unique name CEO_Approval, description 'This is for CEO <PERSON><PERSON>roval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_6762” and Unique Name to “FieldUpdate_6762,” then choose the field “Main Competitor(s).” Under Final Rejection Actions, add another Field Update: Name “FU_6045” and select “Main Competitor(s),” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Spendings Report Approval", "uniqueName": "Spendings_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "greater than", "value": "4433"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "First Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_6762", "uniqueName": "FieldUpdate_6762", "fieldToUpdate": "Main Competitor(s)", "finalRejectionFieldUpdate": {"name": "FU_8569", "fieldToUpdate": "Main Competitor(s)"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Spendings Report Approval.” Set entry criteria where Opportunity: Amount not equal to 1043. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name CEO <PERSON><PERSON><PERSON><PERSON>, unique name CEO_Approval, description 'This is for CEO <PERSON><PERSON><PERSON><PERSON>.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_7717” and Unique Name to “FieldUpdate_7717,” then choose the field “Stage.” Under Final Rejection Actions, add another Field Update: Name “FU_1289” and select “Stage,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Spendings Report Approval", "uniqueName": "Spendings_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "not equal to", "value": "1043"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "Lead Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_7717", "uniqueName": "FieldUpdate_7717", "fieldToUpdate": "Stage", "finalRejectionFieldUpdate": {"name": "FU_8890", "fieldToUpdate": "Stage"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount greater than 4401. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name Manager Approval, unique name Manager_Approval, description 'This is for Manager Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_4396” and Unique Name to “FieldUpdate_4396,” then choose the field “Close Date.” Under Final Rejection Actions, add another Field Update: Name “FU_8153” and select “Close Date,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expenditure Report Approval", "uniqueName": "Expenditure_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "greater than", "value": "4401"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "Lead Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_4396", "uniqueName": "FieldUpdate_4396", "fieldToUpdate": "Close Date", "finalRejectionFieldUpdate": {"name": "FU_8676", "fieldToUpdate": "Close Date"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expense Report Approval.” Set entry criteria where Opportunity: Amount less than 658. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name GM Approval, unique name GM_Approval, description 'This is for GM Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_4632” and Unique Name to “FieldUpdate_4632,” then choose the field “Order Number.” Under Final Rejection Actions, add another Field Update: Name “FU_8317” and select “Order Number,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expense Report Approval", "uniqueName": "Expense_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "less than", "value": "658"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "Lead Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_4632", "uniqueName": "FieldUpdate_4632", "fieldToUpdate": "Order Number", "finalRejectionFieldUpdate": {"name": "FU_5786", "fieldToUpdate": "Order Number"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount less or equal 292. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name Manager Approval, unique name Manager_Approval, description 'This is for Manager Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_2160” and Unique Name to “FieldUpdate_2160,” then choose the field “Lead Source.” Under Final Rejection Actions, add another Field Update: Name “FU_3659” and select “Lead Source,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Expenditure Report Approval", "uniqueName": "Expenditure_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "less or equal", "value": "292"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "First Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_2160", "uniqueName": "FieldUpdate_2160", "fieldToUpdate": "Lead Source", "finalRejectionFieldUpdate": {"name": "FU_9596", "fieldToUpdate": "Lead Source"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}, {"prompt": "Create an approval process using Standard Setup Wizard named “Spendings Report Approval.” Set entry criteria where Opportunity: Amount not equal to 1204. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name GM Approval, unique name GM_Approval, description 'This is for GM Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_9758” and Unique Name to “FieldUpdate_9758,” then choose the field “Lead Source.” Under Final Rejection Actions, add another Field Update: Name “FU_3167” and select “Lead Source,” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.", "result": {"approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Spendings Report Approval", "uniqueName": "Spendings_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "not equal to", "value": "1204"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "High-value Opportunity Notification"}, "step5": {"selectedFields": [], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "approvalStep": {"name": "First Approval", "stepNumber": 1, "criteria": "All records enter this step", "approverField": "Manager"}, "active": true, "hasCreatedSteps": true}], "fieldUpdate": {"approvalAction": "Field Update", "description": "Field update for FieldUpdate_9758", "uniqueName": "FieldUpdate_9758", "fieldToUpdate": "Lead Source", "finalRejectionFieldUpdate": {"name": "FU_2047", "fieldToUpdate": "Lead Source"}, "approvalPageFields": ["Amount", "Stage", "Close Date"], "activated": true}}}]