[{"prompt": "Create a classic email template with Text type titled 'Appointment for Unauthenticated User using Engagement Channels For third party.' in the 'Unfiled Public Classic Email Templates' folder with subject 'Appointment for Unauthenticated User using Engagement Channels For third party. Notification' and body 'Email template for confirmation of an appointment...'. Create an approval process using Standard Setup Wizard named “Spendings Report Approval.” Set entry criteria where Opportunity: Amount not equal to 4361. Specify the approver field as Manager. Use the email template “Appointment for Unauthenticated User using Engagement Channels For third party.” for assignment emails. Include fields Account Name, Amount and Contract in the approval layout. Select 'Record Creator' from 'Creator' option of the submitter type. Then create an approval step with the name CFO Approval, description 'This is for CFO Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process.", "result": {"classicEmailTemplate": {}, "classicEmailTemplateList": [{"templateType": "Custom", "folder": "Unfiled Public Classic Email Templates", "availableForUse": false, "emailTemplateName": "Appointment for Unauthenticated User using Engagement Channels For third party.", "templateUniqueName": "Appointment for Unauthenticated User using Engagement Channels For third party.", "encoding": "Unicode (UTF-8)", "description": "", "subject": "Appointment for Unauthenticated User using Engagement Channels For third party. Notification", "body": "Email template for confirmation of an appointment..."}], "fieldUpdate": {}, "fieldUpdateList": [], "approvalProcesses": {"step1": {}, "step2": {}, "step3": {}, "step4": {}, "step5": {}, "step6": {}, "active": false, "hasCreatedSteps": false}, "approvalProcessesList": [{"step1": {"name": "Spendings Report Approval", "uniqueName": "Spendings_Report_Approval", "desc": ""}, "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": [{"field": "Opportunity: Amount", "operator": "not equal to", "value": "4361"}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}, {"field": "--None--", "operator": "--None--", "value": ""}]}, "step3": {"nextAutomatedApproverDeterminedBy": "Manager", "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.", "useApproverFieldOfOpportunityOwner": false}, "step4": {"approvalAssignmentEmailTemplate": "Appointment for Unauthenticated User using Engagement Channels For third party."}, "step5": {"selectedFields": ["Opportunity Name", "Opportunity Owner", "Account Name", "Amount", "Contract"], "approvalPageFields": true, "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"}, "step6": {"submitterType": "Creator", "allowedSubmitters": ["Record Creator"], "pageLayoutSettings": true, "allowSubmittersToRecallApprovalRequests": false}, "approvalStep": {"approvalStep": {"approvalStepOption": "Yes, I'd like to create an approval step now."}, "approvalStep1": {"approvalProcessName": "CFO Approval", "approvalProcessUniqueName": "CFO_Approval", "approvalProcessDesc": "This is for CFO Approval.", "approvalProcessStepNumber": "1"}, "approvalStep2": {"stepCriteria": "All records should enter this step."}, "approvalStep3": {"approverAction": "Automatically assign using the user field selected earlier. (Manager)", "requestApproval": false}}, "active": true, "hasCreatedSteps": true}], "opportunity": {}, "opportunityList": [], "contact": {}, "contactList": [], "emailAlert": {}}}]