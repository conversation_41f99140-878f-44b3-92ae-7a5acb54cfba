[{"prompt": "Define a new classic email template with Text type called 'Appointment for Unauthenticated User using Appointment Types For third party.' in the 'Unfiled Public Classic Email Templates' folder, using subject 'Appointment for Unauthenticated User using Appointment Types For third party. Notification' and body 'Email template for confirmation of an appointment...'. Then edit the created email template, and then select 'Contact Fields' as the field type, choose 'Fax Opt Out' as the field, and save the updated template. Open Sales from the App Launcher. In Contacts, edit the 'Express Logistics and Transport' record and create a new Contact with salutation '<PERSON>.', first name '<PERSON>', and last name '<PERSON><PERSON>'. Save the contact.", "result": {"classicEmailTemplate": {}, "classicEmailTemplateList": [{"templateType": "Custom", "folder": "Unfiled Public Classic Email Templates", "availableForUse": false, "emailTemplateName": "Appointment for Unauthenticated User using Appointment Types For third party.", "templateUniqueName": "Appointment for Unauthenticated User using Appointment Types For third party.", "encoding": "Unicode (UTF-8)", "description": "", "subject": "Appointment for Unauthenticated User using Appointment Types For third party. Notification", "body": "Email template for confirmation of an appointment..."}], "fieldUpdate": {}, "fieldUpdateList": [], "approvalProcesses": {}, "approvalProcessesList": [], "opportunity": {}, "opportunityList": [], "contact": {}, "contactList": [{"account": "Express Logistics and Transport", "salutation": "Prof.", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}], "emailAlert": {}}}]