/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-tree-container,
.slds-tree_container {
  min-width: 7.5rem;
  max-width: 25rem;
}
.slds-tree-container > .slds-text-heading_label,
.slds-tree_container > .slds-text-heading_label {
  margin-bottom: 0.5rem;
}
.slds-tree__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0.375rem 0 0.375rem 1rem;
}
.slds-tree__item.slds-is-hovered,
.slds-tree__item:hover {
  background: #f3f2f2;
  cursor: pointer;
}
.slds-tree__item .slds-is-disabled {
  visibility: hidden;
}
.slds-tree [role="treeitem"]:focus {
  outline: 0;
}
.slds-tree [role="treeitem"]:focus > .slds-tree__item {
  background: #f3f2f2;
  cursor: pointer;
  text-decoration: underline;
}

.slds-tree [role="treeitem"][aria-selected="true"] > .slds-tree__item {
  background: rgba(21, 137, 238, 0.1);
  -webkit-box-shadow: #1589ee 4px 0 0 inset;
  box-shadow: #1589ee 4px 0 0 inset;
}
[dir="rtl"] .slds-tree [role="treeitem"][aria-selected="true"] > .slds-tree__item {
  -webkit-box-shadow: #1589ee -4px 0 0 inset;
  box-shadow: #1589ee -4px 0 0 inset;
}

.slds-tree [role="treeitem"] > [role="group"] {
  display: none;
}

.slds-tree [role="treeitem"][aria-expanded="true"] > [role="group"] {
  display: block;
}

[aria-expanded="false"] > .slds-tree__item .slds-button__icon {
  -webkit-transition: 0.2s -webkit-transform ease-in-out;
  transition: 0.2s -webkit-transform ease-in-out;
  transition: 0.2s transform ease-in-out;
  transition: 0.2s transform ease-in-out, 0.2s -webkit-transform ease-in-out;
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg);
}

[aria-expanded="true"] > .slds-tree__item .slds-button__icon {
  -webkit-transition: 0.2s -webkit-transform ease-in-out;
  transition: 0.2s -webkit-transform ease-in-out;
  transition: 0.2s transform ease-in-out;
  transition: 0.2s transform ease-in-out, 0.2s -webkit-transform ease-in-out;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}
[dir="rtl"] [aria-expanded="true"] > .slds-tree__item .slds-button__icon {
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
}

[aria-level="1"] > .slds-tree__item {
  padding-left: 1rem;
}

[aria-level="2"] > .slds-tree__item {
  padding-left: 2rem;
}

[aria-level="3"] > .slds-tree__item {
  padding-left: 3rem;
}

[aria-level="4"] > .slds-tree__item {
  padding-left: 4rem;
}

[aria-level="5"] > .slds-tree__item {
  padding-left: 5rem;
}

[aria-level="6"] > .slds-tree__item {
  padding-left: 6rem;
}

[aria-level="7"] > .slds-tree__item {
  padding-left: 7rem;
}

[aria-level="8"] > .slds-tree__item {
  padding-left: 8rem;
}

[aria-level="9"] > .slds-tree__item {
  padding-left: 9rem;
}

[aria-level="10"] > .slds-tree__item {
  padding-left: 10rem;
}

[aria-level="11"] > .slds-tree__item {
  padding-left: 11rem;
}

[aria-level="12"] > .slds-tree__item {
  padding-left: 12rem;
}

[aria-level="13"] > .slds-tree__item {
  padding-left: 13rem;
}

[aria-level="14"] > .slds-tree__item {
  padding-left: 14rem;
}

[aria-level="15"] > .slds-tree__item {
  padding-left: 15rem;
}

[aria-level="16"] > .slds-tree__item {
  padding-left: 16rem;
}

[aria-level="17"] > .slds-tree__item {
  padding-left: 17rem;
}

[aria-level="18"] > .slds-tree__item {
  padding-left: 18rem;
}

[aria-level="19"] > .slds-tree__item {
  padding-left: 19rem;
}

[aria-level="20"] > .slds-tree__item {
  padding-left: 20rem;
}
.slds-tree__item-label {
  display: block;
}
.slds-tree__item-meta {
  display: block;
  color: #3e3e3c;
}

.slds-tree__item .slds-button {
  -ms-flex-item-align: start;
  align-self: flex-start;
  margin-top: 0.125rem;
}

.slds-tree__item .slds-pill {
  margin-left: 0.75rem;
}
.slds-tree__group-header {
  font-size: 0.875rem;
  font-weight: 700;
}
