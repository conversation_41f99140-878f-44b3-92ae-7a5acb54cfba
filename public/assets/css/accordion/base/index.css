/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-accordion {
  position: relative;
}
.slds-accordion__list-item {
  border-top: 1px solid #dddbda;
}
.slds-accordion__list-item:first-child {
  border-top: 0;
}
.slds-accordion__summary {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.slds-accordion__summary-heading {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  min-width: 0;
  font-size: 1rem;
  line-height: 1.25;
}
.slds-accordion__summary-heading .slds-button:focus {
  text-decoration: underline;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.slds-accordion__summary-action {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-width: 0;
}
.slds-accordion__summary-action-icon {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
[dir="rtl"] .slds-accordion__summary-action-icon {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}
@media (min-width: 64em) {
  .slds-accordion__summary-content {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.slds-accordion__section {
  padding: 0.75rem;
}
.slds-accordion__content {
  overflow: hidden;
  visibility: hidden;
  opacity: 0;
  height: 0;
}
.slds-is-open > .slds-accordion__summary {
  margin-bottom: 0.75rem;
}
.slds-is-open > .slds-accordion__summary .slds-accordion__summary-action-icon {
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg);
}
.slds-is-open > .slds-accordion__content {
  overflow: visible;
  visibility: visible;
  opacity: 1;
  height: auto;
}
