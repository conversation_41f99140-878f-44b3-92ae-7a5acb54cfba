/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-tabs_default {
  display: block;
  width: 100%;
  background-color: white;
}
.slds-tabs_default .slds-tabs__item_overflow {
  overflow: visible;
}
.slds-tabs_default__nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  border-bottom: 1px solid #dddbda;
}
.slds-tabs_default__item {
  color: #3e3e3c;
  position: relative;
  padding: 0 0.75rem;
  margin-bottom: -1px;
}
.slds-tabs_default__item:after {
  display: block;
  content: "";
  bottom: 0;
  left: 0;
  right: 0;
  height: 0;
  position: absolute;
}
.slds-tabs_default__item.slds-active,
.slds-tabs_default__item.slds-is-active {
  color: #080707;
}
.slds-tabs_default__item.slds-active:after,
.slds-tabs_default__item.slds-is-active:after {
  background-color: #1589ee;
  height: 0.1875rem;
}
.slds-tabs_default__item.slds-active .slds-tabs_default__link,
.slds-tabs_default__item.slds-is-active .slds-tabs_default__link {
  font-weight: 700;
}
.slds-tabs_default__item.slds-active .slds-tabs_default__link:hover,
.slds-tabs_default__item.slds-is-active .slds-tabs_default__link:hover {
  color: currentColor;
}
.slds-tabs_default__item:hover:after {
  height: 0.125rem;
  background-color: #007add;
  -webkit-transition: height 300ms;
  transition: height 300ms;
}
.slds-tabs_default__item:focus,
.slds-tabs_default__item.slds-has-focus {
  outline: 0;
}
.slds-tabs_default__item:focus:after,
.slds-tabs_default__item.slds-has-focus:after {
  height: 3px;
  background-color: #1589ee;
}
.slds-tabs_default__item .slds-tabs_default__link:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.slds-tabs_default__item.slds-has-notification {
  background: #f3f2f2;
}
.slds-tabs_default__item.slds-has-notification:after {
  background-color: #dddbda;
}
.slds-tabs_default__item.slds-has-notification:hover:after {
  background-color: #0070d2;
}
.slds-tabs_default__item.slds-has-notification .slds-indicator_unread {
  display: inline-block;
  height: 0.375rem;
  width: 0.375rem;
  position: relative;
  top: auto;
  left: auto;
}
.slds-tabs_default__item.slds-is-unsaved .slds-indicator_unread {
  margin-left: -0.35rem;
}
.slds-tabs_default__item.slds-has-notification .slds-indicator_unsaved {
  top: -0.25rem;
}
.slds-tabs_default__link {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  text-decoration: none;
  cursor: pointer;
  height: 2.5rem;
  line-height: 2.5rem;
  color: currentColor;
  border: 0;
  text-transform: inherit;
  z-index: 1;
}
.slds-tabs_default__link:focus {
  outline: 0;
}
.slds-tabs_default__link:hover,
.slds-tabs_default__link:focus {
  text-decoration: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: currentColor;
}
.slds-tabs_default__link[tabindex="0"]:focus {
  text-decoration: underline;
}
.slds-tabs_default__overflow-button {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 2.5rem;
  line-height: 2.5rem;
}
.slds-tabs_default__overflow-button .slds-button {
  line-height: inherit;
  color: #3e3e3c;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.slds-tabs_default__overflow-button .slds-button:focus {
  text-decoration: underline;
}
.slds-tabs_default__overflow-button.slds-has-error .slds-button,
.slds-tabs_default__overflow-button.slds-has-success .slds-button {
  color: white;
}
.slds-tabs_default__content {
  position: relative;
  padding: 1rem 0;
}
.slds-tabs_medium .slds-tabs_default__item {
  font-size: 1rem;
}
.slds-tabs_large .slds-tabs_default__item {
  font-size: 1.25rem;
}
.slds-tabs__left-icon {
  margin-right: 0.5rem;
}
.slds-tabs__left-icon:empty {
  margin-right: 0;
}
.slds-tabs__right-icon {
  margin-left: 0.5rem;
}
