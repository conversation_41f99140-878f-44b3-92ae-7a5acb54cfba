/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-button-group,
.slds-button-group-list {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.slds-button-group .slds-button,
.slds-button-group-list .slds-button {
  border-radius: 0;
  border-width: 1px;
}
.slds-button-group .slds-button:focus,
.slds-button-group-list .slds-button:focus {
  z-index: 1;
}

.slds-button-group .slds-button + .slds-button,
.slds-button-group .slds-button + .slds-button_last .slds-button,
.slds-button-group-list li + li .slds-button {
  margin-left: -1px;
}

.slds-button-group .slds-button_brand + .slds-button_last .slds-button_icon-brand,
.slds-button-group-list li:last-child .slds-button_icon-brand {
  -webkit-box-shadow: inset 1px 0 0 white;
  box-shadow: inset 1px 0 0 white;
}

.slds-button-group-list li:first-child .slds-button,
.slds-button-group .slds-button:first-child {
  border-radius: 0.25rem 0 0 0.25rem;
}
.slds-button-group .slds-button:last-child,
.slds-button-group-list li:last-child .slds-button,
.slds-button-group .slds-button_last .slds-button,
.slds-button-group .slds-button.slds-button_last,
.slds-button-group .slds-button_last .slds-button:only-child,
.slds-button-group .slds-button.slds-button_last {
  border-radius: 0 0.25rem 0.25rem 0;
}

.slds-button-group .slds-button:only-child,
.slds-button-group-list li:only-child .slds-button {
  border-radius: 0.25rem;
}
.slds-button.slds-button_first.slds-button_first {
  border-right: 0;
  border-radius: 0.25rem 0 0 0.25rem;
}
.slds-button.slds-button_middle.slds-button_middle {
  border-radius: 0;
  margin-left: -1px;
}
.slds-button.slds-button_last.slds-button_last {
  border-radius: 0 0.25rem 0.25rem 0;
  margin-left: -1px;
}

.slds-button-group + .slds-button-group,
.slds-button-group + .slds-button-group-list,
.slds-button-group + .slds-button,
.slds-button-group-list + .slds-button-group-list,
.slds-button-group-list + .slds-button-group,
.slds-button-group-list + .slds-button {
  margin-left: 0.25rem;
}
