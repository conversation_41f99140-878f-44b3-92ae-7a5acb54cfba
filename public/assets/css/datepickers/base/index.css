/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-datepicker {
  padding: 0;
  font-size: 0.75rem;
}
.slds-datepicker thead > tr > th,
.slds-datepicker tbody > tr > td {
  text-align: center;
}
.slds-datepicker thead > tr > th {
  padding: 0.5rem;
  font-weight: 400;
  color: #3e3e3c;
}
.slds-datepicker tbody > tr > td {
  padding: 0.25rem;
  font-size: 0.75rem;
}
.slds-datepicker tbody > tr > td > .slds-day {
  width: 2rem;
  height: 2rem;
  display: block;
  position: relative;
  min-width: 2rem;
  line-height: 2rem;
  border-radius: 50%;
  margin: auto;
}
.slds-datepicker tbody > tr > td:hover:not(.slds-disabled-text) > .slds-day,
.slds-datepicker tbody > tr > td:focus:not(.slds-disabled-text) > .slds-day,
.slds-datepicker tbody > tr > td.slds-is-today > .slds-day {
  cursor: pointer;
}
.slds-datepicker tbody > tr > td.slds-is-today > .slds-day {
  background-color: #ecebea;
}
.slds-datepicker tbody > tr > td:focus {
  outline: 0;
}
.slds-datepicker tbody > tr > td:focus > .slds-day {
  -webkit-box-shadow: #0070d2 0 0 0 1px inset;
  box-shadow: #0070d2 0 0 0 1px inset;
}
.slds-datepicker tbody > tr > td.slds-is-selected > .slds-day {
  background: #0070d2;
  color: white;
}
.slds-datepicker tbody > tr > td.slds-is-selected:focus > .slds-day {
  background: #005fb2;
  -webkit-box-shadow: #005fb2 0 0 3px;
  box-shadow: #005fb2 0 0 3px;
  color: white;
}
.slds-datepicker__filter {
  padding: 0.25rem;
}
.slds-datepicker__filter_month {
  padding: 0 0.25rem 0 0;
}
.slds-datepicker__month {
  font-size: 0.75rem;
}

.slds-has-error .slds-datepicker__filter .slds-select {
  border: 1px solid #dddbda;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.slds-day_adjacent-month {
  color: #706e6b;
}

.slds-table .slds-datepicker .slds-datepicker__month tbody > tr:hover > td {
  background-color: unset;
}
