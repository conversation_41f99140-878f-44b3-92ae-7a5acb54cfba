/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-nubbin_top:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  top: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_top:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  top: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_top:after {
  -webkit-box-shadow: -1px -1px 0 0 rgba(0, 0, 0, 0.16);
  box-shadow: -1px -1px 0 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_top-left:before,
.slds-nubbin_top-left-corner:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  top: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_top-left:after,
.slds-nubbin_top-left-corner:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  top: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_top-left:after,
.slds-nubbin_top-left-corner:after {
  -webkit-box-shadow: -1px -1px 0 0 rgba(0, 0, 0, 0.16);
  box-shadow: -1px -1px 0 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_top-left:before,
.slds-nubbin_top-left:after,
.slds-nubbin_top-left-corner:before,
.slds-nubbin_top-left-corner:after {
  /*! @noflip */
  left: 1.5rem;
  top: -0.5rem;
}
.slds-nubbin_top-right:before,
.slds-nubbin_top-right-corner:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  top: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_top-right:after,
.slds-nubbin_top-right-corner:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  top: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_top-right:after,
.slds-nubbin_top-right-corner:after {
  -webkit-box-shadow: -1px -1px 0 0 rgba(0, 0, 0, 0.16);
  box-shadow: -1px -1px 0 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_top-right:before,
.slds-nubbin_top-right:after,
.slds-nubbin_top-right-corner:before,
.slds-nubbin_top-right-corner:after {
  /*! @noflip */
  left: auto;
  /*! @noflip */
  right: 1.5rem;
  top: -0.5rem;
  /*! @noflip */
  margin-right: -0.5rem;
}
.slds-nubbin_bottom:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  bottom: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_bottom:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  bottom: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_bottom:after {
  -webkit-box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.16);
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_bottom-left:before,
.slds-nubbin_bottom-left-corner:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  bottom: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_bottom-left:after,
.slds-nubbin_bottom-left-corner:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  bottom: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_bottom-left:after,
.slds-nubbin_bottom-left-corner:after {
  -webkit-box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.16);
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_bottom-left:before,
.slds-nubbin_bottom-left:after,
.slds-nubbin_bottom-left-corner:before,
.slds-nubbin_bottom-left-corner:after {
  /*! @noflip */
  left: 1.5rem;
  top: 100%;
  margin-top: -0.5rem;
}
.slds-nubbin_bottom-right:before,
.slds-nubbin_bottom-right-corner:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  bottom: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_bottom-right:after,
.slds-nubbin_bottom-right-corner:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  left: 50%;
  bottom: -0.5rem;
  margin-left: -0.5rem;
}
.slds-nubbin_bottom-right:after,
.slds-nubbin_bottom-right-corner:after {
  -webkit-box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.16);
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_bottom-right:before,
.slds-nubbin_bottom-right:after,
.slds-nubbin_bottom-right-corner:before,
.slds-nubbin_bottom-right-corner:after {
  /*! @noflip */
  left: auto;
  /*! @noflip */
  right: 1.5rem;
  top: 100%;
  margin-top: -0.5rem;
  /*! @noflip */
  margin-right: -0.5rem;
}
.slds-nubbin_left:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  left: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_left:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  left: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_left:after {
  -webkit-box-shadow: -1px 1px 2px 0 rgba(0, 0, 0, 0.16);
  box-shadow: -1px 1px 2px 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_left-top:before,
.slds-nubbin_left-top-corner:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  left: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_left-top:after,
.slds-nubbin_left-top-corner:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  left: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_left-top:after,
.slds-nubbin_left-top-corner:after {
  -webkit-box-shadow: -1px 1px 2px 0 rgba(0, 0, 0, 0.16);
  box-shadow: -1px 1px 2px 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_left-top:before,
.slds-nubbin_left-top:after,
.slds-nubbin_left-top-corner:before,
.slds-nubbin_left-top-corner:after {
  top: 1.5rem;
}
.slds-nubbin_left-bottom:before,
.slds-nubbin_left-bottom-corner:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  left: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_left-bottom:after,
.slds-nubbin_left-bottom-corner:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  left: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_left-bottom:before,
.slds-nubbin_left-bottom-corner:before {
  margin-bottom: -1px;
}
.slds-nubbin_left-bottom:after,
.slds-nubbin_left-bottom-corner:after {
  -webkit-box-shadow: -1px 2px 3px 0 rgba(0, 0, 0, 0.16);
  box-shadow: -1px 2px 3px 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_left-bottom:before,
.slds-nubbin_left-bottom:after,
.slds-nubbin_left-bottom-corner:before,
.slds-nubbin_left-bottom-corner:after {
  top: auto;
  bottom: 1rem;
}
.slds-nubbin_right:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  right: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_right:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  right: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_right:after {
  -webkit-box-shadow: 1px -1px 2px 0 rgba(0, 0, 0, 0.16);
  box-shadow: 1px -1px 2px 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_right-top:before,
.slds-nubbin_right-top-corner:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  right: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_right-top:after,
.slds-nubbin_right-top-corner:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  right: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_right-top:after,
.slds-nubbin_right-top-corner:after {
  -webkit-box-shadow: 1px -1px 2px 0 rgba(0, 0, 0, 0.16);
  box-shadow: 1px -1px 2px 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_right-top:before,
.slds-nubbin_right-top:after,
.slds-nubbin_right-top-corner:before,
.slds-nubbin_right-top-corner:after {
  top: 1.5rem;
}
.slds-nubbin_right-bottom:before,
.slds-nubbin_right-bottom-corner:before {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  right: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_right-bottom:after,
.slds-nubbin_right-bottom-corner:after {
  width: 1rem;
  height: 1rem;
  position: absolute;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  content: "";
  background-color: inherit;
  top: 50%;
  right: -0.5rem;
  margin-top: -0.5rem;
}
.slds-nubbin_right-bottom:before,
.slds-nubbin_right-bottom-corner:before {
  margin-bottom: -1px;
}
.slds-nubbin_right-bottom:after,
.slds-nubbin_right-bottom-corner:after {
  -webkit-box-shadow: 2px -1px 3px 0 rgba(0, 0, 0, 0.16);
  box-shadow: 2px -1px 3px 0 rgba(0, 0, 0, 0.16);
  z-index: -1;
}
.slds-nubbin_right-bottom:before,
.slds-nubbin_right-bottom:after,
.slds-nubbin_right-bottom-corner:before,
.slds-nubbin_right-bottom-corner:after {
  top: auto;
  bottom: 1rem;
}
.slds-nubbin_top-left-corner {
  border-radius: 0.125rem 0.25rem 0.25rem 0.25rem;
}
.slds-nubbin_top-left-corner:before,
.slds-nubbin_top-left-corner:after {
  left: 0.75rem;
}
.slds-nubbin_bottom-left-corner {
  border-radius: 0.25rem 0.25rem 0.25rem 0.125rem;
}
.slds-nubbin_bottom-left-corner:before,
.slds-nubbin_bottom-left-corner:after {
  left: 0.75rem;
}
.slds-nubbin_top-right-corner {
  border-radius: 0.25rem 0.125rem 0.25rem 0.25rem;
}
.slds-nubbin_top-right-corner:before,
.slds-nubbin_top-right-corner:after {
  right: 0.75rem;
}
.slds-nubbin_bottom-right-corner {
  border-radius: 0.25rem 0.25rem 0.125rem 0.25rem;
}
.slds-nubbin_bottom-right-corner:before,
.slds-nubbin_bottom-right-corner:after {
  right: 0.75rem;
}
.slds-nubbin_left-top-corner {
  border-radius: 0.125rem 0.25rem 0.25rem 0.25rem;
}
.slds-nubbin_left-top-corner:before,
.slds-nubbin_left-top-corner:after {
  top: 0.75rem;
}
.slds-nubbin_right-top-corner {
  border-radius: 0.25rem 0.125rem 0.25rem 0.25rem;
}
.slds-nubbin_right-top-corner:before,
.slds-nubbin_right-top-corner:after {
  top: 0.75rem;
}
.slds-nubbin_left-bottom-corner {
  border-radius: 0.25rem 0.25rem 0.25rem 0.125rem;
}
.slds-nubbin_left-bottom-corner:before,
.slds-nubbin_left-bottom-corner:after {
  bottom: 0.25rem;
}
.slds-nubbin_right-bottom-corner {
  border-radius: 0.25rem 0.25rem 0.125rem 0.25rem;
}
.slds-nubbin_right-bottom-corner:before,
.slds-nubbin_right-bottom-corner:after {
  bottom: 0.25rem;
}
