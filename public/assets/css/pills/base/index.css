/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-pill {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  line-height: 1.5;
  max-width: 100%;
  padding: 0.125rem;
  border: 1px solid #dddbda;
  border-radius: 0.25rem;
  background-color: white;
  position: relative;
  min-height: 1.625rem;
}
.slds-pill + .slds-pill {
  margin-left: 0.125rem;
}
.slds-pill:hover {
  background-color: #f4f6f9;
}
.slds-pill:focus {
  outline: 0;
  border-radius: 0.25rem;
  border-color: #1589ee;
  -webkit-box-shadow: 0 0 3px #0070d2;
  box-shadow: 0 0 3px #0070d2;
}
.slds-pill a {
  text-decoration: none;
}
.slds-pill_bare {
  background-color: transparent;
  border: 0;
}
.slds-pill_bare:hover {
  background-color: transparent;
}
.slds-pill__container,
.slds-pill-container,
.slds-pill_container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-height: calc(1.875rem + 2px);
  padding: 0.125rem;
  border: 1px solid #dddbda;
  border-radius: 0.25rem;
  background-color: white;
}
.slds-pill__container .slds-listbox_inline,
.slds-pill-container .slds-listbox_inline,
.slds-pill_container .slds-listbox_inline {
  margin-left: 0;
  margin-right: 0;
}
.slds-pill__container_bare,
.slds-pill_container_bare {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0.125rem;
  border: 0;
  border-radius: 0;
  background-color: transparent;
}
.slds-pill__label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.slds-pill__label:focus {
  outline: 0;
  border-radius: 0.25rem;
  -webkit-box-shadow: 0 0 3px #0070d2;
  box-shadow: 0 0 3px #0070d2;
}
.slds-pill__icon,
.slds-pill__icon_container {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.25rem;
}
.slds-pill__icon .slds-icon,
.slds-pill__icon .slds-avatar,
.slds-pill__icon_container .slds-icon,
.slds-pill__icon_container .slds-avatar {
  width: 1.25rem;
  height: 1.25rem;
  display: block;
  font-size: 0.625rem;
}
.slds-pill__icon ~ .slds-pill__action,
.slds-pill__icon_container ~ .slds-pill__action {
  padding-left: calc(1.25rem + 0.25rem + 2px);
}
.slds-pill__remove {
  width: 1rem;
  height: 1rem;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-left: 0.25rem;
  border-radius: 0.125rem;
}
.slds-pill__remove svg {
  width: 0.875rem;
  height: 0.875rem;
}
.slds-pill_link {
  border: 0;
  padding: 0;
}
.slds-pill_link .slds-pill__icon_container {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 0.125rem;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}
.slds-pill_link .slds-pill__remove {
  position: absolute;
  top: 50%;
  right: 0.125rem;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}
.slds-pill__action {
  padding: 0.125rem;
  padding-right: calc(1rem + 0.25rem + 2px);
  border: 1px solid #dddbda;
  border-radius: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}
.slds-pill__action:focus {
  outline: 0;
  border-color: #1589ee;
  -webkit-box-shadow: 0 0 3px #0070d2;
  box-shadow: 0 0 3px #0070d2;
}
.slds-has-error {
  border-color: #c23934;
}
.slds-has-error .slds-pill__label {
  font-weight: 700;
  color: #c23934;
}
.slds-has-error .slds-pill__action {
  border-color: #c23934;
}
