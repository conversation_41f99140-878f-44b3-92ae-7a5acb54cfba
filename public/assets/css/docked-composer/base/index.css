/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-docked_container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
  position: fixed;
  bottom: 0;
  right: 0;
  padding: 0 1.5rem;
  height: 2.5rem;
}
.slds-docked-composer {
  position: relative;
  border-radius: 0.25rem 0.25rem 0 0;
  width: 480px;
  height: 2.5rem;
  float: left;
  -webkit-box-shadow: 0 -2px 2px 0 rgba(0, 0, 0, 0.16);
  box-shadow: 0 -2px 2px 0 rgba(0, 0, 0, 0.16);
  border: 1px solid #dddbda;
  border-bottom: none;
  background-color: white;
}
.slds-docked-composer.slds-has-focus {
  -webkit-box-shadow: 0 0 4px 2px #0070d2;
  box-shadow: 0 0 4px 2px #0070d2;
}
.slds-docked-composer.slds-is-open {
  height: 480px;
}
.slds-docked-composer.slds-is-closed {
  height: 2.5rem;
}
.slds-docked-composer.slds-is-closed .slds-docked-composer__body,
.slds-docked-composer.slds-is-closed .slds-docked-composer__footer {
  display: none;
}
.slds-docked-composer + .slds-docked-composer {
  margin-left: 1.5rem;
}
.slds-docked-composer__header {
  background: white;
  border-bottom: 2px solid #1589ee;
  border-radius: 0.25rem 0.25rem 0 0;
  padding: 0.5rem 0.75rem;
}
.slds-docked-composer__actions .slds-button {
  margin-left: 0.75rem;
}
.slds-docked-composer__body {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  overflow: auto;
}
.slds-docked-composer__body_form {
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  padding: 1rem;
}
.slds-docked-composer__lead {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background: white;
  padding: 1rem 0.5rem;
}
.slds-docked-composer__toolbar {
  background: #f3f2f2;
  padding: 0.25rem 0.5rem;
  max-height: 60px;
  overflow-y: auto;
  border-top: 1px solid #dddbda;
  border-bottom: 1px solid #dddbda;
}
.slds-docked-composer__input {
  background: white;
  padding: 1rem;
  min-height: 6rem;
  resize: none;
  line-height: 1.5;
  overflow: hidden;
  overflow-y: auto;
}
.slds-docked-composer__footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background: #f3f2f2;
  padding: 0.75rem 0.5rem;
  border-top: 1px solid #dddbda;
}
.slds-docked-composer_overflow {
  width: auto;
}
.slds-docked-composer_overflow__button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background: white;
  padding: 0 0.75rem;
  height: inherit;
  white-space: nowrap;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.slds-docked-composer-modal .slds-modal__content {
  border-radius: 0.25rem;
}
.slds-docked-composer-modal .slds-docked-composer {
  width: 100%;
  height: 100%;
  border: 0;
}
