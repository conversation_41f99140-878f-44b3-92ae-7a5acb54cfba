/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-checkbox_toggle {
  width: 100%;
}
.slds-checkbox_toggle .slds-checkbox_faux {
  display: block;
  position: relative;
  width: 3rem;
  height: 1.5rem;
  border: 1px solid #b0adab;
  padding: 0.125rem;
  background-color: #b0adab;
  border-radius: 15rem;
  -webkit-transition: background-color 0.2s cubic-bezier(0.75, 0, 0.08, 1);
  transition: background-color 0.2s cubic-bezier(0.75, 0, 0.08, 1);
}
.slds-checkbox_toggle .slds-checkbox_faux:hover,
.slds-checkbox_toggle .slds-checkbox_faux:focus {
  cursor: pointer;
  background-color: #969492;
}
.slds-checkbox_toggle .slds-checkbox_faux:after {
  content: "";
  position: absolute;
  top: 1px;
  left: 1px;
  width: 1.25rem;
  height: 1.25rem;
  background-color: white;
  border-radius: 15rem;
}
.slds-checkbox_toggle [type="checkbox"][type="checkbox"] {
  width: 1px;
  height: 1px;
  border: 0;
  clip: rect(0 0 0 0);
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
}
.slds-checkbox_toggle [type="checkbox"] + .slds-checkbox_faux_container {
  font-size: 0.625rem;
  color: #3e3e3c;
}
.slds-checkbox_toggle [type="checkbox"] + .slds-checkbox_faux_container .slds-checkbox_off {
  display: block;
}
.slds-checkbox_toggle [type="checkbox"] + .slds-checkbox_faux_container .slds-checkbox_on {
  display: none;
}
.slds-checkbox_toggle [type="checkbox"]:focus + .slds-checkbox_faux,
.slds-checkbox_toggle [type="checkbox"]:focus ~ .slds-checkbox_faux,
.slds-checkbox_toggle [type="checkbox"]:focus + .slds-checkbox_faux_container .slds-checkbox_faux {
  background-color: #969492;
  border-color: #1589ee;
  -webkit-box-shadow: 0 0 3px #0070d2;
  box-shadow: 0 0 3px #0070d2;
}
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux_container .slds-checkbox_off {
  display: none;
}
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux_container .slds-checkbox_on {
  display: block;
}
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux,
.slds-checkbox_toggle [type="checkbox"]:checked ~ .slds-checkbox_faux,
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux_container .slds-checkbox_faux {
  border-color: #0070d2;
  background-color: #0070d2;
}
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux:hover,
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux:focus,
.slds-checkbox_toggle [type="checkbox"]:checked ~ .slds-checkbox_faux:hover,
.slds-checkbox_toggle [type="checkbox"]:checked ~ .slds-checkbox_faux:focus,
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux_container .slds-checkbox_faux:hover,
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux_container .slds-checkbox_faux:focus {
  background-color: #005fb2;
}
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux:before,
.slds-checkbox_toggle [type="checkbox"]:checked ~ .slds-checkbox_faux:before,
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux_container .slds-checkbox_faux:before {
  content: "";
  position: absolute;
  top: 1px;
  right: 1px;
  width: 1.25rem;
  height: 1.25rem;
  background-color: white;
  border-radius: 15rem;
  -webkit-transition: -webkit-transform 0.2s cubic-bezier(0.75, 0, 0.08, 1);
  transition: -webkit-transform 0.2s cubic-bezier(0.75, 0, 0.08, 1);
  transition: transform 0.2s cubic-bezier(0.75, 0, 0.08, 1);
  transition: transform 0.2s cubic-bezier(0.75, 0, 0.08, 1), -webkit-transform 0.2s cubic-bezier(0.75, 0, 0.08, 1);
}
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux:after,
.slds-checkbox_toggle [type="checkbox"]:checked ~ .slds-checkbox_faux:after,
.slds-checkbox_toggle [type="checkbox"]:checked + .slds-checkbox_faux_container .slds-checkbox_faux:after {
  content: " ";
  position: absolute;
  top: 0.25rem;
  left: 0.6rem;
  height: 0.7rem;
  width: 0.45rem;
  border-bottom: 2px solid white;
  /*! @noflip */
  border-right: 2px solid white;
  border-radius: 0;
  background-color: transparent;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.slds-checkbox_toggle [type="checkbox"]:checked:focus + .slds-checkbox_faux,
.slds-checkbox_toggle [type="checkbox"]:checked:focus ~ .slds-checkbox_faux,
.slds-checkbox_toggle [type="checkbox"]:checked:focus + .slds-checkbox_faux_container .slds-checkbox_faux {
  background-color: #005fb2;
}
.slds-checkbox_toggle [type="checkbox"][disabled] + .slds-checkbox_faux,
.slds-checkbox_toggle [type="checkbox"][disabled] ~ .slds-checkbox_faux,
.slds-checkbox_toggle [type="checkbox"][disabled] + .slds-checkbox_faux_container .slds-checkbox_faux {
  background-color: #b0adab;
  pointer-events: none;
}
.slds-checkbox_toggle [type="checkbox"][disabled] + .slds-checkbox_faux:after,
.slds-checkbox_toggle [type="checkbox"][disabled] ~ .slds-checkbox_faux:after,
.slds-checkbox_toggle [type="checkbox"][disabled] + .slds-checkbox_faux_container .slds-checkbox_faux:after {
  background-color: #dddbda;
}
.slds-checkbox_toggle [type="checkbox"][disabled] + .slds-checkbox_faux:hover,
.slds-checkbox_toggle [type="checkbox"][disabled] + .slds-checkbox_faux:focus,
.slds-checkbox_toggle [type="checkbox"][disabled] ~ .slds-checkbox_faux:hover,
.slds-checkbox_toggle [type="checkbox"][disabled] ~ .slds-checkbox_faux:focus,
.slds-checkbox_toggle [type="checkbox"][disabled] + .slds-checkbox_faux_container .slds-checkbox_faux:hover,
.slds-checkbox_toggle [type="checkbox"][disabled] + .slds-checkbox_faux_container .slds-checkbox_faux:focus {
  background-color: #b0adab;
  cursor: default;
}
.slds-checkbox_toggle
  [type="checkbox"][disabled]:checked
  + .slds-checkbox_faux_container
  .slds-checkbox_faux:before
  [type="checkbox"][disabled]:checked
  + .slds-checkbox--faux_container
  .slds-checkbox--faux:before {
  background-color: #dddbda;
}
.slds-checkbox_toggle [type="checkbox"][disabled]:checked + .slds-checkbox_faux_container .slds-checkbox_faux:after {
  background-color: transparent;
}
