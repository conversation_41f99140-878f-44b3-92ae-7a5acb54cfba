/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-mobile-lookup__listbox_container {
  width: 100%;
  margin: 0.25rem 0;
  background: white;
}

.slds-mobile-lookup__listbox-option {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-right: -0.75rem;
  padding: 0.5rem 1.5rem 0.5rem 0;
  min-height: 2.75rem;
  line-height: 1.375;
  border-bottom: 1px solid #f1f2f3;
}
.slds-mobile-lookup__listbox-option mark {
  font-weight: 700;
  background-color: transparent;
  color: inherit;
}

.slds-mobile-lookup__listbox-option_heading {
  font-size: 1rem;
  font-weight: 700;
  border-bottom: 0;
}

.slds-mobile-lookup__listbox_text {
  display: block;
  color: #080707;
  font-size: 1rem;
}

.slds-mobile-lookup__listbox_meta {
  display: block;
  color: #706e6b;
  font-size: 0.875rem;
}

.slds-mobile-lookup__listbox_icon_container {
  margin-left: auto;
  color: #0070d2;
}

.slds-mobile-lookup__listbox_trigger {
  line-height: 2.625rem;
  -webkit-box-pack: unset;
  -ms-flex-pack: unset;
  justify-content: unset;
  font-size: 1rem;
  font-weight: 700;
}
.slds-mobile-lookup__listbox_trigger .slds-icon {
  margin-left: 0.75rem;
  fill: currentColor;
}
.slds-mobile-lookup__listbox_trigger + .slds-mobile-lookup__listbox {
  margin-top: -0.25rem;
}

.slds-mobile-lookup__listbox_loader {
  position: relative;
  height: 2.75rem;
}
