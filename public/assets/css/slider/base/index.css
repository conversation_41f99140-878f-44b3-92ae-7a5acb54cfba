/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-slider {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
}
.slds-slider__range {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  background: transparent;
  border-radius: 0.125rem;
}
.slds-slider__range:focus {
  outline: 0;
}
.slds-slider__range::-webkit-slider-thumb {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: #0070d2;
  border: 0;
  -webkit-box-shadow: rgba(0, 0, 0, 0.16) 0 2px 3px;
  box-shadow: rgba(0, 0, 0, 0.16) 0 2px 3px;
  cursor: pointer;
  -webkit-transition: background 0.15s ease-in-out;
  transition: background 0.15s ease-in-out;
  margin-top: calc(((1rem / 2) - (4px / 2)) * -1);
}
.slds-slider__range::-webkit-slider-thumb:hover {
  background-color: #005fb2;
}
.slds-slider__range::-webkit-slider-runnable-track {
  width: 100%;
  height: 4px;
  cursor: pointer;
  background: #ecebea;
  border-radius: 0.125rem;
}
.slds-slider__range::-moz-range-thumb {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: #0070d2;
  border: 0;
  box-shadow: rgba(0, 0, 0, 0.16) 0 2px 3px;
  cursor: pointer;
  -moz-transition: background 0.15s ease-in-out;
  transition: background 0.15s ease-in-out;
}
.slds-slider__range::-moz-range-thumb:hover {
  background-color: #005fb2;
}
.slds-slider__range::-moz-range-track {
  width: 100%;
  height: 4px;
  cursor: pointer;
  background: #ecebea;
  border-radius: 0.125rem;
}
.slds-slider__range::-ms-track {
  width: 100%;
  height: 4px;
  cursor: pointer;
  background: #ecebea;
  border-radius: 0.125rem;
  border-color: transparent;
  color: transparent;
  cursor: pointer;
}
.slds-slider__range::-ms-thumb {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: #0070d2;
  border: 0;
  box-shadow: rgba(0, 0, 0, 0.16) 0 2px 3px;
  cursor: pointer;
  -ms-transition: background 0.15s ease-in-out;
  transition: background 0.15s ease-in-out;
  margin-top: calc(4px / 4);
}
.slds-slider__range::-ms-thumb:hover {
  background-color: #005fb2;
}
.slds-slider__range:focus::-webkit-slider-thumb {
  background-color: #005fb2;
  -webkit-box-shadow: 0 0 3px #0070d2;
  box-shadow: 0 0 3px #0070d2;
}
.slds-slider__range:active::-webkit-slider-thumb {
  background-color: #005fb2;
}
.slds-slider__range:focus::-moz-range-thumb {
  background-color: #005fb2;
  box-shadow: 0 0 3px #0070d2;
}
.slds-slider__range:active::-moz-range-thumb {
  background-color: #005fb2;
}
.slds-slider__range[disabled]::-webkit-slider-thumb {
  background-color: #ecebea;
  cursor: default;
}
.slds-slider__range[disabled]::-webkit-slider-runnable-track {
  background-color: #ecebea;
  cursor: default;
}
.slds-slider__range[disabled]::-moz-range-thumb {
  background-color: #ecebea;
  cursor: default;
}
.slds-slider__range[disabled]::-moz-range-track {
  background-color: #ecebea;
}
.slds-slider__range[disabled]::-ms-thumb {
  background-color: #ecebea;
  cursor: default;
}
.slds-slider__range[disabled]::-ms-track {
  background-color: #ecebea;
  cursor: default;
}
.slds-slider__value {
  padding: 0 0.5rem;
}
.slds-slider-label__label {
  display: block;
}
.slds-slider_vertical {
  height: 13.875rem;
}
.slds-slider_vertical .slds-slider__range {
  width: 12rem;
  height: 1rem;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
  -webkit-transform-origin: 6rem 6rem;
  transform-origin: 6rem 6rem;
}
.slds-slider_vertical .slds-slider__value {
  position: absolute;
  left: 0;
  bottom: 0;
  padding: 0;
}
