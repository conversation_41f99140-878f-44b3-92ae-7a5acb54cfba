/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-nav-vertical__item {
  display: block;
}
.slds-nav-vertical__item [type="radio"] {
  width: 1px;
  height: 1px;
  border: 0;
  clip: rect(0 0 0 0);
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
}
.slds-nav-vertical__item [type="radio"]:focus + .slds-nav-vertical__action {
}
.slds-nav-vertical__item [type="radio"]:focus + .slds-nav-vertical__action .slds-nav-vertical_radio-faux {
  text-decoration: underline;
}
.slds-nav-vertical__item [type="radio"]:checked + .slds-nav-vertical__action {
  font-weight: bold;
  background-color: #d8edff;
  -webkit-box-shadow: inset 0.25rem 0 0 #1589ee;
  box-shadow: inset 0.25rem 0 0 #1589ee;
}

.slds-nav-vertical_shade .slds-nav-vertical__item [type="radio"]:checked + .slds-nav-vertical__action {
  border-color: #dddbda;
  background-color: white;
}
