/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-icon-trend {
  width: 1rem;
  height: 1rem;
  display: inline-block;
}
.slds-icon-trend.slds-is-animated .slds-icon-trend__arrow {
  -webkit-animation: slds-icon-trend-arrow 0.8s 0.4s ease-in-out both;
  animation: slds-icon-trend-arrow 0.8s 0.4s ease-in-out both;
}
.slds-icon-trend.slds-is-animated .slds-icon-trend__circle {
  -webkit-animation: slds-icon-trend-circle 0.8s ease-in-out both;
  animation: slds-icon-trend-circle 0.8s ease-in-out both;
}
.slds-icon-trend.slds-is-paused .slds-icon-trend__arrow,
.slds-icon-trend.slds-is-paused .slds-icon-trend__circle {
  -webkit-animation-play-state: paused;
  animation-play-state: paused;
}

.slds-icon-trend[data-slds-trend="down"] {
  color: #c23934;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.slds-icon-trend[data-slds-trend="neutral"] {
  color: #979797;
}

.slds-icon-trend[data-slds-trend="up"] {
  color: #028048;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.slds-icon-trend__arrow,
.slds-icon-trend__circle {
  stroke: currentColor;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 1.125px;
}
.slds-icon-trend__arrow {
  stroke-dashoffset: 0.1px;
  stroke-dasharray: 39.175px;
}
.slds-icon-trend__circle {
  stroke-dasharray: 42.3385px, 46.3385px;
}

@-webkit-keyframes slds-icon-trend-arrow {
  from {
    stroke-dashoffset: 40.3px;
  }
}

@keyframes slds-icon-trend-arrow {
  from {
    stroke-dashoffset: 40.3px;
  }
}
@-webkit-keyframes slds-icon-trend-circle {
  from {
    stroke-dasharray: 0.1px, 46.3385px;
  }
}
@keyframes slds-icon-trend-circle {
  from {
    stroke-dasharray: 0.1px, 46.3385px;
  }
}
