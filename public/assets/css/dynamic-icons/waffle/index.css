/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-icon-waffle_container {
  border: 0;
  outline: 0;
}
.slds-icon-waffle_container:hover [class*="slds-r"],
.slds-icon-waffle_container:focus [class*="slds-r"] {
  -webkit-animation: slds-icon-waffle-throb 2 200ms alternate;
  animation: slds-icon-waffle-throb 2 200ms alternate;
}
.slds-icon-waffle_container:hover .slds-r1,
.slds-icon-waffle_container:hover .slds-r2,
.slds-icon-waffle_container:hover .slds-r3,
.slds-icon-waffle_container:focus .slds-r1,
.slds-icon-waffle_container:focus .slds-r2,
.slds-icon-waffle_container:focus .slds-r3 {
  background-color: #418fde;
}
.slds-icon-waffle_container:hover .slds-r4,
.slds-icon-waffle_container:hover .slds-r5,
.slds-icon-waffle_container:hover .slds-r7,
.slds-icon-waffle_container:focus .slds-r4,
.slds-icon-waffle_container:focus .slds-r5,
.slds-icon-waffle_container:focus .slds-r7 {
  background-color: #ed8b00;
}
.slds-icon-waffle_container:hover .slds-r6,
.slds-icon-waffle_container:hover .slds-r8,
.slds-icon-waffle_container:hover .slds-r9,
.slds-icon-waffle_container:focus .slds-r6,
.slds-icon-waffle_container:focus .slds-r8,
.slds-icon-waffle_container:focus .slds-r9 {
  background-color: #ffb60f;
}
.slds-icon-waffle_container:hover .slds-r2,
.slds-icon-waffle_container:hover .slds-r4,
.slds-icon-waffle_container:focus .slds-r2,
.slds-icon-waffle_container:focus .slds-r4 {
  -webkit-transition-delay: 50ms;
  transition-delay: 50ms;
  -webkit-animation-delay: 50ms;
  animation-delay: 50ms;
}
.slds-icon-waffle_container:hover .slds-r3,
.slds-icon-waffle_container:hover .slds-r5,
.slds-icon-waffle_container:hover .slds-r7,
.slds-icon-waffle_container:focus .slds-r3,
.slds-icon-waffle_container:focus .slds-r5,
.slds-icon-waffle_container:focus .slds-r7 {
  -webkit-transition-delay: 100ms;
  transition-delay: 100ms;
  -webkit-animation-delay: 100ms;
  animation-delay: 100ms;
}
.slds-icon-waffle_container:hover .slds-r6,
.slds-icon-waffle_container:hover .slds-r8,
.slds-icon-waffle_container:focus .slds-r6,
.slds-icon-waffle_container:focus .slds-r8 {
  -webkit-transition-delay: 150ms;
  transition-delay: 150ms;
  -webkit-animation-delay: 150ms;
  animation-delay: 150ms;
}
.slds-icon-waffle_container:hover .slds-r9,
.slds-icon-waffle_container:focus .slds-r9 {
  -webkit-transition-delay: 200ms;
  transition-delay: 200ms;
  -webkit-animation-delay: 200ms;
  animation-delay: 200ms;
}
.slds-icon-waffle {
  width: 1.3125rem;
  height: 1.3125rem;
  position: relative;
  display: block;
  cursor: pointer;
}
.slds-icon-waffle [class*="slds-r"] {
  width: 0.3125rem;
  height: 0.3125rem;
  background-color: #706e6b;
  display: inline-block;
  position: absolute;
  border-radius: 50%;
  -webkit-transition: background-color 100ms;
  transition: background-color 100ms;
  -webkit-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  will-change: scale, background-color;
}
.slds-icon-waffle .slds-r1 {
  top: 0;
  left: 0;
}
.slds-icon-waffle .slds-r2 {
  top: 0;
  left: 0.5rem;
}
.slds-icon-waffle .slds-r3 {
  top: 0;
  right: 0;
}
.slds-icon-waffle .slds-r4 {
  top: 0.5rem;
  left: 0;
}
.slds-icon-waffle .slds-r5 {
  top: 0.5rem;
  left: 0.5rem;
}
.slds-icon-waffle .slds-r6 {
  top: 0.5rem;
  right: 0;
}
.slds-icon-waffle .slds-r7 {
  bottom: 0;
  left: 0;
}
.slds-icon-waffle .slds-r8 {
  bottom: 0;
  left: 0.5rem;
}
.slds-icon-waffle .slds-r9 {
  bottom: 0;
  right: 0;
}
.slds-icon-waffle .slds-r1 {
  -webkit-transition-delay: 200ms;
  transition-delay: 200ms;
}
.slds-icon-waffle .slds-r2,
.slds-icon-waffle .slds-r4 {
  -webkit-transition-delay: 150ms;
  transition-delay: 150ms;
}
.slds-icon-waffle .slds-r3,
.slds-icon-waffle .slds-r5,
.slds-icon-waffle .slds-r7 {
  -webkit-transition-delay: 100ms;
  transition-delay: 100ms;
}
.slds-icon-waffle .slds-r6,
.slds-icon-waffle .slds-r8 {
  -webkit-transition-delay: 50ms;
  transition-delay: 50ms;
}

@-webkit-keyframes slds-icon-waffle-throb {
  to {
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
  }
}

@keyframes slds-icon-waffle-throb {
  to {
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
  }
}
