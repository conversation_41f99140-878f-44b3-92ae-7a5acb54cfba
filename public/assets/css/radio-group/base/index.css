/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-radio {
  display: inline-block;
}
.slds-radio .slds-radio_faux {
  width: 1rem;
  height: 1rem;
  display: inline-block;
  position: relative;
  vertical-align: middle;
  border: 1px solid #dddbda;
  border-radius: 50%;
  background: white;
  -webkit-transition: border 0.1s linear, background-color 0.1s linear;
  transition: border 0.1s linear, background-color 0.1s linear;
}
.slds-radio .slds-form-element__label {
  display: inline;
  vertical-align: middle;
  font-size: 0.8125rem;
}
.slds-radio [type="radio"] {
  width: 1px;
  height: 1px;
  border: 0;
  clip: rect(0 0 0 0);
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
}
.slds-radio [type="radio"]:checked + .slds-radio_faux,
.slds-radio [type="radio"]:checked ~ .slds-radio_faux,
.slds-radio [type="radio"]:checked + .slds-radio__label .slds-radio_faux {
  background: white;
}
.slds-radio [type="radio"]:checked + .slds-radio_faux:after,
.slds-radio [type="radio"]:checked ~ .slds-radio_faux:after,
.slds-radio [type="radio"]:checked + .slds-radio__label .slds-radio_faux:after {
  width: 0.5rem;
  height: 0.5rem;
  content: "";
  position: absolute;
  top: 50%;
  /*! @noflip */
  left: 50%;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
  border-radius: 50%;
  background: #0070d2;
}
.slds-radio [type="radio"]:focus + .slds-radio_faux,
.slds-radio [type="radio"]:focus ~ .slds-radio_faux,
.slds-radio [type="radio"]:focus + .slds-radio__label .slds-radio_faux {
  border-color: #1589ee;
  -webkit-box-shadow: 0 0 3px #0070d2;
  box-shadow: 0 0 3px #0070d2;
}
.slds-radio [type="radio"][disabled] {
  cursor: not-allowed;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.slds-radio [type="radio"][disabled] ~ .slds-radio_faux,
.slds-radio [type="radio"][disabled] + .slds-radio__label .slds-radio_faux {
  background-color: #ecebea;
  border-color: #c9c7c5;
}
.slds-radio [type="radio"][disabled] ~ .slds-radio_faux:after,
.slds-radio [type="radio"][disabled] + .slds-radio__label .slds-radio_faux:after {
  background: #969492;
}

.slds-has-error .slds-radio [type="radio"] + .slds-radio_faux,
.slds-has-error .slds-radio [type="radio"] ~ .slds-radio_faux,
.slds-has-error .slds-radio [type="radio"] + .slds-radio__label .slds-radio_faux {
  border-color: #c23934;
  border-width: 2px;
}
.slds-has-error .slds-radio [type="radio"]:checked + .slds-radio_faux,
.slds-has-error .slds-radio [type="radio"]:checked ~ .slds-radio_faux,
.slds-has-error .slds-radio [type="radio"]:checked + .slds-radio__label .slds-radio_faux {
  background: white;
}
.slds-has-error .slds-radio [type="radio"]:checked + .slds-radio_faux:after,
.slds-has-error .slds-radio [type="radio"]:checked ~ .slds-radio_faux:after,
.slds-has-error .slds-radio [type="radio"]:checked + .slds-radio__label .slds-radio_faux:after {
  background: #d4504c;
}

.slds-form-element .slds-radio [type="radio"] + .slds-radio_faux,
.slds-form-element .slds-radio [type="radio"] ~ .slds-radio_faux,
.slds-radio [type="radio"] + .slds-radio__label .slds-radio_faux {
  margin-right: 0.5rem;
}
