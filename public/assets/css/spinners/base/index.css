/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-spinner_container {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9050;
  background-color: rgba(255, 255, 255, 0.75);
  visibility: visible;
  opacity: 1;
  -webkit-transition: opacity 0.2s ease, visibility 0s;
  transition: opacity 0.2s ease, visibility 0s;
  -webkit-transition-delay: 0s, 0.3s;
  transition-delay: 0s, 0.3s;
}
.slds-spinner_container.is-hidden {
  visibility: hidden;
  opacity: 0;
  -webkit-transition: opacity 0.2s ease, visibility 0s;
  transition: opacity 0.2s ease, visibility 0s;
  -webkit-transition-delay: 0s, 0s;
  transition-delay: 0s, 0s;
}

.slds-spinner_container.hide {
  display: none;
}
.slds-spinner {
  position: absolute;
  top: 50%;
  /*! @noflip */
  left: 50%;
  z-index: 9051;
  -webkit-transform: translate(-50%, -50%) rotate(90deg);
  transform: translate(-50%, -50%) rotate(90deg);
}
.slds-spinner_inline {
  position: relative;
  top: auto;
  left: auto;
  right: auto;
  bottom: auto;
  -webkit-transform: none;
  transform: none;
}

.slds-spinner,
.slds-spinner__dot-a,
.slds-spinner__dot-b {
  -webkit-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  will-change: transform;
}
.slds-spinner__dot-a,
.slds-spinner__dot-b {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.slds-spinner:before,
.slds-spinner:after,
.slds-spinner__dot-a:before,
.slds-spinner__dot-b:before,
.slds-spinner__dot-a:after,
.slds-spinner__dot-b:after {
  content: "";
  position: absolute;
  background: #b0adab;
  border-radius: 50%;
  -webkit-animation-duration: 1000ms;
  animation-duration: 1000ms;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slds-spinner__dot-a {
  -webkit-transform: rotate(60deg);
  transform: rotate(60deg);
}

.slds-spinner__dot-b {
  -webkit-transform: rotate(120deg);
  transform: rotate(120deg);
}

.slds-spinner:before {
  -webkit-animation-delay: -83.33333ms;
  animation-delay: -83.33333ms;
}

.slds-spinner__dot-a:before {
  -webkit-animation-delay: 83.33333ms;
  animation-delay: 83.33333ms;
}

.slds-spinner__dot-b:before {
  -webkit-animation-delay: 250ms;
  animation-delay: 250ms;
}

.slds-spinner:after {
  -webkit-animation-delay: 416.66667ms;
  animation-delay: 416.66667ms;
}

.slds-spinner__dot-a:after {
  -webkit-animation-delay: 583.33333ms;
  animation-delay: 583.33333ms;
}

.slds-spinner__dot-b:after {
  -webkit-animation-delay: 750ms;
  animation-delay: 750ms;
}
.slds-spinner_delayed:before {
  -webkit-animation-delay: 216.66667ms;
  animation-delay: 216.66667ms;
}
.slds-spinner_delayed .slds-spinner__dot-a:before {
  -webkit-animation-delay: 383.33333ms;
  animation-delay: 383.33333ms;
}
.slds-spinner_delayed .slds-spinner__dot-b:before {
  -webkit-animation-delay: 550ms;
  animation-delay: 550ms;
}
.slds-spinner_delayed:after {
  -webkit-animation-delay: 716.66667ms;
  animation-delay: 716.66667ms;
}
.slds-spinner_delayed .slds-spinner__dot-a:after {
  -webkit-animation-delay: 883.33333ms;
  animation-delay: 883.33333ms;
}
.slds-spinner_delayed .slds-spinner__dot-b:after {
  -webkit-animation-delay: 1050ms;
  animation-delay: 1050ms;
}
.slds-spinner_brand.slds-spinner:before,
.slds-spinner_brand.slds-spinner:after,
.slds-spinner_brand .slds-spinner__dot-a:before,
.slds-spinner_brand .slds-spinner__dot-b:before,
.slds-spinner_brand .slds-spinner__dot-a:after,
.slds-spinner_brand .slds-spinner__dot-b:after {
  background-color: #1589ee;
}
.slds-spinner_inverse.slds-spinner:before,
.slds-spinner_inverse.slds-spinner:after,
.slds-spinner_inverse .slds-spinner__dot-a:before,
.slds-spinner_inverse .slds-spinner__dot-b:before,
.slds-spinner_inverse .slds-spinner__dot-a:after,
.slds-spinner_inverse .slds-spinner__dot-b:after {
  background-color: white;
}
.slds-spinner_xx-small {
  width: 0.5rem;
}
.slds-spinner_xx-small.slds-spinner_inline {
  height: 0.5rem;
}
.slds-spinner_xx-small.slds-spinner:before,
.slds-spinner_xx-small.slds-spinner:after,
.slds-spinner_xx-small .slds-spinner__dot-a:before,
.slds-spinner_xx-small .slds-spinner__dot-b:before,
.slds-spinner_xx-small .slds-spinner__dot-a:after,
.slds-spinner_xx-small .slds-spinner__dot-b:after {
  width: 0.125rem;
  height: 0.125rem;
}
.slds-spinner_xx-small.slds-spinner:before,
.slds-spinner_xx-small .slds-spinner__dot-a:before,
.slds-spinner_xx-small .slds-spinner__dot-b:before {
  top: -0.0625rem;
  left: -0.0625rem;
  -webkit-animation-name: dotsBounceBefore-extraExtraSmall;
  animation-name: dotsBounceBefore-extraExtraSmall;
}
.slds-spinner_xx-small.slds-spinner:after,
.slds-spinner_xx-small .slds-spinner__dot-a:after,
.slds-spinner_xx-small .slds-spinner__dot-b:after {
  top: -0.0625rem;
  right: -0.0625rem;
  -webkit-animation-name: dotsBounceAfter-extraExtraSmall;
  animation-name: dotsBounceAfter-extraExtraSmall;
}

@-webkit-keyframes dotsBounceBefore-extraExtraSmall {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.275, 0.0425, 0.34, 0.265);
    animation-timing-function: cubic-bezier(0.275, 0.0425, 0.34, 0.265);
  }
  80% {
    -webkit-transform: translate3d(-0.125rem, 0, 0);
    transform: translate3d(-0.125rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 0.555, 0.35, 0.715);
    animation-timing-function: cubic-bezier(0, 0.555, 0.35, 0.715);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes dotsBounceBefore-extraExtraSmall {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.275, 0.0425, 0.34, 0.265);
    animation-timing-function: cubic-bezier(0.275, 0.0425, 0.34, 0.265);
  }
  80% {
    -webkit-transform: translate3d(-0.125rem, 0, 0);
    transform: translate3d(-0.125rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 0.555, 0.35, 0.715);
    animation-timing-function: cubic-bezier(0, 0.555, 0.35, 0.715);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes dotsBounceAfter-extraExtraSmall {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-animation-timing-function: cubic-bezier(0.275, 0.0425, 0.34, 0.265);
    animation-timing-function: cubic-bezier(0.275, 0.0425, 0.34, 0.265);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  80% {
    -webkit-animation-timing-function: cubic-bezier(0, 0.555, 0.35, 0.715);
    animation-timing-function: cubic-bezier(0, 0.555, 0.35, 0.715);
    -webkit-transform: translate3d(0.125rem, 0, 0);
    transform: translate3d(0.125rem, 0, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes dotsBounceAfter-extraExtraSmall {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-animation-timing-function: cubic-bezier(0.275, 0.0425, 0.34, 0.265);
    animation-timing-function: cubic-bezier(0.275, 0.0425, 0.34, 0.265);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  80% {
    -webkit-animation-timing-function: cubic-bezier(0, 0.555, 0.35, 0.715);
    animation-timing-function: cubic-bezier(0, 0.555, 0.35, 0.715);
    -webkit-transform: translate3d(0.125rem, 0, 0);
    transform: translate3d(0.125rem, 0, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.slds-spinner_x-small {
  width: 1rem;
}
.slds-spinner_x-small.slds-spinner_inline {
  height: 1rem;
}
.slds-spinner_x-small.slds-spinner:before,
.slds-spinner_x-small.slds-spinner:after,
.slds-spinner_x-small .slds-spinner__dot-a:before,
.slds-spinner_x-small .slds-spinner__dot-b:before,
.slds-spinner_x-small .slds-spinner__dot-a:after,
.slds-spinner_x-small .slds-spinner__dot-b:after {
  width: 0.25rem;
  height: 0.25rem;
}
.slds-spinner_x-small.slds-spinner:before,
.slds-spinner_x-small .slds-spinner__dot-a:before,
.slds-spinner_x-small .slds-spinner__dot-b:before {
  top: -0.125rem;
  left: -0.125rem;
  -webkit-animation-name: dotsBounceBefore-extraSmall;
  animation-name: dotsBounceBefore-extraSmall;
}
.slds-spinner_x-small.slds-spinner:after,
.slds-spinner_x-small .slds-spinner__dot-a:after,
.slds-spinner_x-small .slds-spinner__dot-b:after {
  top: -0.125rem;
  right: -0.125rem;
  -webkit-animation-name: dotsBounceAfter-extraSmall;
  animation-name: dotsBounceAfter-extraSmall;
}

@-webkit-keyframes dotsBounceBefore-extraSmall {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(-0.25rem, 0, 0);
    transform: translate3d(-0.25rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes dotsBounceBefore-extraSmall {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(-0.25rem, 0, 0);
    transform: translate3d(-0.25rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes dotsBounceAfter-extraSmall {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(0.25rem, 0, 0);
    transform: translate3d(0.25rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes dotsBounceAfter-extraSmall {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(0.25rem, 0, 0);
    transform: translate3d(0.25rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.slds-spinner_small {
  width: 1.25rem;
}
.slds-spinner_small.slds-spinner_inline {
  height: 1.25rem;
}
.slds-spinner_small.slds-spinner:before,
.slds-spinner_small.slds-spinner:after,
.slds-spinner_small .slds-spinner__dot-a:before,
.slds-spinner_small .slds-spinner__dot-b:before,
.slds-spinner_small .slds-spinner__dot-a:after,
.slds-spinner_small .slds-spinner__dot-b:after {
  width: 0.25rem;
  height: 0.25rem;
}
.slds-spinner_small.slds-spinner:before,
.slds-spinner_small .slds-spinner__dot-a:before,
.slds-spinner_small .slds-spinner__dot-b:before {
  top: -0.125rem;
  left: -0.125rem;
  -webkit-animation-name: dotsBounceBefore-small;
  animation-name: dotsBounceBefore-small;
}
.slds-spinner_small.slds-spinner:after,
.slds-spinner_small .slds-spinner__dot-a:after,
.slds-spinner_small .slds-spinner__dot-b:after {
  top: -0.125rem;
  right: -0.125rem;
  -webkit-animation-name: dotsBounceAfter-small;
  animation-name: dotsBounceAfter-small;
}

@-webkit-keyframes dotsBounceBefore-small {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(-0.375rem, 0, 0);
    transform: translate3d(-0.375rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes dotsBounceBefore-small {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(-0.375rem, 0, 0);
    transform: translate3d(-0.375rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes dotsBounceAfter-small {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(0.375rem, 0, 0);
    transform: translate3d(0.375rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes dotsBounceAfter-small {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(0.375rem, 0, 0);
    transform: translate3d(0.375rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
.slds-spinner_medium {
  width: 2rem;
}
.slds-spinner_medium.slds-spinner_inline {
  height: 2rem;
}
.slds-spinner_medium.slds-spinner:before,
.slds-spinner_medium.slds-spinner:after,
.slds-spinner_medium .slds-spinner__dot-a:before,
.slds-spinner_medium .slds-spinner__dot-b:before,
.slds-spinner_medium .slds-spinner__dot-a:after,
.slds-spinner_medium .slds-spinner__dot-b:after {
  width: 0.5rem;
  height: 0.5rem;
}
.slds-spinner_medium.slds-spinner:before,
.slds-spinner_medium .slds-spinner__dot-a:before,
.slds-spinner_medium .slds-spinner__dot-b:before {
  -webkit-animation-name: dotsBounceBefore-medium;
  animation-name: dotsBounceBefore-medium;
  top: -0.25rem;
  left: -0.25rem;
}
.slds-spinner_medium.slds-spinner:after,
.slds-spinner_medium .slds-spinner__dot-a:after,
.slds-spinner_medium .slds-spinner__dot-b:after {
  -webkit-animation-name: dotsBounceAfter-medium;
  animation-name: dotsBounceAfter-medium;
  top: -0.25rem;
  right: -0.25rem;
}

@-webkit-keyframes dotsBounceBefore-medium {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(-0.5rem, 0, 0);
    transform: translate3d(-0.5rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes dotsBounceBefore-medium {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(-0.5rem, 0, 0);
    transform: translate3d(-0.5rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes dotsBounceAfter-medium {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(0.5rem, 0, 0);
    transform: translate3d(0.5rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes dotsBounceAfter-medium {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(0.5rem, 0, 0);
    transform: translate3d(0.5rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
.slds-spinner_large {
  width: 2.75rem;
}
.slds-spinner_large.slds-spinner_inline {
  height: 2.75rem;
}
.slds-spinner_large.slds-spinner:before,
.slds-spinner_large.slds-spinner:after,
.slds-spinner_large .slds-spinner__dot-a:before,
.slds-spinner_large .slds-spinner__dot-b:before,
.slds-spinner_large .slds-spinner__dot-a:after,
.slds-spinner_large .slds-spinner__dot-b:after {
  width: 0.625rem;
  height: 0.625rem;
}
.slds-spinner_large.slds-spinner:before,
.slds-spinner_large .slds-spinner__dot-a:before,
.slds-spinner_large .slds-spinner__dot-b:before {
  -webkit-animation-name: dotsBounceBefore-medium;
  animation-name: dotsBounceBefore-medium;
  top: -0.3125rem;
  left: -0.3125rem;
}
.slds-spinner_large.slds-spinner:after,
.slds-spinner_large .slds-spinner__dot-a:after,
.slds-spinner_large .slds-spinner__dot-b:after {
  -webkit-animation-name: dotsBounceAfter-medium;
  animation-name: dotsBounceAfter-medium;
  top: -0.3125rem;
  right: -0.3125rem;
}

@-webkit-keyframes dotsBounceBefore-large {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(-0.75rem, 0, 0);
    transform: translate3d(-0.75rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes dotsBounceBefore-large {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(-0.75rem, 0, 0);
    transform: translate3d(-0.75rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@-webkit-keyframes dotsBounceAfter-large {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(0.75rem, 0, 0);
    transform: translate3d(0.75rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes dotsBounceAfter-large {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  60% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  }
  80% {
    -webkit-transform: translate3d(0.75rem, 0, 0);
    transform: translate3d(0.75rem, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
    animation-timing-function: cubic-bezier(0, 1.11, 0.7, 1.43);
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
