/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-form-element_stacked,
.slds-form_stacked .slds-form-element,
.slds-form_horizontal .slds-form-element_stacked {
  display: block;
}
.slds-form-element_stacked:not(.slds-form-element_readonly),
.slds-form_stacked .slds-form-element:not(.slds-form-element_readonly),
.slds-form_horizontal .slds-form-element_stacked:not(.slds-form-element_readonly) {
  margin-bottom: 0.5rem;
}
.slds-form-element_stacked:not(.slds-is-editing),
.slds-form_stacked .slds-form-element:not(.slds-is-editing),
.slds-form_horizontal .slds-form-element_stacked:not(.slds-is-editing) {
  padding: 0 0.25rem;
}
.slds-form-element_stacked .slds-form-element,
.slds-form_stacked .slds-form-element .slds-form-element,
.slds-form_horizontal .slds-form-element_stacked .slds-form-element {
  padding: 0;
  margin-bottom: 0;
}
.slds-form-element_stacked:not([class*="slds-size"]),
.slds-form_stacked .slds-form-element:not([class*="slds-size"]),
.slds-form_horizontal .slds-form-element_stacked:not([class*="slds-size"]) {
  width: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
}
.slds-form-element_stacked .slds-checkbox,
.slds-form-element_stacked .slds-radio,
.slds-form_stacked .slds-form-element .slds-checkbox,
.slds-form_stacked .slds-form-element .slds-radio,
.slds-form_horizontal .slds-form-element_stacked .slds-checkbox,
.slds-form_horizontal .slds-form-element_stacked .slds-radio {
  display: block;
}
.slds-form-element_stacked .slds-form-element__label,
.slds-form-element_stacked .slds-form-element__control,
.slds-form_stacked .slds-form-element .slds-form-element__label,
.slds-form_stacked .slds-form-element .slds-form-element__control,
.slds-form_horizontal .slds-form-element_stacked .slds-form-element__label,
.slds-form_horizontal .slds-form-element_stacked .slds-form-element__control {
  border-bottom: 0;
  padding-left: 0;
}
.slds-form-element_stacked .slds-form-element__control,
.slds-form_stacked .slds-form-element .slds-form-element__control,
.slds-form_horizontal .slds-form-element_stacked .slds-form-element__control {
  width: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  clear: left;
}
.slds-form-element_stacked .slds-form-element__icon,
.slds-form_stacked .slds-form-element .slds-form-element__icon,
.slds-form_horizontal .slds-form-element_stacked .slds-form-element__icon {
  float: none;
  padding-top: 0.25rem;
}
