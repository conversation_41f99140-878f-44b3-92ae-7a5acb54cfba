/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
/*! normalize.css v3.0.2 | MIT License | git.io/normalize */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

template {
  display: none;
}

a {
  background-color: transparent;
}

a:active,
a:hover {
  outline: 0;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b,
strong {
  font-weight: bold;
}

dfn {
  font-style: italic;
}

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

mark {
  background: #ff0;
  color: #000;
}

small {
  font-size: 80%;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

img {
  border: 0;
}

figure {
  margin: 1em 40px;
}

hr {
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
}

pre {
  overflow: auto;
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}

button {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled],
input[disabled] {
  cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input {
  line-height: normal;
}

input[type="checkbox"],
input[type="radio"] {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

input[type="search"] {
  -webkit-appearance: textfield;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}

legend {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
}

optgroup {
  font-weight: bold;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

::-webkit-input-placeholder {
  color: #706e6b;
  font-weight: 400;
}

::-moz-placeholder {
  color: #706e6b;
  font-weight: 400;
}

:-ms-input-placeholder {
  color: #706e6b;
  font-weight: 400;
}

::-ms-input-placeholder {
  color: #706e6b;
  font-weight: 400;
}

::placeholder {
  color: #706e6b;
  font-weight: 400;
}

::-moz-selection {
  background: #d8edff;
  text-shadow: none;
  color: #080707;
}

::selection {
  background: #d8edff;
  text-shadow: none;
  color: #080707;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
ol,
ul,
dl,
fieldset {
  margin: 0;
  padding: 0;
}

dd,
figure {
  margin: 0;
}

abbr[title] {
  text-decoration: none;
}

abbr[title],
fieldset,
hr {
  border: 0;
}

hr {
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: inherit;
  font-size: 1em;
}

ol,
ul {
  list-style: none;
}

a {
  color: #006dcc;
  text-decoration: none;
  -webkit-transition: color 0.1s linear;
  transition: color 0.1s linear;
}
a:hover,
a:focus {
  text-decoration: underline;
  color: #005fb2;
}
a:active {
  color: #005fb2;
}

a,
button {
  cursor: pointer;
}

b,
strong,
dfn {
  font-weight: 700;
}

mark {
  background-color: #fff03f;
  color: #080707;
}

abbr[title] {
  cursor: help;
}

input[type="search"] {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

table {
  width: 100%;
}

caption,
th,
td {
  text-align: left;
}

hr {
  display: block;
  margin: 2rem 0;
  border-top: 1px solid #dddbda;
  height: 1px;
  clear: both;
}

audio,
canvas,
iframe,
img,
svg,
video {
  vertical-align: middle;
}

img {
  max-width: 100%;
  height: auto;
}
