/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-button {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0;
  background: transparent;
  background-clip: border-box;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  line-height: 1.875rem;
  text-decoration: none;
  color: #0070d2;
  -webkit-appearance: none;
  white-space: normal;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.slds-button:hover,
.slds-button:focus,
.slds-button:active,
.slds-button:visited {
  text-decoration: none;
}
.slds-button:hover,
.slds-button:focus {
  color: #005fb2;
}
.slds-button:focus {
  outline: 0;
  -webkit-box-shadow: 0 0 3px #0070d2;
  box-shadow: 0 0 3px #0070d2;
}
.slds-button:active {
  color: #005fb2;
}
.slds-button[disabled],
.slds-button:disabled {
  color: #dddbda;
}
.slds-button[disabled] *,
.slds-button:disabled * {
  pointer-events: none;
}
.slds-button a {
  color: currentColor;
}
.slds-button:hover .slds-button__icon,
.slds-button:focus .slds-button__icon,
.slds-button:active .slds-button__icon,
.slds-button[disabled] .slds-button__icon,
.slds-button:disabled .slds-button__icon {
  fill: currentColor;
  pointer-events: none;
}
.slds-button + .slds-button-group,
.slds-button + .slds-button-group-list {
  margin-left: 0.25rem;
}
.slds-button + .slds-button {
  margin-left: 0.25rem;
}

a.slds-button {
  text-align: center;
}
a.slds-button:focus {
  outline: 0;
  -webkit-box-shadow: 0 0 3px #0070d2;
  box-shadow: 0 0 3px #0070d2;
}

a.slds-button_inverse:focus,
a.slds-button--inverse:focus {
  outline: none;
  -webkit-box-shadow: 0 0 3px #ecebea;
  box-shadow: 0 0 3px #ecebea;
  border: 1px solid #ecebea;
}
.slds-button_reset {
  font-size: inherit;
  color: inherit;
  line-height: inherit;
  padding: 0;
  background: transparent;
  border: 0;
  text-align: inherit;
}
.slds-button_neutral {
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  vertical-align: middle;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 1px solid #dddbda;
  -webkit-transition: border 0.15s linear;
  transition: border 0.15s linear;
  border-color: #dddbda;
  background-color: white;
}
.slds-button_neutral:hover,
.slds-button_neutral:focus {
  background-color: #f4f6f9;
}
.slds-button_neutral:active {
  background-color: #eef1f6;
}
.slds-button_neutral[disabled],
.slds-button_neutral:disabled {
  background-color: white;
  cursor: default;
}
.slds-button_brand {
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  vertical-align: middle;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 1px solid #dddbda;
  -webkit-transition: border 0.15s linear;
  transition: border 0.15s linear;
  background-color: #0070d2;
  border-color: #0070d2;
  color: white;
}
.slds-button_brand:link,
.slds-button_brand:visited,
.slds-button_brand:active {
  color: white;
}
.slds-button_brand:hover,
.slds-button_brand:focus {
  background-color: #005fb2;
  border-color: #005fb2;
  color: white;
}
.slds-button_brand:active {
  background-color: #005fb2;
  border-color: #005fb2;
}
.slds-button_brand[disabled],
.slds-button_brand:disabled {
  background: #c9c7c5;
  border-color: #c9c7c5;
  color: white;
}
.slds-button_outline-brand {
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  vertical-align: middle;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 1px solid #dddbda;
  -webkit-transition: border 0.15s linear;
  transition: border 0.15s linear;
  border-color: #dddbda;
  background-color: white;
  border-color: #0070d2;
}
.slds-button_outline-brand:hover,
.slds-button_outline-brand:focus {
  background-color: #f4f6f9;
}
.slds-button_outline-brand:active {
  background-color: #eef1f6;
}
.slds-button_outline-brand[disabled],
.slds-button_outline-brand:disabled {
  border-color: #dddbda;
  color: #dddbda;
  background-color: white;
}
.slds-button_inverse {
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  vertical-align: middle;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 1px solid #dddbda;
  -webkit-transition: border 0.15s linear;
  transition: border 0.15s linear;
  background-color: rgba(0, 0, 0, 0);
  border-color: #dddbda;
}
.slds-button_inverse:hover,
.slds-button_inverse:focus {
  background-color: #f4f6f9;
}
.slds-button_inverse[disabled],
.slds-button_inverse:disabled {
  background-color: rgba(0, 0, 0, 0);
  border-color: rgba(255, 255, 255, 0.15);
}

.slds-button_inverse,
.slds-button_inverse:link,
.slds-button_inverse:visited,
.slds-button_icon-border-inverse,
.slds-button_icon-border-inverse:link,
.slds-button_icon-border-inverse:visited {
  color: #ecebea;
}
.slds-button_inverse:hover,
.slds-button_inverse:focus,
.slds-button_inverse:active,
.slds-button_icon-border-inverse:hover,
.slds-button_icon-border-inverse:focus,
.slds-button_icon-border-inverse:active {
  color: #0070d2;
}
.slds-button_inverse:focus,
.slds-button_icon-border-inverse:focus {
  outline: none;
  -webkit-box-shadow: 0 0 3px #ecebea;
  box-shadow: 0 0 3px #ecebea;
  border: 1px solid #ecebea;
}
.slds-button_inverse[disabled],
.slds-button_inverse:disabled,
.slds-button_icon-border-inverse[disabled],
.slds-button_icon-border-inverse:disabled {
  color: rgba(255, 255, 255, 0.5);
}
.slds-button_destructive {
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  vertical-align: middle;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 1px solid #dddbda;
  -webkit-transition: border 0.15s linear;
  transition: border 0.15s linear;
  background-color: #c23934;
  border-color: #c23934;
  color: white;
}
.slds-button_destructive:link,
.slds-button_destructive:visited,
.slds-button_destructive:active {
  color: white;
}
.slds-button_destructive:hover,
.slds-button_destructive:focus {
  background-color: #a61a14;
  color: white;
}
.slds-button_destructive:active {
  background-color: #870500;
  border-color: #870500;
}
.slds-button_destructive[disabled],
.slds-button_destructive:disabled {
  background: #c9c7c5;
  border-color: #c9c7c5;
  color: white;
}
.slds-button_text-destructive {
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  vertical-align: middle;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 1px solid #dddbda;
  -webkit-transition: border 0.15s linear;
  transition: border 0.15s linear;
  border-color: #dddbda;
  background-color: white;
  color: #c23934;
}
.slds-button_text-destructive:hover,
.slds-button_text-destructive:focus {
  background-color: #f4f6f9;
}
.slds-button_text-destructive:active {
  background-color: #eef1f6;
}
.slds-button_text-destructive:focus,
.slds-button_text-destructive:hover {
  color: #a12b2b;
}
.slds-button_text-destructive[disabled],
.slds-button_text-destructive:disabled {
  color: #dddbda;
  background-color: white;
}
.slds-button_success {
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  vertical-align: middle;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 1px solid #dddbda;
  -webkit-transition: border 0.15s linear;
  transition: border 0.15s linear;
  background-color: #4bca81;
  border-color: #4bca81;
  color: #080707;
}
.slds-button_success:link,
.slds-button_success:visited,
.slds-button_success:active {
  color: #080707;
}
.slds-button_success:hover,
.slds-button_success:focus {
  background-color: #04844b;
  border-color: #04844b;
  color: white;
}
.slds-button_success:active {
  background-color: #04844b;
  border-color: #04844b;
}
.slds-button_success[disabled],
.slds-button_success:disabled {
  background: #c9c7c5;
  border-color: #c9c7c5;
  color: white;
}
.slds-button__icon {
  width: 0.875rem;
  height: 0.875rem;
  fill: currentColor;
}
.slds-button__icon_large {
  width: 1.5rem;
  height: 1.5rem;
}
.slds-button__icon_small {
  width: 0.75rem;
  height: 0.75rem;
}
.slds-button__icon_x-small {
  width: 0.5rem;
  height: 0.5rem;
}
.slds-button__icon_left {
  margin-right: 0.5rem;
}
.slds-button__icon_right {
  margin-left: 0.5rem;
}
.slds-button_full-width {
  font-size: inherit;
  color: inherit;
  line-height: inherit;
  padding: 0;
  background: transparent;
  border: 0;
  text-align: inherit;
  width: 100%;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.slds-button_full-width:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.slds-button_stretch {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 100%;
}
