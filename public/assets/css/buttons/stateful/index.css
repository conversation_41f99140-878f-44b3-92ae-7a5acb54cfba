/*! Lightning Design System 2.12.2 */
@charset "UTF-8";
.slds-button_neutral.slds-is-selected {
  border-color: transparent;
  background-color: transparent;
}
.slds-button_neutral.slds-is-selected:hover:not([disabled]),
.slds-button_neutral.slds-is-selected:focus:not([disabled]) {
  border-color: #dddbda;
  background-color: #f4f6f9;
}
.slds-button_neutral.slds-is-selected:active:not([disabled]) {
  background-color: #eef1f6;
}

.slds-button_inverse.slds-is-selected {
  border-color: transparent;
}

.slds-button_stateful .slds-text-selected,
.slds-button_stateful .slds-text-selected-focus,
.slds-button_stateful .slds-text-not-selected {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.slds-not-selected {
}
.slds-not-selected .slds-text-selected {
  display: none;
}
.slds-not-selected .slds-text-selected-focus {
  display: none;
}
.slds-not-selected .slds-text-not-selected {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.slds-is-selected-clicked .slds-text-selected,
.slds-is-selected[disabled] .slds-text-selected,
.slds-is-selected[disabled]:hover .slds-text-selected,
.slds-is-selected[disabled]:focus .slds-text-selected {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.slds-is-selected-clicked .slds-text-selected-focus,
.slds-is-selected[disabled] .slds-text-selected-focus,
.slds-is-selected[disabled]:hover .slds-text-selected-focus,
.slds-is-selected[disabled]:focus .slds-text-selected-focus {
  display: none;
}
.slds-is-selected-clicked .slds-text-not-selected,
.slds-is-selected[disabled] .slds-text-not-selected,
.slds-is-selected[disabled]:hover .slds-text-not-selected,
.slds-is-selected[disabled]:focus .slds-text-not-selected {
  display: none;
}
.slds-is-selected .slds-text-not-selected {
  display: none;
}
.slds-is-selected .slds-text-selected {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.slds-is-selected .slds-text-selected-focus {
  display: none;
}
.slds-is-selected:hover .slds-text-not-selected,
.slds-is-selected:focus .slds-text-not-selected {
  display: none;
}
.slds-is-selected:hover .slds-text-selected,
.slds-is-selected:focus .slds-text-selected {
  display: none;
}
.slds-is-selected:hover .slds-text-selected-focus,
.slds-is-selected:focus .slds-text-selected-focus {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
