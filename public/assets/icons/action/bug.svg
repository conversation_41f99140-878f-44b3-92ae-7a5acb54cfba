<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 23.0.2, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="52px" height="52px" viewBox="0 0 52 52" enable-background="new 0 0 52 52" xml:space="preserve">
<g id="padding" display="none">
	<path display="inline" opacity="0.2" fill="#FFFFFF" enable-background="new" d="M0,0v52h52V0H0z M49.9,49.9H2.1V2.1h47.8
		C49.9,2.1,49.9,49.9,49.9,49.9z"/>
</g>
<g id="live_area" display="none">
	<rect x="2" y="2" display="inline" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="48"/>
</g>
<g id="grid" display="none">
	<g display="inline" opacity="0.5">
		<line fill="none" stroke-width="0.25" stroke-miterlimit="10" x1="50" y1="2" x2="2" y2="49.9"/>
		<line fill="none" stroke-width="0.25" stroke-miterlimit="10" x1="2" y1="2" x2="49.9" y2="49.9"/>
		<rect x="2" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="4" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="6" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="8" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="10" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="12" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="14" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="16" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="18" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="20" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="22" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="24" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="26" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="28" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="30" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="32" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="34" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="36" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="38" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="40" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="42" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="44" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="46" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="48" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="2" height="48"/>
		<rect x="2" y="48" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="46" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="44" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="42" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="40" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="38" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="36" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="34" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="32" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="30" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="28" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="26" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="24" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="22" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="20" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="18" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="16" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="14" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="12" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="10" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="8" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="6" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="4" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
		<rect x="2" y="2" fill="none" stroke-width="0.25" stroke-miterlimit="10" width="48" height="2"/>
	</g>
</g>
<g id="icons">
	<g>
		<path fill="#FFFFFF" d="M19.7,10.1h12.7c0.9,0,1.6-0.9,1.4-1.7c-0.8-3.6-4-6.2-7.8-6.2s-7,2.7-7.8,6.2
			C18.1,9.2,18.8,10.1,19.7,10.1z M47.5,30.8c1.4,0,2.5-1.1,2.4-2.5C49.8,27,48.7,26,47.3,26h-7v-4c4.6-1.8,7.9-6.8,7.9-12.6
			c0-1.2-0.8-2.2-2-2.5c-1.5-0.2-2.8,0.9-2.8,2.4c0,3.4-1.6,6.4-3.9,7.8c-0.9-1.4-2.4-2.2-4.1-2.2H16.5c-1.7,0-3.2,0.9-4.1,2.2
			c-2.3-1.4-3.9-4.3-3.9-7.6c0-1.3-1-2.5-2.2-2.5C4.9,6.9,3.8,8,3.8,9.3c0,5.9,3.3,10.9,7.9,12.7v4h-7c-1.3,0-2.5,1-2.5,2.2
			c-0.1,1.4,1,2.5,2.4,2.5h7.2v4c-4.6,1.8-7.9,6.8-7.9,12.6c0,1.2,0.8,2.2,2,2.5c1.5,0.2,2.8-0.9,2.8-2.4c0-3.3,1.5-6.3,3.8-7.7
			c1.4,4.5,4.8,7.9,9.2,9.4c1,0.3,2.1-0.5,2.1-1.5V28.5c0-1.3,1-2.5,2.2-2.5c1.4-0.1,2.5,1,2.5,2.4v19.2c0,1.1,1,1.8,2.1,1.5
			c4.4-1.4,7.9-4.9,9.2-9.4c2.2,1.4,3.7,4.3,3.8,7.5c0,1.3,1,2.5,2.2,2.5c1.4,0.1,2.5-1,2.5-2.4c0-5.9-3.3-10.9-7.9-12.7v-4h7.1
			C47.5,30.6,47.5,30.8,47.5,30.8z"/>
	</g>
</g>
</svg>
