## Salesforce Clone

Technologies Used:

[React](https://reactjs.org/) is a popular JavaScript library for building user interfaces.

[Vite](https://vitejs.dev/) is a blazing fast frontend build tool that includes features like Hot Module Reloading (HMR), optimized builds, and TypeScript support out of the box.

[Docker](https://www.docker.com/) is a platform for developing, shipping, and running applications in containers.

### Docker Setup

To run the app using Docker, follow these steps:

1. **Clone the repository**:

   ```sh
   git clone https://github.com/Wombat-Offline-Website/sfw-a68-profile-creation
   cd sfw-a68-profile-creation
   ```

2. **Build the Docker image**:

   ```sh
   docker buildx build --platform=linux/amd64 -f ./Dockerfile -t sfw-a68-profile-creation . --load
   ```

3. **Run the Docker container**:

   ```sh
   docker run -p 5173:5173 sfw-a68-profile-creation
   ```

4. **Open your browser** and navigate to `localhost:5173` to see the app running.

### Getting Started (For Development)

To start the ReactJS app, follow these steps:

1. **Clone the repository**:

   ```sh
   git clone https://github.com/Wombat-Offline-Website/sfw-a68-profile-creation
   cd sfw-a68-profile-creation
   ```

2. **Install dependencies**:

   ```sh
   npm install
   ```

3. **Start the development server**:

   ```sh
   npm run dev
   ```

4. **Open your browser** and navigate to `localhost:5173` to see the app running.

## Prompt Generation.

<details>
<summary><strong>Running the A83 Workflow Tasks Generation Python Script</strong> (click to expand)</summary>

### Note: No special packages are needed here, hence no requirements.txt file.

**Note:** Python version used: `Python 3.9.6`

1. **After cloning the repository** navigate to the project directory:

   ```bash
   cd salesforce-60-61-62-63-64-65
   ```

   **Note:** Based on the Python version on your system, you may need to use (`python3`, `pip3`) or (`python`, `pip`).

2. **Run the script**:

   ```bash
   python3 scripts/workflow-a83/script.py
   ```

   or

   ```bash
   python scripts/workflow-a83/script.py
   ```

   Running this command will generate 20 workflow tasks by default and store them in the `generated_workflows/a83/prompts.json` file.

### Command-line Arguments

You can customize the number of tasks and output directory:

```
python3 scripts/workflow-a83/script.py [-n NUMBER_OF_TASKS] [-o OUTPUT_DIRECTORY]
```

**Example command:**

```
python3 scripts/workflow-a83/script.py \
  --number 10 \
  --output generated_workflows/a83/prompts.json
```

Running this command will generate 30 workflow tasks and store them in the `generated_workflows/a83/prompts.json` file.

</details>

<details>
<summary><strong>Sample generated prompt for workflow a83</strong> (click to expand)</summary>

Example Prompt

```json
[
  {
    "prompt": "Create a classic email template with Text type titled 'Appointment for Unauthenticated User using Engagement Channels For third party.' in the 'Unfiled Public Classic Email Templates' folder with subject 'Appointment for Unauthenticated User using Engagement Channels For third party. Notification' and body 'Email template for confirmation of an appointment...'. Create an approval process using Standard Setup Wizard named “Spendings Report Approval.” Set entry criteria where Opportunity: Amount not equal to 4361. Specify the approver field as Manager. Use the email template “Appointment for Unauthenticated User using Engagement Channels For third party.” for assignment emails. Include fields Account Name, Amount and Contract in the approval layout. Select 'Record Creator' from 'Creator' option of the submitter type. Then create an approval step with the name CFO Approval, description 'This is for CFO Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process.",
    "result": {
      "classicEmailTemplate": {},
      "classicEmailTemplateList": [
        {
          "templateType": "Custom",
          "folder": "Unfiled Public Classic Email Templates",
          "availableForUse": false,
          "emailTemplateName": "Appointment for Unauthenticated User using Engagement Channels For third party.",
          "templateUniqueName": "Appointment for Unauthenticated User using Engagement Channels For third party.",
          "encoding": "Unicode (UTF-8)",
          "description": "",
          "subject": "Appointment for Unauthenticated User using Engagement Channels For third party. Notification",
          "body": "Email template for confirmation of an appointment..."
        }
      ],
      "fieldUpdate": {},
      "fieldUpdateList": [],
      "approvalProcesses": {
        "step1": {},
        "step2": {},
        "step3": {},
        "step4": {},
        "step5": {},
        "step6": {},
        "active": false,
        "hasCreatedSteps": false
      },
      "approvalProcessesList": [
        {
          "step1": {
            "name": "Spendings Report Approval",
            "uniqueName": "Spendings_Report_Approval",
            "desc": ""
          },
          "step2": {
            "approvalProcessCriteria": "criteria are met",
            "filterLogics": [
              {
                "field": "Opportunity: Amount",
                "operator": "not equal to",
                "value": "4361"
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              }
            ]
          },
          "step3": {
            "nextAutomatedApproverDeterminedBy": "Manager",
            "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.",
            "useApproverFieldOfOpportunityOwner": false
          },
          "step4": {
            "approvalAssignmentEmailTemplate": "Appointment for Unauthenticated User using Engagement Channels For third party."
          },
          "step5": {
            "selectedFields": [
              "Opportunity Name",
              "Opportunity Owner",
              "Account Name",
              "Amount",
              "Contract"
            ],
            "approvalPageFields": true,
            "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"
          },
          "step6": {
            "submitterType": "Creator",
            "allowedSubmitters": ["Record Creator"],
            "pageLayoutSettings": true,
            "allowSubmittersToRecallApprovalRequests": false
          },
          "approvalStep": {
            "approvalStep": {
              "approvalStepOption": "Yes, I'd like to create an approval step now."
            },
            "approvalStep1": {
              "approvalProcessName": "CFO Approval",
              "approvalProcessUniqueName": "CFO_Approval",
              "approvalProcessDesc": "This is for CFO Approval.",
              "approvalProcessStepNumber": "1"
            },
            "approvalStep2": {
              "stepCriteria": "All records should enter this step."
            },
            "approvalStep3": {
              "approverAction": "Automatically assign using the user field selected earlier. (Manager)",
              "requestApproval": false
            }
          },
          "active": true,
          "hasCreatedSteps": true
        }
      ],
      "opportunity": {},
      "opportunityList": [],
      "contact": {},
      "contactList": [],
      "emailAlert": {}
    }
  }
]
```

</details>

<details>
<summary><strong>Running the A139 Workflow Tasks Generation Python Script</strong> (click to expand)</summary>

### Note: No special packages are needed here, hence no requirements.txt file.

**Note:** Python version used: `Python 3.9.6`

1. **After cloning the repository** navigate to the project directory:

   ```bash
   cd salesforce-60-61-62-63-64-65
   ```

   **Note:** Based on the Python version on your system, you may need to use (`python3`, `pip3`) or (`python`, `pip`).

2. **Run the script**:

   ```bash
   python3 scripts/workflow-a139/script.py
   ```

   or

   ```bash
   python scripts/workflow-a139/script.py
   ```

   Running this command will generate 20 workflow tasks by default and store them in the `generated_workflows/a139/prompts.json` file.

### Command-line Arguments

You can customize the number of tasks and output directory:

```
python3 scripts/workflow-a139/script.py [-n NUMBER_OF_TASKS] [-o OUTPUT_DIRECTORY]
```

**Example command:**

```
python3 scripts/workflow-a139/script.py \
  --number 10 \
  --output generated_workflows/a139/prompts.json
```

Running this command will generate 30 workflow tasks and store them in the `generated_workflows/a139/prompts.json` file.

</details>

<details>
<summary><strong>Sample generated prompt for workflow a139</strong> (click to expand)</summary>

Example Prompt

```json
[
  {
    "prompt": "Create an approval process using Standard Setup Wizard named “Spendings Report Approval.” Set entry criteria where Opportunity: Amount less than 4271. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Include fields Account Name, Amount and Contract in the approval layout. Select 'Opportunity Owner' from 'Owner' option of the submitter type. Then create an approval step with the name Manager Approval, description 'This is for Manager Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. Then, create a new approval action “Email Alert.” Enter Description “Alert for EmailAlert_6701”, Unique Name “EmailAlert_6701”, and select Email Template “Rescheduled Service Appointment Confirmation Email”. Check “Protected Component”. Move User: Security User, User: chetan chavan, User: Integration User from Available Recipients to Selected Recipients, then Save. Under Final Rejection Actions, add a new Field Update: Name “FU_4579” and select field “Opportunity: Probability (%)”. Activate the action. Add the '<EMAIL>', '<EMAIL>', '<EMAIL>' emails to additional emails field.Select 'Current User's email address' from the 'From Email Address' field and uncehck the 'Make this address default' option. Add a final rejection action for field update where name is 'High Value Status', unique name is 'High_Value_Status', description 'This is for High Value Status.', and the 'Field to Update' should be Closed Won. The 'Re-evaluate Workflow Rules after Field Change' should be unchecked, and text options should be 'A blank value (null)'. From Sales in App Launcher, create a new Opportunity named 'Opportunity_9961' with amount 97997, close date 2025-06-20, account name 'Stark Industries', type 'Existing Customer - Replacement', stage 'Negotiation/Review', and probability '11%' and save the opportunity.",
    "result": {
      "classicEmailTemplate": {},
      "classicEmailTemplateList": [],
      "fieldUpdate": {},
      "fieldUpdateList": [],
      "approvalProcesses": {
        "step1": {},
        "step2": {},
        "step3": {},
        "step4": {},
        "step5": {},
        "step6": {},
        "active": false,
        "hasCreatedSteps": false
      },
      "approvalProcessesList": [
        {
          "step1": {
            "name": "Spendings Report Approval",
            "uniqueName": "Spendings_Report_Approval",
            "desc": ""
          },
          "step2": {
            "approvalProcessCriteria": "criteria are met",
            "filterLogics": [
              {
                "field": "Opportunity: Amount",
                "operator": "less than",
                "value": "4271"
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              }
            ]
          },
          "step3": {
            "nextAutomatedApproverDeterminedBy": "Manager",
            "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.",
            "useApproverFieldOfOpportunityOwner": false
          },
          "step4": {
            "approvalAssignmentEmailTemplate": "High-value Opportunity Notification"
          },
          "step5": {
            "selectedFields": ["Account Name", "Amount", "Contract"],
            "approvalPageFields": true,
            "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"
          },
          "step6": {
            "prompt": "Select 'Opportunity Owner' from 'Owner' option of the submitter type.",
            "result": {
              "submitterType": "Owner",
              "allowedSubmitters": ["Opportunity Owner"],
              "pageLayoutSettings": true,
              "allowSubmittersToRecallApprovalRequests": false
            }
          },
          "approvalStep": {
            "approvalStep": {
              "approvalStepOption": "Yes, I'd like to create an approval step now."
            },
            "approvalStep1": {
              "approvalProcessName": "Manager Approval",
              "approvalProcessUniqueName": "Manager_Approval",
              "approvalProcessDesc": "This is for Manager Approval.",
              "approvalProcessStepNumber": "1"
            },
            "approvalStep2": {
              "stepCriteria": "All records should enter this step."
            },
            "approvalStep3": {
              "approverAction": "Automatically assign using the user field selected earlier. (Manager)",
              "requestApproval": false
            }
          },
          "active": true,
          "hasCreatedSteps": true
        }
      ],
      "opportunity": {
        "name": "Opportunity_9961",
        "amount": 97997,
        "closeDate": "2025-06-20",
        "accountName": "Stark Industries",
        "type": "Existing Customer - Replacement",
        "stage": "Negotiation/Review",
        "probability": "11%"
      },
      "opportunityList": [],
      "contact": {},
      "contactList": [],
      "emailAlert": {
        "emailAlert": {
          "description": "Alert for EmailAlert_6701",
          "uniqueName": "EmailAlert_6701",
          "emailTemplate": "Rescheduled Service Appointment Confirmation Email",
          "protectedComponent": true,
          "selectedRecipients": [
            "User: Security User",
            "User: chetan chavan",
            "User: Integration User"
          ],
          "additionalEmails": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
          ],
          "makeDefault": false,
          "from_email_address": "Current User's email address"
        },
        "finalRejectionFieldUpdate": {
          "name": "High Value Status",
          "uniqueName": "High_Value_Status",
          "description": "This is for High Value Status.",
          "fieldToUpdate": "Closed Won",
          "reEvaluateWorkflowRules": false,
          "textOptions": "A blank value (null)"
        },
        "activated": true
      }
    }
  }
]
```

</details>

<details>
<summary><strong>Running the A61 Workflow Tasks Generation Python Script</strong> (click to expand)</summary>

### Note: No special packages are needed here, hence no requirements.txt file.

**Note:** Python version used: `Python 3.9.6`

1. **After cloning the repository** navigate to the project directory:

   ```bash
   cd salesforce-60-61-62-63-64-65
   ```

   **Note:** Based on the Python version on your system, you may need to use (`python3`, `pip3`) or (`python`, `pip`).

2. **Run the script**:

   ```bash
   python3 scripts/workflow-a61/script.py
   ```

   or

   ```bash
   python scripts/workflow-a61/script.py
   ```

   Running this command will generate 20 workflow tasks by default and store them in the `generated_workflows/a61/prompts.json` file.

### Command-line Arguments

You can customize the number of tasks and output directory:

```
python3 scripts/workflow-a61/script.py [-n NUMBER_OF_TASKS] [-o OUTPUT_DIRECTORY]
```

**Example command:**

```
python3 scripts/workflow-a61/script.py \
  --number 10 \
  --output generated_workflows/a61/prompts.json
```

Running this command will generate 30 workflow tasks and store them in the `generated_workflows/a61/prompts.json` file.

</details>

<details>
<summary><strong>Sample generated prompt for workflow a61</strong> (click to expand)</summary>

Example Prompt

```json
[
  {
    "prompt": "Define a new classic email template with Text type called 'Appointment for Unauthenticated User using Appointment Types For third party.' in the 'Unfiled Public Classic Email Templates' folder, using subject 'Appointment for Unauthenticated User using Appointment Types For third party. Notification' and body 'Email template for confirmation of an appointment...'. Then edit the created email template, and then select 'Contact Fields' as the field type, choose 'Fax Opt Out' as the field, and save the updated template. Open Sales from the App Launcher. In Contacts, edit the 'Express Logistics and Transport' record and create a new Contact with salutation 'Prof.', first name 'Jane', and last name 'Doe'. Save the contact.",
    "result": {
      "classicEmailTemplate": {},
      "classicEmailTemplateList": [
        {
          "templateType": "Custom",
          "folder": "Unfiled Public Classic Email Templates",
          "availableForUse": false,
          "emailTemplateName": "Appointment for Unauthenticated User using Appointment Types For third party.",
          "templateUniqueName": "Appointment for Unauthenticated User using Appointment Types For third party.",
          "encoding": "Unicode (UTF-8)",
          "description": "",
          "subject": "Appointment for Unauthenticated User using Appointment Types For third party. Notification",
          "body": "Email template for confirmation of an appointment..."
        }
      ],
      "fieldUpdate": {},
      "fieldUpdateList": [],
      "approvalProcesses": {},
      "approvalProcessesList": [],
      "opportunity": {},
      "opportunityList": [],
      "contact": {},
      "contactList": [
        {
          "account": "Express Logistics and Transport",
          "salutation": "Prof.",
          "firstName": "Jane",
          "lastName": "Doe"
        }
      ],
      "emailAlert": {}
    }
  }
]
```

</details>

<details>
<summary><strong>Running the A105 Workflow Tasks Generation Python Script</strong> (click to expand)</summary>

### Note: No special packages are needed here, hence no requirements.txt file.

**Note:** Python version used: `Python 3.9.6`

1. **After cloning the repository** navigate to the project directory:

   ```bash
   cd salesforce-60-61-62-63-64-65
   ```

   **Note:** Based on the Python version on your system, you may need to use (`python3`, `pip3`) or (`python`, `pip`).

2. **Run the script**:

   ```bash
   python3 scripts/workflow-a105/script.py
   ```

   or

   ```bash
   python scripts/workflow-a105/script.py
   ```

   Running this command will generate 20 workflow tasks by default and store them in the `generated_workflows/a105/prompts.json` file.

### Command-line Arguments

You can customize the number of tasks and output directory:

```
python3 scripts/workflow-a105/script.py [-n NUMBER_OF_TASKS] [-o OUTPUT_DIRECTORY]
```

**Example command:**

```
python3 scripts/workflow-a105/script.py \
  --number 10 \
  --output generated_workflows/a105/prompts.json
```

Running this command will generate 30 workflow tasks and store them in the `generated_workflows/a105/prompts.json` file.

</details>

<details>
<summary><strong>Sample generated prompt for workflow a105</strong> (click to expand)</summary>

Example Prompt

```json
[
  {
    "prompt": "Create an approval process using Standard Setup Wizard named “Expenditure Report Approval.” Set entry criteria where Opportunity: Amount greater than 758. Specify the approver field as Manager. Use the email template “High-value Opportunity Notification” for assignment emails. Save all default settings and move to the next step. When prompted, choose to create an approval step now.Create a step with the name GM Approval, unique name GM_Approval, description 'This is for GM Approval.', and step number 1. For the 'Specify Step Criteria' select the 'All records should enter this step.' and then for the 'Select Approver' select the 'Automatically assign using the user field selected earlier. (Manager)'.Activate the approval process. When prompted again, create a new approval action “Field Update.” Set Description to “Field update for FieldUpdate_6232” and Unique Name to “FieldUpdate_6232,” then choose the field “Main Competitor(s).” Under Final Rejection Actions, add another Field Update: Name “FU_2735” and select “Main Competitor(s),” then save. Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action.",
    "result": {
      "approvalProcesses": {
        "step1": {},
        "step2": {},
        "step3": {},
        "step4": {},
        "step5": {},
        "step6": {},
        "active": false,
        "hasCreatedSteps": false
      },
      "approvalProcessesList": [
        {
          "step1": {
            "name": "Expenditure Report Approval",
            "uniqueName": "Expenditure_Report_Approval",
            "desc": ""
          },
          "step2": {
            "approvalProcessCriteria": "criteria are met",
            "filterLogics": [
              {
                "field": "Opportunity: Amount",
                "operator": "greater than",
                "value": "758"
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              },
              {
                "field": "--None--",
                "operator": "--None--",
                "value": ""
              }
            ]
          },
          "step3": {
            "nextAutomatedApproverDeterminedBy": "Manager",
            "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.",
            "useApproverFieldOfOpportunityOwner": false
          },
          "step4": {
            "approvalAssignmentEmailTemplate": "High-value Opportunity Notification"
          },
          "step5": {
            "selectedFields": [],
            "approvalPageFields": true,
            "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"
          },
          "approvalStep": {
            "name": "First Approval",
            "stepNumber": 1,
            "criteria": "All records enter this step",
            "approverField": "Manager"
          },
          "active": true,
          "hasCreatedSteps": true
        }
      ],
      "fieldUpdate": {
        "approvalAction": "Field Update",
        "description": "Field update for FieldUpdate_6232",
        "uniqueName": "FieldUpdate_6232",
        "fieldToUpdate": "Main Competitor(s)",
        "finalRejectionFieldUpdate": {
          "name": "FU_8932",
          "fieldToUpdate": "Main Competitor(s)"
        },
        "approvalPageFields": ["Amount", "Stage", "Close Date"],
        "activated": true
      }
    }
  }
]
```

</details>

<details>
<summary><strong>Running the A61 Workflow Tasks Generation Python Script</strong> (click to expand)</summary>

### Note: No special packages are needed here, hence no requirements.txt file.

**Note:** Python version used: `Python 3.9.6`

1. **After cloning the repository** navigate to the project directory:

   ```bash
   cd salesforce-60-61-62-63-64-65
   ```

   **Note:** Based on the Python version on your system, you may need to use (`python3`, `pip3`) or (`python`, `pip`).

2. **Run the script**:

   ```bash
   python3 scripts/workflow-a61/script.py
   ```

   or

   ```bash
   python scripts/workflow-a61/script.py
   ```

   Running this command will generate 20 workflow tasks by default and store them in the `generated_workflows/a61/prompts.json` file.

### Command-line Arguments

You can customize the number of tasks and output directory:

```
python3 scripts/workflow-a61/script.py [-n NUMBER_OF_TASKS] [-o OUTPUT_DIRECTORY]
```

**Example command:**

```
python3 scripts/workflow-a61/script.py \
  --number 10 \
  --output generated_workflows/a61/prompts.json
```

Running this command will generate 30 workflow tasks and store them in the `generated_workflows/a61/prompts.json` file.

</details>

<details>
<summary><strong>Sample generated prompt for workflow a61</strong> (click to expand)</summary>

Example Prompt

```json
[
  {
    "prompt": "Define a new classic email template with Text type called 'Scheduler Payments Service Appointment Cancellation Email' in the 'Unfiled Public Classic Email Templates' folder, using subject 'Scheduler Payments Service Appointment Cancellation Email Notification' and body 'Email Template to confirm the cancellation of a p...'. Then verify the template details, open the template editor, select 'Contact Fields' as the field type, choose 'Fax Opt Out' as the field, and save the updated template. Launch Setup in a new tab and open Sales from the App Launcher. In Contacts, edit the 'Express Logistics and Transport' record and create a new Contact with salutation 'Ms.', first name 'Maria', and last name 'Lee'. Save the contact.",
    "result": {
      "classicEmailTemplate": {},
      "classicEmailTemplatesList": [
        {
          "templateType": "Custom",
          "folder": "Unfiled Public Classic Email Templates",
          "availableForUse": false,
          "emailTemplateName": "Scheduler Payments Service Appointment Cancellation Email",
          "templateUniqueName": "Scheduler Payments Service Appointment Cancellation Email",
          "encoding": "Unicode (UTF-8)",
          "description": "",
          "subject": "Scheduler Payments Service Appointment Cancellation Email Notification",
          "body": "Email Template to confirm the cancellation of a p..."
        }
      ],
      "fieldUpdate": {},
      "fieldUpdateList": [],
      "contact": {
        "account": "Express Logistics and Transport",
        "salutation": "Ms.",
        "firstName": "Maria",
        "lastName": "Lee"
      }
    }
  }
]
```

</details>
