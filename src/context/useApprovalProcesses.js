import React, { useEffect, useState } from "react";
const defaultStep1Errors = {
  name: "You must enter a value",
  uniqueName:
    "The Unique Name field can only contain underscores and alphanumeric characters. It must be unique, begin with a letter, not include spaces, not end with an underscore, and not contain two consecutive underscores.",
};
const defaultStep2Errors = {
  name: "You must enter a value",
};
const initialFilterLogicData = {
  field: "--None--",
  operator: "--None--",
  value: "",
};
const defaultApprovalStep1Errors = {
  stepNumber: {
    empty: "You must enter a value",
    number: "The step number must be at least 1 but no more than 1",
    negative: "Must be non-negative.",
    invalid: "Invalid number",
  },
};

const useApprovalProcesses = () => {
  const [data, setData] = useState({
    step1: {
      name: "",
      uniqueName: "",
      desc: "",
    },
    step2: {
      approvalProcessCriteria: "criteria are met",
      filterLogics: [
        initialFilterLogicData,
        initialFilterLogicData,
        initialFilterLogicData,
        initialFilterLogicData,
        initialFilterLogicData,
      ],
    },
    step3: {
      nextAutomatedApproverDeterminedBy: "--None--",
      useApproverFieldOfOpportunityOwner: false,
      recordEditabilityProperties:
        "Administrators ONLY can edit records during the approval process.",
    },
    step4: {
      approvalAssignmentEmailTemplate: "",
      emailTemplatesType: "classic",
    },
    step5: {
      selectedFields: [],
      approvalPageFields: false,
      securitySettings: "",
    },
    step6: {
      submitterType: "Owner",
      allowedSubmitters: [],
      pageLayoutSettings: true,
      allowSubmittersToRecallApprovalRequests: false,
    },
    approvalStep: {
      approvalStepOption: "Yes, I'd like to create an approval step now.",
    },
    approvalStep1: {
      approvalProcessName: "",
      approvalProcessUniqueName: "",
      approvalProcessDesc: "",
      approvalProcessStepNumber: "1",
    },
    approvalStep2: {},
    approvalStep3: {},
  });
  const [errors, setErrors] = useState({
    step1: { name: "", uniqueName: "" },
    step2: { filterLogics: [] },
    step5: { selectedFields: "" },
    approvalStep1: {
      name: "",
      uniqueName: "",
      stepNumber: "",
    },
  });
  const setStep1 = (name, value) => {
    data.step1[name] = value;
    setData({ ...data });
  };
  const setStep2 = (name, value) => {
    data.step2[name] = value;
    setData({ ...data });
  };
  const setApprovalStep1 = (name, value) => {
    data.approvalStep1[name] = value;
    setData({ ...data });
  };
  const setStep2FilterLogic = (index) => {};
  const setErrorsStep1 = () => {
    errors.step1 = {};

    if (data.step1.name === "") {
      errors.step1.name = defaultStep1Errors.name;
    } else {
      errors.step1.name = "";
    }
    if (data.step1.uniqueName === "") {
      errors.step1.uniqueName = defaultStep1Errors.uniqueName;
    } else {
      errors.step1.uniqueName = "";
    }

    setErrors({ ...errors });
  };
  const setErrorsStep2 = () => {
    let index = 0;
    errors.step2.filterLogics = [];
    for (const item of data.step2.filterLogics) {
      if (item.field === "--None--") {
        break;
      } else {
        if (item.operator === "--None--") {
          errors.step2.filterLogics[index] = "Error: You must enter a value";
        }
      }
      index++;
    }
    setErrors({ ...errors });
    return errors.step2.filterLogics.length === 0;
  };
  const setErrorsApprovalStep1 = (stepNumber) => {
    errors.approvalStep1 = { ...errors };

    if (data.approvalStep1.approvalProcessName === "") {
      errors.approvalStep1.name = defaultStep1Errors.name;
    } else {
      errors.approvalStep1.name = "";
    }
    if (data.approvalStep1.approvalProcessUniqueName === "") {
      errors.approvalStep1.uniqueName = defaultStep1Errors.uniqueName;
    } else {
      errors.approvalStep1.uniqueName = "";
    }
    if (data.approvalStep1.approvalProcessStepNumber === "") {
      errors.approvalStep1.stepNumber =
        defaultApprovalStep1Errors.stepNumber.empty;
    } else if (isNaN(parseInt(stepNumber))) {
      errors.approvalStep1.stepNumber =
        defaultApprovalStep1Errors.stepNumber.invalid;
    } else if (!isNaN(parseInt(stepNumber)) && stepNumber < 0) {
      errors.approvalStep1.stepNumber =
        defaultApprovalStep1Errors.stepNumber.negative;
    } else if (
      !isNaN(parseInt(stepNumber)) &&
      (parseInt(stepNumber) > 1 || parseInt(stepNumber) === 0)
    ) {
      errors.approvalStep1.stepNumber =
        defaultApprovalStep1Errors.stepNumber.number;
    } else {
      errors.approvalStep1.stepNumber = "";
    }

    setErrors({ ...errors });
  };
  useEffect(() => {
    if (
      data.step3.nextAutomatedApproverDeterminedBy === "--None--" &&
      data.step3.useApproverFieldOfOpportunityOwner
    ) {
      setData((prev) => ({
        ...prev,
        step3: {
          ...prev.step3,
          useApproverFieldOfOpportunityOwner: false,
        },
      }));
    }
  }, [data]);
  return {
    data,
    setData,
    setStep1,
    setStep2,
    errors,
    setErrors,
    setErrorsStep1,
    setErrorsStep2,
    setStep2FilterLogic,
    setErrorsApprovalStep1,
  };
};

export default useApprovalProcesses;
