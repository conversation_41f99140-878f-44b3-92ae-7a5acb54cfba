import React, { createContext, useContext, useState } from "react";

// Create the context
const ObjectManagerContext = createContext();

// Custom hook to use the context
export const useObjectManager = () => {
  return useContext(ObjectManagerContext);
};

// Provider component
export const ObjectManagerProvider = ({ children }) => {
  // Function to find an object by API name
  const findObjectByApiName = (apiName) => {
    if (!apiName) return null;

    // Check if we have opportunity objects in window.allVariableData
    const opportunityObjects = [
      {
        SortLabelSortedAscending: "Opportunity",
        SortAPIName: "Opportunity",
        Type: "Standard Object",
        MasterLabel: "Opportunity",
        PluralLabel: "Opportunities",
        Description: "",
        SortDescription: "",
        SortLastModified: "2023-05-15",
        SortDeployed: "True",
        IsCustom: false,
        EnableReports: true,
        TrackActivities: true,
        TrackFieldHistory: true,
        deploymentStatus: "Deployed",
        helpSettings: "Standard salesforce.com Help Window",
        allowReports: true,
        allowActivities: true,
        trackFieldHistory: true
      },
      {
        SortLabelSortedAscending: "Opportunity Contact Role",
        SortAPIName: "OpportunityContactRole",
        Type: "Standard Object",
        MasterLabel: "Opportunity Contact Role",
        PluralLabel: "Opportunity Contact Roles",
        Description: "Represents the role that a contact plays in an opportunity.",
        SortDescription: "Represents the role that a contact plays in an opportunity.",
        SortLastModified: "2023-05-15",
        SortDeployed: "True",
        IsCustom: false,
        EnableReports: true,
        TrackActivities: true,
        TrackFieldHistory: true,
        deploymentStatus: "Deployed",
        helpSettings: "Standard salesforce.com Help Window",
        allowReports: true,
        allowActivities: true,
        trackFieldHistory: true
      },
      {
        SortLabelSortedAscending: "Opportunity Product",
        SortAPIName: "OpportunityLineItem",
        Type: "Standard Object",
        MasterLabel: "Opportunity Product",
        PluralLabel: "Opportunity Products",
        Description: "Represents a product or service associated with an opportunity.",
        SortDescription: "Represents a product or service associated with an opportunity.",
        SortLastModified: "2023-05-15",
        SortDeployed: "True",
        IsCustom: false,
        EnableReports: true,
        TrackActivities: true,
        TrackFieldHistory: true,
        deploymentStatus: "Deployed",
        helpSettings: "Standard salesforce.com Help Window",
        allowReports: true,
        allowActivities: true,
        trackFieldHistory: true
      }
    ];

    // Find the object by API name
    return opportunityObjects.find(obj => obj.SortAPIName === apiName);
  };

  // Value to be provided by the context
  const value = {
    findObjectByApiName
  };

  return (
    <ObjectManagerContext.Provider value={value}>
      {children}
    </ObjectManagerContext.Provider>
  );
};
