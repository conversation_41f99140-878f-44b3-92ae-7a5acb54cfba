import { Routes, Route, Navigate } from "react-router-dom";

import SetupHomeMain from "./setup_home/SetupHomeMain";
import ObjectManagerMain from "./setup_object_manager/ObjectManagerMain";
import SetupProfilesMain from "./setup_profiles/SetupProfilesMain";
import SellerHomePage from "../pages/SellerHomePage";
import OpportunitiesPage from "../pages/OpportunitiesPage";
import ContactsPage from "../pages/ContactsPage";
import OpportunityDetailPage from "./opportunities/OpportunityDetailPage";
import AccountDetailPage from "../pages/AccountDetailPage";
import NewfieldUpdate from "./NewFieldUpdate/NewFieldUpdate";
import NewEmailAlert from "./NewEmailAlert/NewEmailAlert";
import NewEmailAlertLookup from "./NewEmailAlert/Lookup/page";
import CustomObjectMain from "./setup_object_manager/custom-object/CustomObjectMain";
import CustomObjectContainer from "./setup_object_manager/custom-object/CustomObjectContainer";
import PageLayouts from "./setup_object_manager/custom-object/PageLayouts/PageLayouts";
import CreatedPageLayouts from "./setup_object_manager/custom-object/PageLayouts/CreatedPageLayouts";

import SetupProfilesTable from "./setup_profiles/SetupProfilesTable";
import CloneStandardUserMain from "./setup_profiles/clone_standard_user/CloneStandardUserMain";

import ProfileViewMain from "./setup_profiles/profile_view/ProfileViewMain";
import ProfileEditMain from "./setup_profiles/profile_edit/ProfileEditMain";
import EmailTemplatesMain from "./classic_email_templates/email_template_main";
import SetupApprovalProcessesMain from "./approval_processes/SetupApprovalProcessesMain";
import APHome from "./approval_processes/Main/APHome";
import APStep1 from "./approval_processes/New/Step1/APStep1";
import APStep2 from "./approval_processes/New/Step2/APStep2";
import APNewMain from "./approval_processes/New/APNewMain";
import APStep3 from "./approval_processes/New/Step3/APStep3";
import APStep4 from "./approval_processes/New/Step4/APStep4";
import APLookup from "./approval_processes/New/Step4/APLookup";
import APPicklistLookup from "./approval_processes/New/Step2/APPicklistLookup";
import APStep5 from "./approval_processes/New/Step5/APStep5";
import APStep6 from "./approval_processes/New/Step6/APStep6";
import APNewFinal from "./approval_processes/New/Final/APNewFinal";
import APDetail from "./approval_processes/New/Final/APDetail";
import NewAPStepMain from "./approval_processes/New/NewAPStep/NewAPStepMain";
import NewAPStep1 from "./approval_processes/New/NewAPStep/Step1/NewAPStep1";
import NewAPStep2 from "./approval_processes/New/NewAPStep/Step2/NewAPStep2";
import NewAPStep3 from "./approval_processes/New/NewAPStep/Step3/NewAPStep3";
import NewAPStepFinal from "./approval_processes/New/NewAPStep/newAPStepFinal/newAPStepFinal";

/**
 * MainPage component serves as the central routing hub for the application.
 * It defines the main routes and their corresponding components for the
 * setup sections including home, profiles, and object manager.
 **/

export default function MainPage() {
  //  This is the Center Stage from where routes are defined and you can control the components to show.
  //  Notes:
  //  "Outlet" is used to automatically populate the children, in case, of nested routes.
  //  Please ensure that you have defined the routes in the correct order.
  //  Ensure that "folderNameMain" element is defined in your folder for each route for clean structure.

  return (
    <>
      <Routes>
        <Route path="/setup/home/<USER>" element={<SetupHomeMain />} />
        {/* Used for Workflow a68 */}
        <Route path="/setup/profiles" element={<SetupProfilesMain />}>
          <Route path="/setup/profiles" element={<SetupProfilesTable />} />
          <Route
            path="/setup/profiles/clone_standard_profile"
            element={<CloneStandardUserMain />}
          />
          <Route
            path="/setup/profiles/profile_view"
            element={<ProfileViewMain />}
          />
          <Route
            path="/setup/profiles/profile_edit"
            element={<ProfileEditMain />}
          />
          <Route
            path="/setup/profiles/*"
            element={<Navigate to="/setup/profiles" />}
          />
        </Route>
        {/*  NewFieldUpdate as a top-level route */}
        <Route path="/NewFieldUpdate" element={<NewfieldUpdate />} />
        {/*  NewEmailAlert as a top-level route */}
        <Route path="/NewEmailAlert" element={<NewEmailAlert />} />
        {/*  NewEmailAlert Lookup as a top-level route */}
        <Route path="/NewEmailAlert/Lookup" element={<NewEmailAlertLookup />} />
        <Route path="/setup/objectManager/*" element={<ObjectManagerMain />} />
        <Route path="/setup/objectManager" element={<ObjectManagerMain />} />
        <Route
          path="/setup/objectManager/:apiName"
          element={<CustomObjectMain />}
        >
          <Route index element={<CustomObjectContainer />} />
          <Route path="view" element={<CustomObjectContainer />} />
          <Route path="page-layouts" element={<PageLayouts />} />
          <Route
            path="page-layouts/:layoutId/view"
            element={<CreatedPageLayouts />}
          />
        </Route>
        <Route path="/setup/approval-processes/lookup" element={<APLookup />} />
        <Route
          path="/setup/approval-processes/picklist-lookup"
          element={<APPicklistLookup />}
        />
        <Route
          path="/setup/approval-processes/*"
          element={<SetupApprovalProcessesMain />}
        >
          <Route index element={<APHome />} />
          <Route path="new/*" element={<APNewMain />}>
            <Route path="step1" element={<APStep1 />} />
            <Route path="step2" element={<APStep2 />} />
            <Route path="step3" element={<APStep3 />} />
            <Route path="step4" element={<APStep4 />} />
            <Route path="step5" element={<APStep5 />} />
            <Route path="step6" element={<APStep6 />} />
            <Route path="final" element={<APNewFinal />} />
          </Route>
          <Route path="detail" element={<APDetail />} />
          <Route path="newStep/*" element={<NewAPStepMain />}>
            <Route path="step1" element={<NewAPStep1 />} />
            <Route path="step2" element={<NewAPStep2 />} />
            <Route path="step3" element={<NewAPStep3 />} />
            <Route path="final" element={<NewAPStepFinal />} />
          </Route>
        </Route>
        <Route
          path="/setup/email/classic-templates"
          element={<EmailTemplatesMain />}
        />
        <Route path="*" element={<Navigate to="/setup/home" />} />{" "}
        <Route path="/sales/home" element={<SellerHomePage />} />
        <Route path="/sales/opportunities" element={<OpportunitiesPage />} />
        <Route path="/sales/contacts" element={<ContactsPage />} />
        <Route
          path="/sales/opportunities/:id"
          element={<OpportunityDetailPage />}
        />
        <Route
          path="/sales/accounts/:accountName"
          element={<AccountDetailPage />}
        />
        {/* Fallback Route */}
      </Routes>
    </>
  );
}
