// src/components/contacts/ContactsTable.jsx
import React from "react";
import { useNavigate } from "react-router-dom";

// your headers
const HEADER_TITLES = [
  "Name",
  "Account Name",
  "Account Site",
  "Phone",
  "Email",
  "Contact Owner Alias",
];

// each row’s data
const ROW_DATA = [
  {
    name: "<PERSON><PERSON> green",
    account: "Burlington Textiles Corp of America",
    phone: "(785)2416200",
    alias: "pra",
  },
];

// column‐specific offsets
const accountOffsets = ["5rem", "6.9rem"];
const phoneOffsets = ["15.2rem", "17.7rem"];
const emailDateOffsets = ["2.9rem", "3.9rem"];
const aliasOffsets = ["11.8rem", "6.6rem"];
// ← NEW: per‐row offset for the little “actions” dropdown
const aliasDropdownOffsets = ["27rem", "24rem"];

export default function ContactsTable() {
  const navigate = useNavigate();
  const separatorStyle = {
    width: "1px",
    height: "32px",
    backgroundColor: "#b0b7bd",
    margin: "0 8px",
    alignSelf: "center",
  };
  const dropdownIconStyle = {
    width: "0.875rem",
    height: "0.875rem",
    fill: "currentColor",
    marginLeft: "69px",
    cursor: "pointer",
  };

  return (
    <div style={{ width: "100%", boxSizing: "border-box" }}>
      {/* Header Row */}
      <div
        style={{
          display: "flex",
          alignItems: "center",
          height: "32px",
          backgroundColor: "#f3f3f3",
          padding: "4px 8px",
          fontSize: "13px",
          lineHeight: "19.5px",
          fontFamily:
            '-apple-system, "system-ui", "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
          color: "#181818",
          boxSizing: "border-box",
          borderTop: "1px solid #b0b7bd",
          borderBottom: "1px solid #b0b7bd",
        }}
      >
        {/* Visually‐hidden “Item Number” */}
        <div style={{ minWidth: "60px", padding: "0 8px" }}>
          <span
            style={{
              position: "absolute",
              width: "1px",
              height: "1px",
              clip: "rect(0,0,0,0)",
              overflow: "hidden",
            }}
          >
            Item Number
          </span>
        </div>

        {/* Select‐all checkbox */}
        <div
          style={{
            width: "32px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <div style={{ position: "relative", marginTop: "4px" }}>
            <input
              type="checkbox"
              aria-label="Select all items"
              style={{
                position: "absolute",
                width: "1px",
                height: "1px",
                clip: "rect(0,0,0,0)",
                overflow: "hidden",
                border: 0,
                padding: 0,
                margin: "-1px",
              }}
            />
            <span
              style={{
                width: "16px",
                height: "16px",
                display: "inline-block",
                backgroundColor: "#fff",
                border: "1px solid #747474",
                borderRadius: "2px",
                boxSizing: "border-box",
              }}
            />
          </div>
        </div>

        {/* Dynamic Headers */}
        {HEADER_TITLES.map((title) => (
          <React.Fragment key={title}>
            <div
              style={{
                flex: "none",
                display: "flex",
                alignItems: "center",
                padding: "0 8px",
                minWidth: title === "Account Name" ? "17rem" : "0",
                justifyContent:
                  title === "Account Name" ? "space-between" : undefined,
                boxSizing: "border-box",
              }}
            >
              <span
                className="slds-truncate"
                title={title}
                style={{
                  overflow: "hidden",
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                  color: "#444",
                  cursor: "pointer",
                  fontWeight: 700,
                  font: '700 13px -apple-system,system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol"',
                  lineHeight: "19.5px",
                  textAlign: "left",
                }}
              >
                {title}
              </span>
              <svg
                aria-hidden="true"
                viewBox="0 0 520 520"
                style={dropdownIconStyle}
              >
                <path d="M476 178L271 385c-6 6-16 6-22 0L44 178c-6-6-6-16 0-22l22-22c6-6 16-6 22 0l161 163c6 6 16 6 22 0l161-162c6-6 16-6 22 0l22 22c5 6 5 15 0 21z" />
              </svg>
            </div>
            <div style={separatorStyle} />
          </React.Fragment>
        ))}

        <div style={{ flex: 1 }} />
      </div>

      {/* Data Rows */}
      {ROW_DATA.map((row, rowIndex) => (
        <div
          key={rowIndex}
          style={{
            display: "flex",
            alignItems: "center",
            height: "32px",
            backgroundColor: "#fff",
            padding: "4px 8px",
            fontSize: "13px",
            lineHeight: "19.5px",
            fontFamily:
              '-apple-system, "system-ui", "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
            color: "#181818",
            boxSizing: "border-box",
            borderBottom: "1px solid #d8dde6",
          }}
        >
          {/* Row number */}
          <div
            style={{
              minWidth: "60px",
              padding: "0 8px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <span
              style={{ fontSize: "0.75rem", color: "#444", fontWeight: 400 }}
            >
              {rowIndex + 1}
            </span>
          </div>

          {/* Row checkbox */}
          <div
            style={{
              width: "32px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <div style={{ position: "relative", marginTop: "4px" }}>
              <input
                type="checkbox"
                aria-label={`Select item ${rowIndex + 1}`}
                style={{
                  position: "absolute",
                  width: "1px",
                  height: "1px",
                  clip: "rect(0,0,0,0)",
                  overflow: "hidden",
                  border: 0,
                  padding: 0,
                  margin: "-1px",
                }}
              />
              <span
                style={{
                  width: "16px",
                  height: "16px",
                  display: "inline-block",
                  backgroundColor: "#fff",
                  border: "1px solid #747474",
                  borderRadius: "2px",
                  boxSizing: "border-box",
                }}
              />
            </div>
          </div>

          {/* Cells for each header */}
          {HEADER_TITLES.map((title, colIdx) => {
            // Opportunity Name
            if (title === "Name") {
              return (
                <div
                  key={colIdx}
                  style={{
                    flex: "none",
                    display: "flex",
                    alignItems: "center",
                    padding: "0 8px",
                    minWidth: 0,
                    boxSizing: "border-box",
                  }}
                >
                  <a
                    href="#"
                    style={{
                      color: "rgb(11, 92, 171)",
                      textDecoration: "none",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {row.name}
                  </a>
                </div>
              );
            }

            // Account Name
            if (title === "Account Name") {
              return (
                <div
                  key={colIdx}
                  style={{
                    flex: "none",
                    display: "flex",
                    alignItems: "flex-start",
                    padding: "0 8px",
                    minWidth: 0,
                    boxSizing: "border-box",
                    marginLeft: accountOffsets[rowIndex] || "0rem",
                  }}
                >
                  <a
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      // navigate to your account‐detail page (placeholder path)
                      navigate(
                        `/sales/accounts/${encodeURIComponent(row.account)}`
                      );
                    }}
                    style={{
                      color: "rgb(11, 92, 171)",
                      textDecoration: "none",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {row.account}
                  </a>
                </div>
              );
            }

            // Stage
            if (title === "Phone") {
              return (
                <div
                  key={colIdx}
                  style={{
                    flex: "none",
                    display: "flex",
                    alignItems: "center",
                    padding: "0 8px",
                    minWidth: 0,
                    boxSizing: "border-box",
                    marginLeft: phoneOffsets[rowIndex] || "0rem",
                  }}
                >
                  <span
                    style={{
                      color: "#181818",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {row.phone}
                  </span>
                </div>
              );
            }

            // Close Date
            if (title === "Close Date") {
              return (
                <div
                  key={colIdx}
                  style={{
                    flex: "none",
                    display: "flex",
                    alignItems: "center",
                    padding: "0 8px",
                    minWidth: 0,
                    boxSizing: "border-box",
                    marginLeft: emailDateOffsets[rowIndex] || "0rem",
                  }}
                >
                  <span
                    style={{
                      color: "#181818",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {row.closeDate}
                  </span>
                </div>
              );
            }

            // Contact Owner Alias + its “actions” dropdown
            if (title === "Contact Owner Alias") {
              return (
                <div key={colIdx} style={{ position: "relative" }}>
                  <div
                    style={{
                      flex: "none",
                      display: "flex",
                      alignItems: "center",
                      padding: "0 8px",
                      minWidth: 0,
                      boxSizing: "border-box",
                      marginLeft: aliasOffsets[rowIndex] || "0rem",
                    }}
                  >
                    <span
                      style={{
                        color: "#181818",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}
                    >
                      {row.alias}
                    </span>
                  </div>
                  {/* actions dropdown */}
                  <a
                    href="#"
                    role="button"
                    aria-haspopup="true"
                    aria-expanded="false"
                    style={{
                      position: "absolute",
                      left: aliasDropdownOffsets[rowIndex] || "0rem",
                      top: "50%",
                      transform: "translateY(-50%)",
                      width: "1.25rem",
                      height: "1.25rem",
                      border: "1px solid #c9c9c9",
                      borderRadius: "0.25rem",
                      background: "#fff",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      cursor: "pointer",
                    }}
                  >
                    <svg
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                      style={{
                        width: "0.75rem",
                        height: "0.75rem",
                        fill: "#747474",
                      }}
                    >
                      <path d="M7 10l5 5 5-5z" />
                    </svg>
                  </a>
                </div>
              );
            }

            // everything else placeholder
            return (
              <div
                key={colIdx}
                style={{
                  flex: "none",
                  display: "flex",
                  alignItems: "center",
                  padding: "0 8px",
                  minWidth: 0,
                  boxSizing: "border-box",
                }}
              />
            );
          })}

          <div style={{ flex: 1 }} />
        </div>
      ))}
    </div>
  );
}
