// /src/components/contacts/ContactsTopBar.jsx
import React, { useState } from "react";

export default function ContactsTopBar() {
  const [isNewDialogOpen, setIsNewDialogOpen] = useState(false);
  const openNewDialog = () => setIsNewDialogOpen(true);
  const closeNewDialog = () => setIsNewDialogOpen(false);
  // change this to push the entire right-hand group left/right
  const searchOffset = "0.1rem";

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        width: "100%",
        backgroundColor: "#f3f3f3",
        borderBottom: "1px solid #d8dde6",
        color: "#181818",
        fontSize: "13px",
        lineHeight: "19.5px",
        fontFamily:
          '-apple-system, "system-ui", "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
        padding: "0.5rem 1rem 1.5rem",
        boxSizing: "border-box",
      }}
    >
      {/* ── TOP ROW: Icon + Breadcrumb & Title */}
      <div style={{ display: "flex", alignItems: "center" }}>
        {/* Icon */}
        <div
          style={{
            width: 32,
            height: 32,
            marginRight: 12,
            backgroundColor: "rgb(150, 2, 199)",
            borderRadius: 4,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flexShrink: 0,
          }}
        >
          <img
            src="/assets/images/contact_120.png"
            alt="Opportunity"
            style={{ height: 32, objectFit: "contain" }}
          />
        </div>

        {/* Breadcrumb & Title */}
        <div
          style={{
            flexGrow: 1,
            minWidth: 0,
            display: "flex",
            flexDirection: "column",
            overflow: "hidden",
          }}
        >
          <h1
            style={{
              margin: 0,
              paddingRight: 8,
              fontSize: 13,
              fontWeight: 400,
              lineHeight: "19.5px",
              whiteSpace: "nowrap",
            }}
          >
            Contacts
          </h1>

          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginTop: 4,
            }}
          >
            <h2
              style={{
                margin: 0,
                paddingRight: 4,
                fontSize: 18,
                fontWeight: 700,
                lineHeight: "22.5px",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              Recently Viewed
            </h2>
            {/* List‐view dropdown */}
            <button
              title="Select a List View"
              style={{
                marginLeft: 4,
                width: 24,
                height: 24,
                padding: 0,
                background: "transparent",
                border: "none",
                cursor: "pointer",
              }}
            >
              <svg
                viewBox="0 0 520 520"
                aria-hidden="true"
                style={{ width: 14, height: 14, fill: "#181818" }}
              >
                <path d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z" />
              </svg>
            </button>
            {/* Pin icon */}
            <button
              className="slds-button slds-button_icon slds-button_icon-small slds-button_icon-border-filled"
              title="This list is pinned."
              style={{
                marginLeft: 4,
                width: "1.5rem",
                height: "1.5rem",
                padding: 0,
                border: "1px solid #333",
                borderRadius: "0.25rem",
                background: "#fff",
                cursor: "pointer",
                display: "inline-flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <svg
                className="slds-button__icon"
                aria-hidden="true"
                viewBox="0 0 520 520"
                style={{
                  width: "0.875rem",
                  height: "0.875rem",
                  fill: "currentColor",
                }}
              >
                <path d="M369 237h-5L330 79h9a29 29 0 000-58H181a29 29 0 000 58h9l-33 158h-5a29 29 0 000 58h84v174c0 16 13 30 30 30s30-13 30-30V296h74c16 0 29-13 29-29s-14-30-30-30z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      {/* ── MIDDLE ROW: New / Intelligence / Import / Send List Email / Assign Label */}
      <div
        style={{
          display: "flex",
          justifyContent: "flex-end",
          marginTop: "-3rem", // spacing below top row
        }}
      >
        <ul
          className="slds-button-group"
          style={{
            display: "inline-flex",
            margin: 0,
            padding: 0,
          }}
        >
          {/* New */}
          <li>
            <button
              className="slds-button slds-button_neutral"
              style={{
                border: "1px solid #333",
                borderRight: "none",
                borderRadius: "0.25rem 0 0 0.25rem",
              }}
              onClick={openNewDialog}
            >
              New
            </button>
          </li>

          {/* Intelligence View */}
          <li>
            <button
              className="slds-button slds-button_neutral"
              style={{
                marginLeft: "-1px",
                border: "1px solid #333",
                borderRight: "none",
                borderRadius: 0,
              }}
            >
              Intelligence View
            </button>
          </li>

          {/* Import */}
          <li>
            <button
              className="slds-button slds-button_neutral"
              style={{
                marginLeft: "-1px",
                border: "1px solid #333",
                borderRight: "none",
                borderRadius: 0,
              }}
            >
              Import
            </button>
          </li>

          {/* Send List Email */}
          <li>
            <button
              className="slds-button slds-button_neutral"
              style={{
                marginLeft: "-1px",
                border: "1px solid #333",
                borderRight: "none",
                borderRadius: 0,
              }}
            >
              Send List Email
            </button>
          </li>

          {/* Assign Label */}
          <li>
            <button
              className="slds-button slds-button_neutral"
              style={{
                marginLeft: "-1px",
                border: "1px solid #333",
                borderRadius: "0 0.25rem 0.25rem 0",
              }}
            >
              Assign Label
            </button>
          </li>
        </ul>
      </div>

      {/* ── BOTTOM ROW: Status (left) + Controls (right) */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "flex-end",
          width: "100%",
          marginTop: "0.75rem",
        }}
      >
        {/* Status text on extreme left */}
        <div style={{ fontSize: "0.75rem", color: "#181818" }}>
          <span style={{ marginRight: 4 }}>1 items •</span>
          <span>Updated 16 minutes ago</span>
        </div>

        {/* All your right‐side buttons/search group */}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            marginRight: searchOffset,
          }}
        >
          {/* New / Assign Label */}

          {/* Search box */}
          <div
            style={{
              position: "relative",
              width: "15rem",
              marginLeft: "1rem",
            }}
          >
            <input
              type="search"
              className="slds-input slds-input-has-icon slds-input-has-icon_left"
              placeholder="Search this list..."
              style={{
                paddingLeft: "2rem",
                width: "100%",
                border: "1px solid #333",
                borderRadius: "4px",
                height: "2rem",
                boxSizing: "border-box",
              }}
            />
            <svg
              className="slds-input__icon slds-input__icon_left slds-icon-text-default"
              aria-hidden="true"
              viewBox="0 0 520 520"
              style={{
                position: "absolute",
                left: "0.75rem",
                top: "50%",
                transform: "translateY(-50%)",
                width: "0.875rem",
                height: "0.875rem",
                fill: "rgb(174,174,174)",
              }}
            >
              <path d="M496 453L362 320a189 189 0 10-340-92 190 190 0 00298 135l133 133a14 14 0 0021 0l21-21a17 17 0 001-22zM210 338a129 129 0 11130-130 129 129 0 01-130 130z" />
            </svg>
          </div>

          {/* Gear dropdown */}
          <button
            aria-expanded="false"
            aria-haspopup="true"
            title="List View Controls"
            style={{
              marginLeft: "0.5rem",
              backgroundColor: "#fff",
              color: "rgb(53, 93, 150)",
              cursor: "pointer",
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              height: "2rem",
              padding: "0 0.5rem",
              border: "1px solid #333",
              borderRadius: "4px",
              boxSizing: "border-box",
            }}
          >
            {/* Correct gear icon */}
            <svg
              viewBox="0 0 520 520"
              aria-hidden="true"
              style={{
                width: "0.875rem",
                height: "0.875rem",
                fill: "currentColor",
                verticalAlign: "middle",
              }}
            >
              <path d="M261 191c-39 0-70 31-70 70s31 70 70 70 70-31 70-70-31-70-70-70zm210 133l-37-31a195 195 0 000-68l37-31c12-10 16-28 8-42l-16-28a34 34 0 00-40-14l-46 17a168 168 0 00-59-34l-8-47c-3-16-17-25-33-25h-32c-16 0-30 9-33 25l-8 46a180 180 0 00-60 34l-46-17-11-2c-12 0-23 6-29 16l-16 28c-8 14-5 32 8 42l37 31a195 195 0 000 68l-37 31a34 34 0 00-8 42l16 28a34 34 0 0040 14l46-17c18 16 38 27 59 34l8 48a33 33 0 0033 27h32c16 0 30-12 33-28l8-48a170 170 0 0062-37l43 17 12 2c12 0 23-6 29-16l15-26c9-11 5-29-7-39zm-210 47c-61 0-110-49-110-110s49-110 110-110 110 49 110 110-49 110-110 110z" />
            </svg>

            {/* chevron */}
            <svg
              viewBox="0 0 520 520"
              aria-hidden="true"
              style={{
                width: "0.5rem",
                height: "0.5rem",
                fill: "currentColor",
                marginLeft: "0.25rem",
                verticalAlign: "middle",
              }}
            >
              <path d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z" />
            </svg>
          </button>

          {/* Second dropdown */}
          <button
            title="Select list display"
            style={{
              marginLeft: "0.5rem",
              backgroundColor: "#fff",
              color: "#181818",
              border: "1px solid #333",
              borderRadius: "4px",
              padding: "0 8px",
              width: 44,
              height: 32,
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
          >
            <svg
              viewBox="0 0 520 520"
              aria-hidden="true"
              style={{ width: "14px", height: "14px", fill: "#181818" }}
            >
              <path d="M465 20H55c-8 0-15 7-15 15v50c0 8 7 15 15 15h410c8 0 15-7 15-15V35c0-8-7-15-15-15zM145 140H55c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15zm160 0h-90c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15zm160 0h-90c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15zM145 240H55c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15zm160 0h-90c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15zm160 0h-90c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15zM145 340H55c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15zm160 0h-90c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15zm160 0h-90c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15zM145 440H55c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15zm160 0h-90c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15zm160 0h-90c-8 0-15 7-15 15v30c0 8 7 15 15 15h90c8 0 15-7 15-15v-30c0-8-7-15-15-15z" />
            </svg>
            <svg
              viewBox="0 0 520 520"
              aria-hidden="true"
              style={{
                width: "8px",
                height: "8px",
                marginLeft: "4px",
                fill: "#181818",
              }}
            >
              <path d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z" />
            </svg>
          </button>

          {/* Refresh */}
          <button
            title="Refresh"
            style={{
              marginLeft: "0.5rem",
              backgroundColor: "#fff",
              color: "#181818",
              border: "1px solid #333",
              borderRadius: "4px",
              width: "32px",
              height: "32px",
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
          >
            <svg
              aria-hidden="true"
              focusable="false"
              viewBox="0 0 520 520"
              style={{
                width: "0.875rem",
                height: "0.875rem",
                fill: "currentColor",
              }}
            >
              <path d="M465 40h-30c-8 0-15 7-15 15v70c0 9-5 13-12 7l-10-10a210 210 0 10-12 309c7-6 7-16 1-22l-21-21c-5-5-14-6-20-1a152 152 0 01-172 14 152 152 0 0177-281 150 150 0 01118 58c3 8-4 12-13 12h-70c-8 0-15 7-15 15v31c0 8 6 14 14 14h183c7 0 13-6 13-13V55c-1-8-8-15-16-15z" />
            </svg>
            <span
              className="slds-assistive-text"
              style={{
                position: "absolute",
                width: "1px",
                height: "1px",
                overflow: "hidden",
                clip: "rect(0,0,0,0)",
                margin: "-1px",
              }}
            >
              Refresh
            </span>
          </button>

          {/* Edit List */}
          <button
            title="Edit List"
            style={{
              marginLeft: "0.5rem",
              backgroundColor: "#fff",
              color: "#181818",
              border: "1px solid #333",
              borderRadius: "4px",
              width: "32px",
              height: "32px",
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
          >
            <svg
              aria-hidden="true"
              focusable="false"
              viewBox="0 0 520 520"
              style={{
                width: "0.875rem",
                height: "0.875rem",
                fill: "currentColor",
              }}
            >
              <path d="M95 334l89 89c4 4 10 4 14 0l222-223c4-4 4-10 0-14l-88-88a10 10 0 00-14 0L95 321c-4 4-4 10 0 13zM361 57a10 10 0 000 14l88 88c4 4 10 4 14 0l25-25a38 38 0 000-55l-47-47a40 40 0 00-57 0zM21 482c-2 10 7 19 17 17l109-26c4-1 7-3 9-5l2-2c2-2 3-9-1-13l-90-90c-4-4-11-3-13-1l-2 2a20 20 0 00-5 9z" />
            </svg>
            <span
              className="slds-assistive-text"
              style={{
                position: "absolute",
                width: "1px",
                height: "1px",
                overflow: "hidden",
                clip: "rect(0,0,0,0)",
                margin: "-1px",
              }}
            >
              Edit List
            </span>
          </button>

          {/* Charts & Filters group (disabled) */}
          <div
            style={{
              display: "flex",
              marginLeft: "0.5rem",
              backgroundColor: "#fff",
              borderRadius: "4px",
              overflow: "hidden",
              height: "32px",
              border: "1px solid #c9c9c9",
            }}
          >
            <button
              disabled
              style={{
                border: "none",
                width: "32px",
                height: "32px",
                display: "inline-flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "#fff",
                cursor: "not-allowed",
              }}
            >
              <svg
                viewBox="0 0 520 520"
                style={{ width: "14px", height: "14px", fill: "#c9c9c9" }}
                aria-hidden="true"
              >
                <path d="M455 234L250 347a20 20 0 01-30-18V84c0-10-10-18-19-15a220 220 0 10276 175c-2-10-13-15-22-10zm-178 46l197-105c12-6 16-22 8-33A302 302 0 00287 22c-14-3-27 8-27 22v226c0 9 9 14 17 10z" />
              </svg>
            </button>
            <button
              disabled
              style={{
                border: "none",
                width: "32px",
                height: "32px",
                display: "inline-flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "#fff",
                cursor: "not-allowed",
                borderLeft: "1px solid #c9c9c9",
              }}
            >
              <svg
                viewBox="0 0 520 520"
                style={{ width: "14px", height: "14px", fill: "#c9c9c9" }}
                aria-hidden="true"
              >
                <path d="M483 40H39c-15 0-22 17-13 28l194 227c6 7 9 17 9 26v144c0 8 8 15 16 15h30c8 0 14-7 14-15V321c0-10 4-19 11-26L496 68c9-11 2-28-13-28z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
