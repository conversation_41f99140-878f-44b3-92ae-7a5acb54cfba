import React, { useState } from "react";

const APPicklistLookup = () => {
  const [selectedValues, setSelectedValues] = useState([]);

  // Get the rowIndex from URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const rowIndex = parseInt(urlParams.get("rowIndex") || "0", 10);

  const statusOptions = [
    { value: "Submitted", label: "Submitted" },
    { value: "Approved", label: "Approved" },
    { value: "Rejected", label: "Rejected" },
  ];

  const handleCheckboxChange = (value) => {
    setSelectedValues((prev) => {
      if (prev.includes(value)) {
        return prev.filter((v) => v !== value);
      } else {
        return [...prev, value];
      }
    });
  };

  const handleInsertSelected = () => {
    if (window.opener && !window.opener.closed) {
      const selectedText = selectedValues.join(", ");
      // Send the selected values back to the parent window with rowIndex
      window.opener.postMessage(
        {
          type: "PICKLIST_VALUES_SELECTED",
          values: selectedText,
          rowIndex: rowIndex,
        },
        "*"
      );
      window.close();
    }
  };

  const handleDeselectAll = () => {
    setSelectedValues([]);
  };

  return (
    <>
      <div
        style={{
          minHeight: "100vh",
          padding: 0,
          margin: 0,
          display: "relative",
          overflow: "hidden",
        }}
      >
        <div style={{ height: "20vh", display: "fixed" }}>
          <Component1 />
          <Component2
            selectedValues={selectedValues}
            handleInsertSelected={handleInsertSelected}
            handleDeselectAll={handleDeselectAll}
          />
        </div>
        <div style={{ height: "80vh", overflow: "auto" }}>
          <Component3
            statusOptions={statusOptions}
            selectedValues={selectedValues}
            handleCheckboxChange={handleCheckboxChange}
            handleInsertSelected={handleInsertSelected}
          />
        </div>
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
            body {
            background-position: left top;
            font-size: 75%;
            font-family: Arial, Helvetica, sans-serif;
            color: rgb(34, 34, 34);
            background: url("/assets/images/lookup_bg.png") repeat-x rgb(255, 255, 255);
            padding: 0px;
            background-repeat: repeat-x;
            background-color: rgb(255, 255, 255);
            }
            `,
        }}
      />
    </>
  );
};

export default APPicklistLookup;

function Component1() {
  return (
    <>
      <div
        className="bPageTitle"
        style={{
          margin: "0px",
          marginBottom: "0px",
          padding: "0 15px",
        }}
      >
        <div
          className="ptBody secondaryPalette"
          style={{
            overflow: "hidden",
            width: "100%",
            borderColor: "rgb(3, 45, 96)",
            padding: "0px",
            backgroundColor: "transparent",
            color: "rgb(0, 0, 0)",
            paddingTop: "0px",
            paddingBottom: "0px",
          }}
        >
          <div
            className="content"
            style={{
              cssFloat: "left",
              verticalAlign: "middle",
              width: "70%",
              paddingLeft: "0px",
            }}
          >
            <img
              className="pageTitleIcon"
              src="/assets/downloaded/s.gif"
              style={{
                border: "0px",
                display: "inline",
                cssFloat: "left",
                marginRight: "5px",
                backgroundPosition: "0px -1202px",
                backgroundImage: 'url("/assets/downloaded/master.png")',
                width: "32px",
                height: "32px",
                marginTop: "5px",
              }}
            />
            <h1
              style={{
                fontWeight: "bold",
                fontFamily: "Arial, Helvetica, sans-serif",
                display: "block",
                margin: "8px 0px 4px",
                fontSize: "1.8em",
                color: "rgb(51, 52, 53)",
              }}
            >
              Lookup
            </h1>
          </div>
        </div>
      </div>
    </>
  );
}

function Component2({
  selectedValues,
  handleInsertSelected,
  handleDeselectAll,
}) {
  return (
    <>
      <div
        className="pBody"
        style={{
          fontWeight: "normal",
          padding: "0 15px",
          paddingBottom: "5px",
        }}
      >
        <div
          className="bDescription"
          style={{
            textAlign: "left",
            padding: "0px",
            fontWeight: "normal",
            paddingTop: "5px",
            paddingBottom: "10px",
            margin: "0px",
            fontSize: "0.9em",
            paddingLeft: "0px",
          }}
        >
          Select the picklist values to add below.
        </div>
        <div style={{ marginBottom: "10px" }}>
          <input
            className="btn"
            name="insert"
            type="button"
            value="Insert Selected"
            onClick={handleInsertSelected}
            disabled={selectedValues.length === 0}
            title="Insert Selected"
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: selectedValues.length === 0 ? "default" : "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                selectedValues.length === 0
                  ? "rgb(200, 200, 200)"
                  : "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              background:
                selectedValues.length === 0
                  ? "rgb(200, 200, 200)"
                  : 'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
              borderRadius: "3px",
              color:
                selectedValues.length === 0
                  ? "rgb(100, 100, 100)"
                  : "rgb(51, 51, 51)",
              backgroundImage:
                selectedValues.length === 0
                  ? "none"
                  : 'url("/assets/downloaded/btn_sprite.png")',
              backgroundRepeat: "repeat-x",
              fontWeight: "bold",
              fontSize: "0.9em",
              padding: "4px 8px",
              marginRight: "10px",
            }}
          />
          <a
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handleDeselectAll();
            }}
            style={{
              color: "rgb(1, 91, 167)",
              textDecoration: "underline",
              fontSize: "0.9em",
            }}
          >
            Deselect all
          </a>
        </div>
      </div>
    </>
  );
}

function Component3({
  statusOptions,
  selectedValues,
  handleCheckboxChange,
  handleInsertSelected,
}) {
  return (
    <>
      <form
        id="new"
        name="new"
        acceptCharset="UTF-8"
        style={{
          margin: "0px",
          marginTop: "0px",
          padding: "0 15px",
          paddingBottom: "10px",
        }}
      >
        <div
          className="lookup"
          style={{
            padding: "0px",
            paddingBottom: "0px",
            paddingLeft: "0px",
            width: "100%",
            display: "table",
            overflow: "auto",
          }}
        >
          <div className="listRelatedObject lookupBlock">
            <div
              className="bPageBlock brandSecondaryBrd secondaryPalette"
              style={{
                backgroundPosition: "left bottom",
                backgroundRepeat: "no-repeat",
                clear: "both",
                margin: "0px",
                padding: "0px",
                marginBottom: "10px",
                paddingRight: "0px",
                paddingBottom: "0px",
                backgroundImage: "none",
                borderColor: "rgb(3, 45, 96)",
                borderTop: "3px solid rgb(3, 45, 96)",
                borderTopWidth: "3px",
                borderBottom: "1px solid rgb(234, 234, 234)",
                borderLeft: "1px solid rgb(234, 234, 234)",
                borderRight: "1px solid rgb(234, 234, 234)",
                borderRadius: "4px",
                backgroundColor: "rgb(248, 248, 248)",
                borderBottomWidth: "1px",
                borderBottomStyle: "solid",
                borderLeftColor: "rgb(224, 227, 229)",
                borderRightColor: "rgb(224, 227, 229)",
                borderBottomColor: "rgb(224, 227, 229)",
              }}
            >
              <div
                className="pbHeader"
                style={{
                  margin: "0px",
                  backgroundColor: "transparent",
                  display: "none",
                  borderBottom: "none",
                  padding: "0px",
                  paddingBottom: "0px",
                  border: "0px",
                }}
              />
              <div
                className="pbBody"
                style={{
                  color: "rgb(0, 0, 0)",
                  backgroundColor: "transparent",
                  backgroundImage: "none",
                  padding: "0px",
                  margin: "0px",
                  marginRight: "0px",
                }}
              >
                <table
                  className="list"
                  cellPadding="0"
                  cellSpacing="0"
                  style={{
                    width: "100%",
                    borderTop: "1px solid rgb(224, 227, 229)",
                    backgroundColor: "rgb(255, 255, 255)",
                    border: "1px solid rgb(224, 227, 229)",
                  }}
                >
                  <tbody>
                    <tr className="headerRow">
                      <th
                        className="zen-deemphasize"
                        scope="col"
                        style={{
                          textAlign: "left",
                          borderBottom: "2px solid rgb(204, 204, 204)",
                          whiteSpace: "nowrap",
                          border: "1px solid rgb(237, 237, 237)",
                          background: "rgb(242, 243, 243)",
                          borderWidth: "0px 0px 1px 1px",
                          borderColor: "rgb(224, 227, 229)",
                          padding: "5px 2px 4px 5px",
                          color: "rgb(0, 0, 0)",
                          fontSize: "0.9em",
                          fontWeight: "bold",
                          borderLeftWidth: "0px",
                          width: "50px",
                        }}
                      >
                        Select
                      </th>
                      <th
                        className="zen-deemphasize"
                        scope="col"
                        style={{
                          textAlign: "left",
                          borderBottom: "2px solid rgb(204, 204, 204)",
                          whiteSpace: "nowrap",
                          border: "1px solid rgb(237, 237, 237)",
                          background: "rgb(242, 243, 243)",
                          borderWidth: "0px 0px 1px 1px",
                          borderColor: "rgb(224, 227, 229)",
                          padding: "5px 2px 4px 5px",
                          color: "rgb(0, 0, 0)",
                          fontSize: "0.9em",
                          fontWeight: "bold",
                        }}
                      >
                        Value
                      </th>
                    </tr>
                    {statusOptions.map((option, index) => (
                      <tr
                        key={option.value}
                        className={
                          index % 2 === 0 ? "dataRow even first" : "dataRow odd"
                        }
                        style={{
                          backgroundColor:
                            index % 2 === 0
                              ? "rgb(255, 255, 255)"
                              : "rgb(243, 243, 243)",
                        }}
                      >
                        <td
                          style={{
                            padding: "4px 2px 4px 5px",
                            borderBottom: "1px solid rgb(224, 227, 229)",
                            borderRight: "1px solid rgb(224, 227, 229)",
                            textAlign: "center",
                            verticalAlign: "top",
                            fontSize: "0.9em",
                          }}
                        >
                          <input
                            type="checkbox"
                            checked={selectedValues.includes(option.value)}
                            onChange={() => handleCheckboxChange(option.value)}
                            style={{
                              margin: "0",
                              cursor: "pointer",
                            }}
                          />
                        </td>
                        <td
                          style={{
                            padding: "4px 2px 4px 5px",
                            borderBottom: "1px solid rgb(224, 227, 229)",
                            borderRight: "1px solid rgb(224, 227, 229)",
                            fontSize: "0.9em",
                            color: "rgb(34, 34, 34)",
                            verticalAlign: "top",
                          }}
                        >
                          {option.label}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div
                className="pbFooter secondaryPalette"
                style={{
                  backgroundPosition: "right bottom",
                  height: "9px",
                  width: "9px",
                  cssFloat: "right",
                  backgroundImage:
                    'url("/assets/downloaded/bgPageBlockRight.gif")',
                  backgroundRepeat: "repeat-x",
                  borderColor: "rgb(3, 45, 96)",
                  backgroundColor: "rgb(3, 45, 96)",
                  display: "none",
                }}
              >
                <div className="bg" />
              </div>
            </div>
          </div>
          <div style={{ marginBottom: "15px", textAlign: "left" }}>
            <input
              className="btn"
              name="insert"
              type="button"
              value="Insert Selected"
              onClick={handleInsertSelected}
              disabled={selectedValues.length === 0}
              title="Insert Selected"
              style={{
                backgroundPosition: "left top",
                borderRight: "1px solid rgb(92, 93, 97)",
                borderBottom: "1px solid rgb(92, 93, 97)",
                borderTop: "none",
                borderLeft: "none",
                cursor: selectedValues.length === 0 ? "default" : "pointer",
                display: "inline",
                fontFamily: "Arial, Helvetica, sans-serif",
                margin: "1px",
                borderWidth: "1px",
                borderStyle: "solid",
                borderColor:
                  selectedValues.length === 0
                    ? "rgb(200, 200, 200)"
                    : "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                borderImage: "initial",
                background:
                  selectedValues.length === 0
                    ? "rgb(200, 200, 200)"
                    : 'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                borderRadius: "3px",
                color:
                  selectedValues.length === 0
                    ? "rgb(100, 100, 100)"
                    : "rgb(51, 51, 51)",
                backgroundImage:
                  selectedValues.length === 0
                    ? "none"
                    : 'url("/assets/downloaded/btn_sprite.png")',
                backgroundRepeat: "repeat-x",
                fontWeight: "bold",
                fontSize: "0.9em",
                padding: "4px 8px",
              }}
            />
          </div>
          <div
            className="footer"
            style={{
              borderTop: "2px solid rgb(217, 217, 217)",
              marginTop: "20px",
              paddingTop: "0.5em",
              color: "rgb(135, 135, 135)",
              border: "0px",
              textAlign: "center",
              fontSize: "0.8em",
            }}
          >
            Copyright © 2000-2025 salesforce.com, inc. All rights reserved.
          </div>
        </div>
      </form>
    </>
  );
}
