import React, { useContext, useEffect } from "react";
import fieldOptions from "../../../data/APFieldOptions.json";
import operatorOptions from "../../../data/APOperatorOptions.json";
import { GlobalContext } from "../../../../../context/GlobalContext";
const APEntryFIeldsRow = ({ isLast, index }) => {
  const { approvalProcessData } = useContext(GlobalContext);
  const { data, setData, errors } = approvalProcessData;
  const { step2 } = data;
  useEffect(() => {
    step2.filterLogics[index] = {
      field: "--None--",
      operator: "--None--",
      value: "",
    };
    setData((prev) => ({ ...prev, step2 }));
  }, []);
  const handleChange = (e) => {
    const { name, value } = e.target;

    step2.filterLogics[index] = {
      ...step2.filterLogics[index],
      [name]: value,
    };
    setData((prev) => ({ ...prev, step2 }));
  };

  const handleLookupClick = (event) => {
    event.preventDefault();
    event.stopPropagation();
    const lookupWindow = window.open(
      `/setup/approval-processes/picklist-lookup?rowIndex=${index}`,
      "PicklistLookup",
      "width=780,height=500,resizable=yes,scrollbars=yes"
    );
    lookupWindow?.focus();
  };

  useEffect(() => {
    const handleMessage = (event) => {
      if (
        event.data.type === "PICKLIST_VALUES_SELECTED" &&
        event.data.rowIndex === index
      ) {
        const selectedValues = event.data.values;
        step2.filterLogics[index] = {
          ...step2.filterLogics[index],
          value: selectedValues,
        };
        setData((prev) => ({ ...prev, step2 }));
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, [index, step2, setData]);
  return (
    <tr
      id="frow1"
      style={{
        border: "0px solid rgb(229, 231, 235)",
        boxSizing: "border-box",
        borderColor: "hsl(0 0% 85%)",
        display: "flex",
        gap: "7px",
      }}
    >
      <td
        style={{
          border: "0px solid rgb(229, 231, 235)",
          boxSizing: "border-box",
          margin: "0px",
          width: "40%",
          fontFamily: "Arial, Helvetica, sans-serif",
          color: "black",
          verticalAlign: "top",
          borderColor: "rgb(236, 236, 236)",
          borderBottom: "none",
          borderBottomStyle: "none",
          borderBottomWidth: "0px",
        }}
      >
        <select
          id="critfld1"
          name="field"
          title="Field 1"
          value={step2.filterLogics[index]?.field}
          onChange={handleChange}
          style={{
            border: "1px solid black",
            borderRadius: "3px",
            boxSizing: "border-box",
            borderColor: "hsl(0 0% 85%)",
            margin: "0px",
            padding: "2px",
            height: "20px",
            width: "100%",
            fontFamily: "inherit",
            fontFeatureSettings: "inherit",
            fontVariationSettings: "inherit",
            fontSize: "100%",
            fontWeight: "inherit",
            lineHeight: "inherit",
            letterSpacing: "inherit",
            textTransform: "none",
            color: "black",
            verticalAlign: "middle",
            marginRight: "0.25em",
          }}
        >
          {fieldOptions.map((option, index) => (
            <option key={index + 1} value={option}>
              {option}
            </option>
          ))}
        </select>
      </td>
      <td
        style={{
          border: "0px solid rgb(229, 231, 235)",
          boxSizing: "border-box",
          margin: "0px",
          width: "15%",
          fontFamily: "Arial, Helvetica, sans-serif",
          color: "black",
          verticalAlign: "top",
          borderColor: "rgb(236, 236, 236)",
          borderBottom: "none",
          borderBottomStyle: "none",
          borderBottomWidth: "0px",
        }}
      >
        <select
          id="critop1"
          name="operator"
          title="Operator 1"
          value={step2.filterLogics[index]?.operator}
          onChange={handleChange}
          style={{
            border: "1px solid black",
            borderRadius: "3px",
            boxSizing: "border-box",
            borderColor: "hsl(0 0% 85%)",
            margin: "0px",
            padding: "2px",
            width: "100%",
            height: "20px",
            fontFamily: "inherit",
            fontFeatureSettings: "inherit",
            fontVariationSettings: "inherit",
            fontSize: "100%",
            fontWeight: "inherit",
            lineHeight: "inherit",
            letterSpacing: "inherit",
            textTransform: "none",
            verticalAlign: "middle",
            marginRight: "0.25em",
            color: "black",
          }}
        >
          {operatorOptions.map((option, index) => (
            <option key={index + 1} value={option}>
              {option}
            </option>
          ))}
        </select>
      </td>
      <td
        style={{
          border: "0px solid rgb(229, 231, 235)",
          boxSizing: "border-box",
          margin: "0px",
          width: "15%",
          fontFamily: "Arial, Helvetica, sans-serif",
          color: "black",
          verticalAlign: "top",
          borderColor: "rgb(236, 236, 236)",
          borderBottom: "none",
          borderBottomStyle: "none",
          borderBottomWidth: "0px",
        }}
      >
        <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
          <input
            id="pVAL1"
            name="value"
            type="text"
            maxLength="255"
            size={20}
            title="Value 1"
            value={step2.filterLogics[index].value}
            onChange={handleChange}
            style={{
              border: "1px solid black",
              borderRadius: "3px",
              boxSizing: "border-box",
              borderColor: "hsl(0 0% 85%)",
              margin: "0px",
              padding: "2px",
              height: "20px",
              width: "100%",
              fontFamily: "inherit",
              fontFeatureSettings: "inherit",
              fontVariationSettings: "inherit",
              fontSize: "100%",
              fontWeight: "inherit",
              lineHeight: "inherit",
              letterSpacing: "inherit",
              color: "black",
              paddingTop: "0px",
              verticalAlign: "middle",
              marginRight: "0.25em",
            }}
          />
          {step2.filterLogics[index]?.field ===
            "Job Application: Application Status" && (
            <div
              onClick={handleLookupClick}
              style={{
                backgroundImage: 'url("/assets/images/lookup.gif")',
                width: "20px",
                height: "20px",
                cursor: "pointer",
                flexShrink: 0,
              }}
            />
          )}
        </div>
      </td>
      <td
        style={{
          border: "0px solid rgb(229, 231, 235)",
          boxSizing: "border-box",
          margin: "0px",
          width: "30%",
          fontFamily: "Arial, Helvetica, sans-serif",
          color: "black",
          verticalAlign: "top",
          borderColor: "rgb(236, 236, 236)",
          borderBottom: "none",
          borderBottomStyle: "none",
          borderBottomWidth: "0px",
        }}
      >
        <span
          id="and1"
          className="textBox"
          style={{
            border: "0px solid rgb(229, 231, 235)",
            boxSizing: "border-box",
            borderColor: "hsl(0 0% 85%)",
            margin: "1px 7px 1px 1px",
            width: "100%",
            visibility: "visible",
          }}
        >
          <span style={{ width: "10%", display: "inline-block" }}>
            {isLast ? <span>&nbsp;</span> : "AND"}
          </span>
        </span>
        {errors.step2.filterLogics[index] && (
          <span style={{ color: "#ba0517" }}>
            {errors.step2.filterLogics[index]}
          </span>
        )}
      </td>
    </tr>
  );
};

export default APEntryFIeldsRow;
