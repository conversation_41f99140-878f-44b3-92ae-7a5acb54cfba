import React, { useContext, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "../../../../context/GlobalContext";
import APStep3Checkboxes from "./APStep3Checkboxes";

const APStep3 = () => {
  const { approvalProcessData, setAllVariableData, saveToLocalStorage } =
    useContext(GlobalContext);
  useEffect(() => {
    approvalProcessData.setData((prev) => ({
      ...prev,
      step3: {
        nextAutomatedApproverDeterminedBy: "--None--",
        useApproverFieldOfOpportunityOwner: false,
        recordEditabilityProperties:
          "Administrators ONLY can edit records during the approval process.",
      },
    }));
  }, []);
  useEffect(() => {
    setAllVariableData((prev) => {
      prev.approvalProcesses = {
        ...prev.approvalProcesses,
        step3: approvalProcessData.data.step3,
        step4: {},
        step5: {},
        step6: {},
      };
      saveToLocalStorage("approvalProcesses", prev.approvalProcesses);
      return { ...prev };
    });
  }, [approvalProcessData.data]);
  return (
    <>
      <NavigatonButtons />
      <Component1 />
      <Component2 />
      <NavigatonButtons />
      <style
        dangerouslySetInnerHTML={{
          __html: `
                    html {
                    border: 0px solid rgb(229, 231, 235);
                    box-sizing: border-box;
                    border-color: hsl(0 0% 85%);
                    line-height: 1.5;
                    text-size-adjust: 100%;
                    tab-size: 4;
                    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                    font-feature-settings: normal;
                    font-variation-settings: normal;
                    -webkit-tap-highlight-color: transparent;
                    overflow: auto;
                    }

                    body {
                    border: 0px solid rgb(229, 231, 235);
                    box-sizing: border-box;
                    border-color: hsl(0 0% 85%);
                    line-height: inherit;
                    min-height: 100vh;
                    margin: 0px;
                    background-position: left top;
                    background-repeat: repeat-x;
                    color: rgb(34, 34, 34);                    
                    font-size: 71%;
                    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                    -webkit-font-smoothing: antialiased;
                    }
                `,
        }}
      />
    </>
  );
};

export default APStep3;

function NavigatonButtons() {
  const navigate = useNavigate();
  const handleNext = () => {
    navigate("/setup/approval-processes/new/step4");
  };
  const handleCancel = () => {
    navigate("/setup/approval-processes");
  };
  const handlePrevious = () => {
    navigate("/setup/approval-processes/new/step2");
  };
  return (
    <>
      <div
        className="pbWizardHeader"
        style={{
          overflow: "hidden",
          height: "1%",
          borderBottom: "1px solid rgb(219, 219, 219)",
          marginBottom: "0px",
          color: "rgb(0, 0, 0)",
          backgroundColor: "rgb(248, 248, 248)",
        }}
      >
        <div
          className="pbTopButtons"
          style={{
            color: "rgb(51, 51, 51)",
            cssFloat: "right",
            padding: "8px 12px",
            margin: "0px",
            whiteSpace: "nowrap",
          }}
        >
          <button
            className="btn"
            onClick={handlePrevious}
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              background:
                'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
              borderRadius: "3px",
              color: "rgb(51, 51, 51)",
              backgroundImage: 'url("/assets/downloaded/btn_sprite.png")',
              backgroundRepeat: "repeat-x",
              fontWeight: "bold",
              fontSize: "0.9em",
              padding: "4px 3px",
              paddingTop: "4px",
            }}
          >
            Previous
          </button>
          <button
            className="btn"
            onClick={handleNext}
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              background:
                'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
              borderRadius: "3px",
              color: "rgb(51, 51, 51)",
              backgroundImage: 'url("/assets/downloaded/btn_sprite.png")',
              backgroundRepeat: "repeat-x",
              fontWeight: "bold",
              fontSize: "0.9em",
              padding: "4px 3px",
              paddingTop: "4px",
            }}
          >
            Next
          </button>
          <button
            className="btnCancel"
            onClick={handleCancel}
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              borderRadius: "3px",
              background: "none transparent",
              border: "0px none",
              padding: "0px",
              backgroundImage: "none",
              backgroundRepeat: "initial",
              color: "rgb(1, 91, 167)",
              fontWeight: "normal",
              fontSize: "0.9em",
              marginLeft: "7px",
              paddingTop: "0px",
            }}
          >
            Cancel
          </button>
        </div>
      </div>
    </>
  );
}

function Component1() {
  return (
    <>
      <div
        className="pbDescription"
        style={{
          border: "0px solid rgb(229, 231, 235)",
          boxSizing: "border-box",
          borderColor: "hsl(0 0% 85%)",
          color: "rgb(51, 51, 51)",
          fontSize: "109%",
          clear: "right",
          padding: "8px 12px",
          margin: "0px",
          borderTop: "1px solid rgb(255, 255, 255)",
          borderBottom: "1px solid rgb(219, 219, 219)",
        }}
      >
        {
          "When you define approval steps, you can assign approval requests to different users. One of your options is to use a user field to automatically route these requests. If you want to use this option for any of your approval steps, select a field from the picklist below. Also, when a record is in the approval process, it will always be locked-- only an administrator will be able to edit it. However, you may choose to also allow the currently assigned approver to edit the record."
        }
      </div>
    </>
  );
}
function Component2() {
  const { approvalProcessData } = useContext(GlobalContext);
  const { data, setData } = approvalProcessData;
  const handleChange = (e) => {
    const { name, value } = e.target;
    data.step3[name] = value;
    setData({ ...data });
  };
  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    setData((prev) => ({
      ...prev,
      step3: {
        ...prev.step3,
        [name]: checked,
      },
    }));
  };
  return (
    <>
      <div
        className="pbWizardBody"
        style={{
          border: "0px solid rgb(229, 231, 235)",
          boxSizing: "border-box",
          borderColor: "hsl(0 0% 85%)",
          clear: "both",
          padding: "8px 12px",
          margin: "0px",
          paddingBottom: "16px",
          borderTop: "1px solid rgb(255, 255, 255)",
          borderBottom: "1px solid rgb(219, 219, 219)",
          color: "rgb(0, 0, 0)",
          backgroundColor: "rgb(248, 248, 248)",
        }}
      >
        <div
          id="ep"
          className="bPageBlock brandSecondaryBrd bEditBlock secondaryPalette"
          style={{
            boxSizing: "border-box",
            borderColor: "hsl(0 0% 85%)",
            borderTop: "4px solid rgb(34, 34, 34)",
            backgroundPosition: "left bottom",
            clear: "both",
            padding: "0px",
            paddingBottom: "0px",
            margin: "0px",
            marginBottom: "0px",
            borderBottom: "1px solid rgb(234, 234, 234)",
            borderLeft: "1px solid rgb(234, 234, 234)",
            borderRight: "1px solid rgb(234, 234, 234)",
            borderRadius: "4px",
            background: "none",
            backgroundImage: "none",
            backgroundRepeat: "initial",
            backgroundColor: "initial",
            border: "0px none",
            borderTopWidth: "0px",
          }}
        >
          <div
            className="pbHeader pbHeaderEmpty"
            style={{
              boxSizing: "border-box",
              borderColor: "hsl(0 0% 85%)",
              margin: "0px",
              borderBottom: "1px solid rgb(255, 255, 255)",
              padding: "0px",
              paddingBottom: "0px",
              color: "rgb(255, 255, 255)",
              background: "none",
              border: "none",
              backgroundColor: "initial",
              display: "none",
            }}
          >
            <table
              cellPadding="0"
              cellSpacing="0"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "inherit",
                textIndent: "0px",
                borderCollapse: "collapse",
                borderSpacing: "0px",
                width: "100%",
                padding: "5px 0px",
                borderBottom: "1px solid rgb(219, 219, 219)",
              }}
            >
              <tbody
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                }}
              >
                <tr
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                  }}
                >
                  <td
                    className="pbTitle"
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      verticalAlign: "middle",
                      fontSize: "91%",
                      width: "30%",
                      color: "rgb(255, 255, 255)",
                      background: "none",
                      padding: "0px",
                      margin: "0px",
                    }}
                  >
                    <img
                      className="minWidth"
                      height={1}
                      width={1}
                      src="/assets/downloaded/s.gif"
                      style={{
                        boxSizing: "border-box",
                        borderColor: "hsl(0 0% 85%)",
                        maxWidth: "100%",
                        verticalAlign: "middle",
                        margin: "0px 0px -1px",
                        padding: "0px",
                        border: "0px",
                        height: "1px",
                        width: "190px",
                        marginRight: "0px",
                        visibility: "hidden",
                        display: "block",
                        backgroundColor: "#8e9dbe",
                      }}
                    />
                    <h2
                      className="mainTitle"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        borderColor: "hsl(0 0% 85%)",
                        fontWeight: "bold",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        padding: "0px",
                        display: "block",
                        margin: "0px",
                        fontSize: "1.3em",
                        color: "rgb(255, 255, 255)",
                      }}
                    />
                  </td>
                  <td
                    id="topButtonRow"
                    className="pbButton"
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                      margin: "0px",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      color: "rgb(0, 0, 0)",
                      verticalAlign: "middle",
                      padding: "5px 12px",
                    }}
                  >
                     
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div
            className="pbBody"
            style={{
              border: "0px solid rgb(229, 231, 235)",
              boxSizing: "border-box",
              borderColor: "hsl(0 0% 85%)",
              color: "rgb(0, 0, 0)",
              padding: "0px",
              margin: "0px",
              marginRight: "0px",
              background: "none rgb(248, 248, 248)",
              backgroundImage: "none",
              backgroundRepeat: "initial",
              backgroundColor: "rgb(248, 248, 248)",
            }}
          >
            <div
              id="errorDiv_ep"
              className="pbError"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
                fontWeight: "bold",
                color: "rgb(204, 0, 0)",
                textAlign: "center",
                display: "none",
              }}
            >
              Error: Invalid Data.{" "}
              <br
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                }}
              />
              Review all error messages below to correct your data.
            </div>
            <div
              id="head_1_ep"
              className="brandTertiaryBgr first pbSubheader tertiaryPalette"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
                overflow: "hidden",
                fontWeight: "bold",
                fontSize: "91%",
                marginBottom: "2px",
                marginTop: "0px",
                borderStyle: "solid",
                borderWidth: "1px 0px 0px",
                padding: "4px 16px",
                backgroundImage:
                  "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFElEQVR4AWP4////fmIwnRWOKgQAQ091gLhk6q4AAAAASUVORK5CYII=)",
                color: "rgb(0, 0, 0)",
                borderTop: "1px solid rgb(255, 255, 255)",
                marginLeft: "-12px",
                marginRight: "-12px",
                paddingLeft: "12px",
                paddingRight: "12px",
                borderTopStyle: "solid",
                borderTopColor: "rgb(255, 255, 255)",
                backgroundColor: "#8e9dbe",
              }}
            >
              <h3
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                  margin: "0px",
                  display: "inline",
                  fontWeight: "bold",
                  fontFamily: "Arial, Helvetica, sans-serif",
                  fontSize: "1.2em",
                }}
              >
                Select Field Used for Automated Approval Routing
                <span
                  className="titleSeparatingColon"
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                    display: "none",
                  }}
                >
                  :
                </span>
              </h3>
            </div>
            <div
              className="pbSubsection"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
              }}
            >
              <table
                className="detailList"
                cellPadding="0"
                cellSpacing="0"
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "inherit",
                  textIndent: "0px",
                  borderCollapse: "collapse",
                  width: "100%",
                }}
              >
                <tbody
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                  }}
                >
                  <tr
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                    }}
                  >
                    <td
                      className="labelCol"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        padding: "2px 10px 2px 2px",
                        textAlign: "right",
                        fontSize: "91%",
                        fontWeight: "bold",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        width: "18%",
                        color: "rgb(74, 74, 86)",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        backgroundColor: "transparent",
                        paddingTop: "3px",
                        paddingBottom: "3px",
                      }}
                    >
                      <label
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                          whiteSpace: "nowrap",
                        }}
                      >
                        Next Automated Approver Determined By
                      </label>
                    </td>
                    <td
                      className="data2Col"
                      colSpan="3"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(0, 0, 0)",
                        verticalAlign: "top",
                        textAlign: "left",
                        width: "82%",
                        padding: "0px 2px 0px 10px",
                        backgroundColor: "transparent",
                        paddingTop: "3px",
                        paddingBottom: "3px",
                      }}
                    >
                      <div style={{ display: "flex", gap: "5px" }}>
                        <select
                          id="manager"
                          name="nextAutomatedApproverDeterminedBy"
                          value={data.step3.nextAutomatedApproverDeterminedBy}
                          onChange={handleChange}
                          style={{
                            border: "1px solid black",
                            boxSizing: "border-box",
                            margin: "0px",
                            padding: "0px",
                            fontFamily: "inherit",
                            fontFeatureSettings: "inherit",
                            fontVariationSettings: "inherit",
                            fontSize: "100%",
                            fontWeight: "inherit",
                            lineHeight: "inherit",
                            letterSpacing: "inherit",
                            textTransform: "none",
                            color: "black",
                            verticalAlign: "middle",
                            marginRight: "0.25em",
                          }}
                        >
                          <optgroup
                            style={{
                              border: "0px solid rgb(229, 231, 235)",
                              boxSizing: "border-box",
                              borderColor: "hsl(0 0% 85%)",
                              margin: "0px",
                              padding: "0px",
                              fontFamily: "inherit",
                              fontFeatureSettings: "inherit",
                              fontVariationSettings: "inherit",
                              fontWeight: "inherit",
                              lineHeight: "inherit",
                              letterSpacing: "inherit",
                              color: "inherit",
                              fontSize: "100%",
                            }}
                          >
                            <option
                              style={{
                                border: "0px solid rgb(229, 231, 235)",
                                boxSizing: "border-box",
                                borderColor: "hsl(0 0% 85%)",
                                fontSize: "100%",
                              }}
                            >
                              --None--
                            </option>
                          </optgroup>
                          <optgroup
                            label="Standard User Fields"
                            style={{ color: "inherit" }}
                          >
                            <option
                              value="Manager"
                              style={{
                                border: "0px solid rgb(229, 231, 235)",
                                boxSizing: "border-box",
                                borderColor: "hsl(0 0% 85%)",
                                fontSize: "100%",
                              }}
                            >
                              Manager
                            </option>
                          </optgroup>
                          <optgroup
                            label="Custom User Fields"
                            style={{
                              border: "0px solid rgb(229, 231, 235)",
                              boxSizing: "border-box",
                              borderColor: "hsl(0 0% 85%)",
                              margin: "0px",
                              padding: "0px",
                              fontFamily: "inherit",
                              fontFeatureSettings: "inherit",
                              fontVariationSettings: "inherit",
                              fontWeight: "inherit",
                              lineHeight: "inherit",
                              letterSpacing: "inherit",
                              color: "inherit",
                              fontSize: "100%",
                            }}
                          >
                            <option value="sales manager">sales manager</option>
                            <option value="Hr Manager">Hr Manager</option>
                          </optgroup>
                          <optgroup
                            label="-------------"
                            style={{
                              border: "0px solid rgb(229, 231, 235)",
                              boxSizing: "border-box",
                              borderColor: "hsl(0 0% 85%)",
                              margin: "0px",
                              padding: "0px",
                              fontFamily: "inherit",
                              fontFeatureSettings: "inherit",
                              fontVariationSettings: "inherit",
                              fontWeight: "inherit",
                              lineHeight: "inherit",
                              letterSpacing: "inherit",
                              color: "inherit",
                              fontSize: "100%",
                            }}
                          >
                            <option
                              value="Create New Hierarchical Relationship Field"
                              style={{
                                border: "0px solid rgb(229, 231, 235)",
                                boxSizing: "border-box",
                                borderColor: "hsl(0 0% 85%)",
                                fontSize: "100%",
                              }}
                            >
                              Create New Hierarchical Relationship Field
                            </option>
                          </optgroup>
                        </select>
                        <img
                          className="infoIcon"
                          src="/assets/downloaded/s.gif"
                          style={{
                            boxSizing: "border-box",
                            borderColor: "hsl(0 0% 85%)",
                            display: "block",
                            maxWidth: "100%",
                            border: "0px",
                            width: "16px",
                            backgroundPosition: "left top",
                            backgroundImage:
                              "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAiCAYAAABWQVnHAAACTUlEQVR42qWU02IlTRSF903wXv8/M28yeI6xbdt2bPfp2LbT1sGeWt1x0qeCiy/Y9a3VLmpsaMhVFOWEmkiMq6rKMuDBR66+ro6orrb29MjwCPu+z+l0Rgo8+MjVVFcTVVVWzrqux6lUSpCWAg8+cpUVFUTlZWXs+kGIF4BkFiIPICeICkzbDbEcbxXb8zj/qMI/1fkN8xV3Q4Fm2CG66WwgZgY2FixoVsiibq/y/4Xu8AyuFU5smK+4GwrmlwwgFswNoOBKwdiG2YorPQOQf6SJrxXIzyD2HuAMbhROSu5BzFM4dKlbnIESlrSOaPFPoay0lG0XCw4bFtrNWLAOD77IAaLSkpJZXTfYNC3WNJ0XF5diwTo8+MgJiIoKC0+3tbbx3Nw8BCnw4BcXFZ0TEBX8/Zv798+fi39+/54SsAx48JETBw8LSAx3DXKAmHlf7L/gYxfT526mL4KvPXLgwUfuXQcTfuR+6uITYmFcCCwDHnzkXrUx0cs2Pq1MMls+cyotBx585J61MNHjZp5d8pidFLMrJfLgI/dQZaL7iWigAdGuZ0EDXuQjJyC6p2R42o6YcXgVy/XwMWFL2zBfcZETRAVjZsS4xasYlrv8Cdsb5iuutODAxWhLu1owvrcCkH8ssb8CbGnXCyb2URBtaZKCmKdw6GLXui1Nz/4UFt0MLzhpnrdTMuDBX1fQlJ6ds1Ksu0lesn1etLxYsA4PPnICoruNydPlgwHPmz52XSnw4IvcubtNKaI7DUHu7Xr/4u06b0rAMuDBR06U0D8ynEueMpfs+wAAAABJRU5ErkJggg==)",
                            height: "15px",
                            marginRight: "0.25em",
                            verticalAlign: "top",
                            marginTop: "3px",
                          }}
                        />
                      </div>

                      {data.step3.nextAutomatedApproverDeterminedBy ===
                        "Create New Hierarchical Relationship Field" && (
                        <div>
                          <div style={{ display: "flex", gap: "5px" }}>
                            <span>
                              Select the label for the new hierarchy field
                            </span>
                            <input
                              type="text"
                              style={{
                                border: "2px solid #ba0517",
                              }}
                            />
                          </div>
                          <div>
                            <span style={{ color: "#ba0517" }}>
                              <b>Error:</b> You must enter a value
                            </span>
                          </div>
                        </div>
                      )}
                      <div
                        className="mouseOverInfoOuter"
                        tabIndex="0"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                          textDecoration: "none",
                          position: "relative",
                          display: "inline",
                        }}
                      >
                        <div
                          className="mouseOverInfo"
                          style={{
                            boxSizing: "border-box",
                            border: "1px solid black",
                            whiteSpace: "normal",
                            position: "absolute",
                            bottom: "20px",
                            width: "20em",
                            zIndex: 11,
                            opacity: 0,
                            fontWeight: "normal",
                            color: "rgb(0, 0, 0)",
                            borderColor: "rgb(51, 51, 51)",
                            padding: "8px 10px",
                            borderRadius: "3px",
                            backgroundColor: "rgb(255, 255, 208)",
                            fontSize: "1em",
                            display: "none",
                            left: "19px",
                          }}
                        >
                          Note that if you do not select a field here, you will
                          not be able to create any steps later that
                          automatically assign a request to an approver.
                        </div>
                      </div>
                      <div
                        id="hierLabelDiv"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                          display: "none",
                        }}
                      >
                        <label
                          htmlFor="hierLabel"
                          style={{
                            border: "0px solid rgb(229, 231, 235)",
                            boxSizing: "border-box",
                            borderColor: "hsl(0 0% 85%)",
                          }}
                        >
                          <span
                            className="assistiveText"
                            style={{
                              border: "0px solid rgb(229, 231, 235)",
                              boxSizing: "border-box",
                              borderColor: "hsl(0 0% 85%)",
                              overflow: "hidden",
                              height: "1px",
                              width: "1px",
                              clip: "rect(1px, 1px, 1px, 1px)",
                              position: "absolute",
                            }}
                          >
                            *
                          </span>
                          Select the label for the new hierarchy field
                        </label>
                        <input
                          id="hierLabel"
                          name="hierLabel"
                          type="text"
                          maxLength="40"
                          size={20}
                          style={{
                            border: "2px solid rgb(186, 5, 23)",
                            boxSizing: "border-box",
                            margin: "0px",
                            padding: "0px",
                            fontFamily: "inherit",
                            fontFeatureSettings: "inherit",
                            fontVariationSettings: "inherit",
                            fontSize: "100%",
                            fontWeight: "inherit",
                            lineHeight: "inherit",
                            letterSpacing: "inherit",
                            color: "inherit",
                            paddingTop: "0px",
                            verticalAlign: "middle",
                            marginRight: "0.25em",
                          }}
                        />
                      </div>
                    </td>
                  </tr>
                  <tr
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                    }}
                  >
                    <td
                      className="last labelCol"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        padding: "2px 10px 2px 2px",
                        textAlign: "right",
                        fontSize: "91%",
                        fontWeight: "bold",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        width: "18%",
                        color: "rgb(74, 74, 86)",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        backgroundColor: "transparent",
                        paddingTop: "3px",
                        paddingBottom: "3px",
                      }}
                    >
                      <label
                        htmlFor="ownMgrElm"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                        }}
                      >
                        Use Approver Field of Opportunity Owner
                      </label>
                    </td>
                    <td
                      className="dataCol last col02"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(0, 0, 0)",
                        textAlign: "left",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        width: "32%",
                        padding: "0px 2px 0px 10px",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        borderRight: "none",
                        borderRightColor: "initial",
                        paddingRight: "20px",
                        backgroundColor: "transparent",
                        paddingTop: "3px",
                        paddingBottom: "3px",
                      }}
                    >
                      <input
                        id="ownMgrElm"
                        name="useApproverFieldOfOpportunityOwner"
                        type="checkbox"
                        checked={data.step3.useApproverFieldOfOpportunityOwner}
                        onChange={handleCheckboxChange}
                        disabled={
                          data.step3.nextAutomatedApproverDeterminedBy ===
                          "--None--"
                        }
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                          margin: "0px",
                          padding: "0px",
                          fontFamily: "inherit",
                          fontFeatureSettings: "inherit",
                          fontVariationSettings: "inherit",
                          fontSize: "100%",
                          fontWeight: "inherit",
                          lineHeight: "inherit",
                          letterSpacing: "inherit",
                          color: "inherit",
                          paddingTop: "0px",
                          cursor: "pointer",
                          verticalAlign: "middle",
                          marginRight: "0.25em",
                        }}
                      />
                    </td>
                    <td
                      className="labelCol last empty"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        padding: "2px 10px 2px 2px",
                        textAlign: "right",
                        fontSize: "91%",
                        fontWeight: "bold",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        width: "18%",
                        color: "rgb(74, 74, 86)",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        backgroundColor: "transparent",
                        paddingTop: "3px",
                        paddingBottom: "3px",
                      }}
                    >
                       
                    </td>
                    <td
                      className="dataCol last empty"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(0, 0, 0)",
                        textAlign: "left",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        width: "32%",
                        padding: "0px 2px 0px 10px",
                        borderRightColor: "transparent",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        backgroundColor: "transparent",
                        paddingTop: "3px",
                        paddingBottom: "3px",
                      }}
                    >
                       
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              id="head_2_ep"
              className="pbSubheader brandTertiaryBgr tertiaryPalette"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
                overflow: "hidden",
                fontWeight: "bold",
                fontSize: "91%",
                marginTop: "15px",
                marginBottom: "2px",
                backgroundColor: "#8e9dbe",
                borderStyle: "solid",
                borderWidth: "1px 0px 0px",
                padding: "4px 16px",
                backgroundImage:
                  "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFElEQVR4AWP4////fmIwnRWOKgQAQ091gLhk6q4AAAAASUVORK5CYII=)",
                color: "rgb(0, 0, 0)",
                borderTop: "1px solid rgb(255, 255, 255)",
                marginLeft: "-12px",
                marginRight: "-12px",
                paddingLeft: "12px",
                paddingRight: "12px",
                borderTopStyle: "solid",
                borderTopColor: "rgb(255, 255, 255)",
              }}
            >
              <h3
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                  margin: "0px",
                  display: "inline",
                  fontWeight: "bold",
                  fontFamily: "Arial, Helvetica, sans-serif",
                  fontSize: "1.2em",
                }}
              >
                Record Editability Properties
                <span
                  className="titleSeparatingColon"
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                    display: "none",
                  }}
                >
                  :
                </span>
              </h3>
            </div>
            <div
              className="pbSubsection"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
              }}
            >
              <table
                className="detailList"
                cellPadding="0"
                cellSpacing="0"
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "inherit",
                  textIndent: "0px",
                  borderCollapse: "collapse",
                  width: "100%",
                }}
              >
                <tbody
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                  }}
                >
                  <APStep3Checkboxes />
                </tbody>
              </table>
            </div>
          </div>
          <div
            className="pbFooter secondaryPalette"
            style={{
              boxSizing: "border-box",
              borderColor: "hsl(0 0% 85%)",
              backgroundPosition: "right bottom",
              height: "9px",
              width: "9px",
              cssFloat: "right",
              display: "none",
              background: "none",
              border: "none",
              backgroundImage: "none",
              backgroundRepeat: "initial",
              backgroundColor: "initial",
            }}
          >
            <div
              className="bg"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
              }}
            />
          </div>
        </div>
      </div>
    </>
  );
}
