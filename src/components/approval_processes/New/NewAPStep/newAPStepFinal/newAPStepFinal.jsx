import React, { useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "../../../../../context/GlobalContext";

const NewAPStepFinal = () => {
  const navigate = useNavigate();
  const [selectedOption, setSelectedOption] = useState("approve_radio");
  const [approveAction, setApproveAction] = useState("T"); // Default to Task
  const [rejectAction, setRejectAction] = useState("T");

  const {
    allVariableData,
    setAllVariableData,
    saveToLocalStorage,
    approvalProcessData,
  } = useContext(GlobalContext);
  useEffect(() => {
    allVariableData.approvalProcesses = {
      step1: {},
      step2: {},
      step3: {},
      step4: {},
      step5: {},
      step6: {},
      active: false,
      approvalStep: {},
    };
    approvalProcessData.data = {
      ...approvalProcessData.data,
      step1: {
        name: "",
        uniqueName: "",
        desc: "",
      },
      approvalStep: {
        approvalStepOption: "Yes, I'd like to create an approval step now.",
      },
      approvalStep1: {
        approvalProcessName: "",
        approvalProcessUniqueName: "",
        approvalProcessDesc: "",
        approvalProcessStepNumber: "1",
      },
    };
    setAllVariableData({ ...allVariableData });
    approvalProcessData.setData({ ...approvalProcessData.data });
    saveToLocalStorage("approvalProcesses", {});
  }, []);

  const handleGo = () => {
    // Check which option is selected and navigate accordingly
    if (selectedOption === "approve_radio" && approveAction === "U") {
      // Set the approval action section for field update tracking
      // Since this is from the approval step creation, we'll use "finalApprovalActions"
      localStorage.setItem("approvalActionSection", "finalApprovalActions");

      // Navigate to Field Update page
      navigate("/NewFieldUpdate");
    } else if (selectedOption === "approve_radio" && approveAction === "E") {
      // Set the approval action section for email alert tracking
      // Since this is from the approval step creation, we'll use "finalApprovalActions"
      localStorage.setItem("approvalActionSection", "finalApprovalActions");

      // Navigate to Email Alert page
      navigate("/NewEmailAlert");
    } else if (selectedOption === "reject_radio" && rejectAction === "U") {
      // Set the approval action section for field update tracking
      // Since this is from the approval step creation, we'll use "finalRejectionActions"
      localStorage.setItem("approvalActionSection", "finalRejectionActions");

      // Navigate to Field Update page for rejection action
      navigate("/NewFieldUpdate");
    } else if (selectedOption === "reject_radio" && rejectAction === "E") {
      // Set the approval action section for email alert tracking
      // Since this is from the approval step creation, we'll use "finalRejectionActions"
      localStorage.setItem("approvalActionSection", "finalRejectionActions");

      // Navigate to Email Alert page for rejection action
      navigate("/NewEmailAlert");
    } else if (selectedOption === "approval_process_detail") {
      // Navigate to approval process detail page
      navigate("/setup/approval-processes/detail");
    } else {
      // Handle other action types (Task, Outbound Message, Flow)
      // For now, just navigate to approval process detail page
      navigate("/setup/approval-processes/detail");
    }
  };

  return (
    <div>
      <Component1 />
      <Component2
        selectedOption={selectedOption}
        setSelectedOption={setSelectedOption}
        approveAction={approveAction}
        setApproveAction={setApproveAction}
        rejectAction={rejectAction}
        setRejectAction={setRejectAction}
        handleGo={handleGo}
      />
    </div>
  );
};

export default NewAPStepFinal;

function Component1() {
  return (
    <>
      <div
        className="bPageTitle"
        style={{ marginBottom: "15px", padding: "15px 0px 0px" }}
      >
        <div
          className="ptBody"
          style={{
            overflow: "hidden",
            width: "100%",
            padding: "0px",
            backgroundColor: "transparent",
            color: "rgb(0, 0, 0)",
            paddingTop: "0px",
            paddingBottom: "0px",
          }}
        >
          <div
            className="content"
            style={{
              cssFloat: "left",
              verticalAlign: "middle",
              width: "70%",
              paddingLeft: "0px",
            }}
          >
            <img
              className="pageTitleIcon"
              src="/assets/downloaded/s.gif"
              style={{
                border: "0px",
                cssFloat: "left",
                marginRight: "5px",
                backgroundPosition: "0px -1058px",
                backgroundImage: 'url("/assets/downloaded/master.png")',
                width: "32px",
                height: "32px",
                marginTop: "5px",
                display: "none",
              }}
            />
            <h1
              className="noSecondHeader pageType"
              style={{
                fontFamily: "Arial, Helvetica, sans-serif",
                display: "block",
                margin: "10px 0px 15px",
                whiteSpace: "nowrap",
                paddingLeft: "1px",
                marginBottom: "0px",
                color: "rgb(0, 0, 0)",
                fontSize: "2em",
                fontWeight: "normal",
                marginLeft: "0px",
                marginTop: "0px",
              }}
            >
              What Would You Like To Do Now?
            </h1>
            <div
              className="blank"
              style={{ fontSize: "0px", clear: "both" }}
            ></div>
          </div>
          <div
            className="links"
            style={{
              padding: "10px 5px 0px 0px",
              whiteSpace: "nowrap",
              cssFloat: "right",
              textAlign: "right",
              verticalAlign: "middle",
              fontSize: "91%",
              paddingTop: "0px",
            }}
          >
            <a
              href="/#"
              title="Help for this Page (New Window)"
              style={{ textDecoration: "none", color: "rgb(1, 91, 167)" }}
            >
              <span
                className="helpLink"
                style={{
                  paddingRight: "5px",
                  textDecoration: "none",
                  color: "rgb(1, 91, 167)",
                }}
              >
                Help for this Page
              </span>
              <img
                className="helpIcon"
                src="/assets/downloaded/s.gif"
                style={{
                  border: "0px",
                  backgroundPosition: "0px -142px",
                  width: "16px",
                  height: "16px",
                  background:
                    'url("/assets/downloaded/help_orange.png") no-repeat transparent',
                  backgroundImage: 'url("/assets/downloaded/help_orange.png")',
                  verticalAlign: "bottom",
                }}
              />
            </a>
          </div>
        </div>
        <div
          className="ptBreadcrumb"
          style={{
            fontFamily: "Verdana, Geneva, sans-serif",
            fontSize: "91.3%",
            verticalAlign: "middle",
            height: "auto",
            marginTop: "3px",
            marginBottom: "0px",
          }}
        />
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
html {
  overflow: auto;
}

body {
  background-position: left top;
  font-size: 71%;
  background: none transparent;
  margin: 0px;
  padding: 0px;
  overflow: auto;
  font-family: Helvetica;
  color: rgb(60, 61, 62);
  background-repeat: initial;
  background-color: transparent;
  height: 100%;
}
`,
        }}
      />
    </>
  );
}

function Component2({
  selectedOption,
  setSelectedOption,
  approveAction,
  setApproveAction,
  rejectAction,
  setRejectAction,
  handleGo,
}) {
  return (
    <>
      <div
        id="ep"
        className="bPageBlock brandSecondaryBrd bEditBlock secondaryPalette"
        style={{
          backgroundPosition: "left bottom",
          backgroundRepeat: "no-repeat",
          clear: "both",
          margin: "0px",
          backgroundImage: "none",
          padding: "0px",
          paddingBottom: "0px",
          marginBottom: "10px",
          borderColor: "rgb(116, 126, 150)",
          borderTop: "3px solid rgb(116, 126, 150)",
          borderTopWidth: "3px",
          borderBottom: "1px solid rgb(234, 234, 234)",
          borderLeft: "1px solid rgb(234, 234, 234)",
          borderRight: "1px solid rgb(234, 234, 234)",
          borderRadius: "4px",
          backgroundColor: "rgb(248, 248, 248)",
        }}
      >
        <div
          className="pbHeader pbHeaderEmpty"
          style={{
            margin: "0px",
            backgroundColor: "transparent",
            borderBottom: "1px solid rgb(255, 255, 255)",
            padding: "0px",
            paddingBottom: "0px",
            display: "none",
          }}
        >
          <table
            cellPadding="0"
            cellSpacing="0"
            style={{
              borderSpacing: "0px",
              width: "100%",
              padding: "5px 0px",
              borderBottom: "1px solid rgb(219, 219, 219)",
            }}
          >
            <tbody>
              <tr>
                <td
                  className="pbTitle"
                  style={{
                    fontFamily: "Arial, Helvetica, sans-serif",
                    margin: "0px",
                    verticalAlign: "middle",
                    color: "rgb(34, 34, 34)",
                    fontSize: "91%",
                    width: "30%",
                    padding: "5px 12px",
                  }}
                >
                  <img
                    className="minWidth"
                    height={1}
                    width={1}
                    src="/assets/downloaded/s.gif"
                    style={{
                      verticalAlign: "middle",
                      margin: "0px 0px -1px",
                      padding: "0px",
                      border: "0px",
                      height: "1px",
                      width: "190px",
                      marginRight: "0px",
                      visibility: "hidden",
                      display: "block",
                    }}
                  />
                  <h2
                    className="mainTitle"
                    style={{
                      fontWeight: "bold",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      padding: "0px",
                      display: "block",
                      margin: "0px",
                      color: "rgb(0, 0, 0)",
                      fontSize: "1.3em",
                    }}
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div
          className="pbBody"
          style={{
            color: "rgb(0, 0, 0)",
            margin: "0px",
            backgroundColor: "transparent",
            marginRight: "0px",
            padding: "0px",
            backgroundImage: "none",
          }}
        >
          <div
            id="errorDiv_ep"
            className="pbError"
            style={{
              fontWeight: "bold",
              color: "rgb(204, 0, 0)",
              textAlign: "center",
              display: "none",
            }}
          >
            Error: Invalid Data. <br />
            Review all error messages below to correct your data.
          </div>
          <div className="pbSubsection" style={{ padding: "10px 10px 0px" }}>
            <table
              className="detailList"
              cellPadding="0"
              cellSpacing="0"
              style={{ width: "100%" }}
            >
              <tbody>
                <tr className="detailRow">
                  <td
                    colSpan="4"
                    style={{
                      margin: "0px",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      color: "rgb(0, 0, 0)",
                      verticalAlign: "top",
                      borderColor: "rgb(236, 236, 236)",
                      borderBottom: "none",
                      borderBottomWidth: "0px",
                    }}
                  >
                    <div
                      className="textMarginBottom"
                      style={{ marginBottom: "5px" }}
                    >
                      You have just created an approval step. You can optionally
                      specify workflow actions to occur upon approval or
                      rejection of this step. Would you like to do that now?{" "}
                    </div>
                  </td>
                </tr>
                <tr className="detailRow">
                  <td
                    colSpan="4"
                    style={{
                      margin: "0px",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      color: "rgb(0, 0, 0)",
                      verticalAlign: "top",
                      borderColor: "rgb(236, 236, 236)",
                      borderBottom: "none",
                      borderBottomWidth: "0px",
                    }}
                  >
                    <div style={{ marginBottom: "5px" }}>
                      <input
                        id="redirectapprove_radio"
                        name="redirect"
                        type="radio"
                        checked={selectedOption === "approve_radio"}
                        value="approve_radio"
                        onChange={(e) => setSelectedOption(e.target.value)}
                        style={{
                          paddingTop: "0px",
                          verticalAlign: "middle",
                          margin: "-1px 5px 0px",
                          marginRight: "5px",
                        }}
                      />
                      <label
                        htmlFor="redirectapprove_radio"
                        style={{ verticalAlign: "middle" }}
                      >
                        Yes, I'd like to create a new approval action for this
                        step now.{" "}
                      </label>
                      <select
                        id="approve_action"
                        name="approve_action"
                        value={approveAction}
                        onChange={(e) => setApproveAction(e.target.value)}
                        style={{
                          color: "rgb(0, 0, 0)",
                          verticalAlign: "middle",
                          marginRight: "0.25em",
                        }}
                      >
                        <option value="T" style={{ fontSize: "100%" }}>
                          Task
                        </option>
                        <option value="E" style={{ fontSize: "100%" }}>
                          Email Alert
                        </option>
                        <option value="U" style={{ fontSize: "100%" }}>
                          Field Update
                        </option>
                        <option value="M" style={{ fontSize: "100%" }}>
                          Outbound Message
                        </option>
                        <option value="A" style={{ fontSize: "100%" }}>
                          Flow
                        </option>
                      </select>
                    </div>
                    <div style={{ marginBottom: "5px" }}>
                      <input
                        id="redirectreject_radio"
                        name="redirect"
                        type="radio"
                        checked={selectedOption === "reject_radio"}
                        value="reject_radio"
                        onChange={(e) => setSelectedOption(e.target.value)}
                        style={{
                          paddingTop: "0px",
                          verticalAlign: "middle",
                          margin: "-1px 5px 0px",
                          marginRight: "5px",
                        }}
                      />
                      <label
                        htmlFor="redirectreject_radio"
                        style={{ verticalAlign: "middle" }}
                      >
                        Yes, I'd like to create a new rejection action for this
                        step now.{" "}
                      </label>
                      <select
                        id="reject_action"
                        name="reject_action"
                        value={rejectAction}
                        onChange={(e) => setRejectAction(e.target.value)}
                        style={{
                          color: "rgb(0, 0, 0)",
                          verticalAlign: "middle",
                          marginRight: "0.25em",
                        }}
                      >
                        <option value="T" style={{ fontSize: "100%" }}>
                          Task
                        </option>
                        <option value="E" style={{ fontSize: "100%" }}>
                          Email Alert
                        </option>
                        <option value="U" style={{ fontSize: "100%" }}>
                          Field Update
                        </option>
                        <option value="M" style={{ fontSize: "100%" }}>
                          Outbound Message
                        </option>
                        <option value="A" style={{ fontSize: "100%" }}>
                          Flow
                        </option>
                      </select>
                    </div>
                    <div style={{ marginBottom: "5px" }}>
                      <input
                        id="redirectapproval_process_detail"
                        name="redirect"
                        type="radio"
                        checked={selectedOption === "approval_process_detail"}
                        value="approval_process_detail"
                        onChange={(e) => setSelectedOption(e.target.value)}
                        style={{
                          paddingTop: "0px",
                          verticalAlign: "middle",
                          margin: "-1px 5px 0px",
                          marginRight: "5px",
                        }}
                      />
                      <label
                        htmlFor="redirectapproval_process_detail"
                        style={{ verticalAlign: "middle" }}
                      >
                        No, I'll do this later. Take me to the approval process
                        detail page to review what I've just created.{" "}
                      </label>
                    </div>
                  </td>
                </tr>
                <tr className="last detailRow">
                  <td
                    colSpan="4"
                    style={{
                      margin: "0px",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      color: "rgb(0, 0, 0)",
                      verticalAlign: "top",
                      borderColor: "rgb(236, 236, 236)",
                      borderBottom: "none",
                      borderBottomWidth: "0px",
                    }}
                  />
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          className="pbBottomButtons"
          style={{
            margin: "0px",
            backgroundColor: "transparent",
            backgroundImage: "none",
            marginTop: "20px",
            borderTop: "1px solid rgb(219, 219, 219)",
          }}
        >
          <table
            cellPadding="0"
            cellSpacing="0"
            style={{
              borderSpacing: "0px",
              width: "100%",
              borderTop: "1px solid rgb(255, 255, 255)",
            }}
          >
            <tbody>
              <tr>
                <td
                  className="pbTitle"
                  style={{
                    fontFamily: "Arial, Helvetica, sans-serif",
                    margin: "0px",
                    verticalAlign: "middle",
                    color: "rgb(34, 34, 34)",
                    fontSize: "91%",
                    width: "30%",
                    padding: "5px 12px",
                  }}
                >
                  <img
                    className="minWidth"
                    height={1}
                    width={190}
                    src="/assets/downloaded/s.gif"
                    style={{
                      verticalAlign: "middle",
                      margin: "0px 0px -1px",
                      padding: "0px",
                      border: "0px",
                      height: "1px",
                      width: "190px",
                      marginRight: "0px",
                      visibility: "hidden",
                      display: "block",
                    }}
                  />
                </td>
                <td
                  id="bottomButtonRow"
                  className="pbButtonb"
                  style={{
                    margin: "0px",
                    fontFamily: "Arial, Helvetica, sans-serif",
                    color: "rgb(34, 34, 34)",
                    padding: "5px 12px",
                  }}
                >
                  <input
                    className="btn"
                    name="go"
                    type="button"
                    defaultValue=" Go! "
                    title="Go!"
                    onClick={handleGo}
                    style={{
                      backgroundPosition: "left top",
                      borderRight: "1px solid rgb(92, 93, 97)",
                      borderBottom: "1px solid rgb(92, 93, 97)",
                      borderTop: "none",
                      borderLeft: "none",
                      cursor: "pointer",
                      display: "inline",
                      verticalAlign: "middle",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      borderWidth: "1px",
                      borderStyle: "solid",
                      borderColor:
                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                      borderImage: "initial",
                      background:
                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                      borderRadius: "3px",
                      color: "rgb(51, 51, 51)",
                      backgroundImage:
                        'url("/assets/downloaded/btn_sprite.png")',
                      backgroundRepeat: "repeat-x",
                      fontWeight: "bold",
                      fontSize: "0.9em",
                      padding: "4px 3px",
                      paddingTop: "4px",
                      margin: "0px 2px",
                      marginRight: "2px",
                    }}
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div
          className="pbFooter secondaryPalette"
          style={{
            backgroundPosition: "right bottom",
            height: "9px",
            width: "9px",
            cssFloat: "right",
            backgroundImage: 'url("/assets/downloaded/bgPageBlockRight.gif")',
            backgroundRepeat: "repeat-x",
            borderColor: "rgb(116, 126, 150)",
            backgroundColor: "rgb(116, 126, 150)",
            display: "none",
          }}
        >
          <div className="bg" />
        </div>
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
html {
  overflow: auto;
}

body {
  background-position: left top;
  font-size: 71%;
  background: none transparent;
  margin: 0px;
  padding: 0px;
  overflow: auto;
  font-family: Helvetica;
  color: rgb(60, 61, 62);
  background-repeat: initial;
  background-color: transparent;
  height: 100%;
}
`,
        }}
      />
    </>
  );
}
