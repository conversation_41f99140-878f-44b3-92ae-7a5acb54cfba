import React, { useContext, useEffect } from "react";
import APPicklist from "./APPicklist";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "../../../../context/GlobalContext";
import APPicklistContainer from "./APPicklistContainer";

const APStep6 = () => {
  const { approvalProcessData, setAllVariableData, saveToLocalStorage } =
    useContext(GlobalContext);
  useEffect(() => {
    setAllVariableData((prev) => {
      prev.approvalProcesses = {
        ...prev.approvalProcesses,
        step6: approvalProcessData.data.step6,
      };
      saveToLocalStorage("approvalProcesses", prev.approvalProcesses);
      return { ...prev };
    });
  }, [approvalProcessData.data]);
  return (
    <div
      style={{
        backgroundColor: "rgb(248, 248, 248)",
      }}
    >
      <NavigatonButtons />
      <Component1 />
      <Component2 />
      <NavigatonButtons />
    </div>
  );
};

export default APStep6;

function Component1() {
  return (
    <>
      <div
        className="pbDescription"
        style={{
          border: "0px solid rgb(229, 231, 235)",
          boxSizing: "border-box",
          borderColor: "hsl(0 0% 85%)",
          color: "rgb(51, 51, 51)",
          fontSize: "109%",
          clear: "right",
          padding: "8px 12px",
          margin: "0px",
          borderTop: "1px solid rgb(255, 255, 255)",
          borderBottom: "1px solid rgb(219, 219, 219)",
        }}
      >
        {
          "Using the options below, specify which users are allowed to submit the initial request for approval. For example, expense reports should normally be submitted for approval only by their owners."
        }
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
                    html {
                        border: 0px solid rgb(229, 231, 235);
                        box-sizing: border-box;
                        border-color: hsl(0 0% 85%);
                        line-height: 1.5;
                        text-size-adjust: 100%;
                        tab-size: 4;
                        font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                        font-feature-settings: normal;
                        font-variation-settings: normal;
                        -webkit-tap-highlight-color: transparent;
                        overflow: auto;
                    }

                    body {
                        border: 0px solid rgb(229, 231, 235);
                        box-sizing: border-box;
                        border-color: hsl(0 0% 85%);
                        line-height: inherit;
                        min-height: 100vh;
                        margin: 0px;
                        background-position: left top;
                        background-repeat: repeat-x;
                        color: rgb(34, 34, 34);
                        background-color: rgb(255, 255, 255);
                        font-size: 71%;
                        font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                        -webkit-font-smoothing: antialiased;
                    }
                    `,
        }}
      />
    </>
  );
}

function NavigatonButtons() {
  const {
    approvalProcessData,
    allVariableData,
    setAllVariableData,
    saveToLocalStorage,
  } = useContext(GlobalContext);
  const navigate = useNavigate();
  const handleNext = () => {
    // Ensure the approval process has the action arrays initialized
    const approvalProcess = {
      ...allVariableData.approvalProcesses,
      finalApprovalActions:
        allVariableData.approvalProcesses.finalApprovalActions || [],
      finalRejectionActions:
        allVariableData.approvalProcesses.finalRejectionActions || [],
    };

    allVariableData.approvalProcesses = {};
    // Push the approval process to the list
    allVariableData.approvalProcessesList.push(approvalProcess);
    saveToLocalStorage("approvalProcesses", allVariableData.approvalProcesses);

    // Save to localStorage
    saveToLocalStorage(
      "approvalProcessesList",
      allVariableData.approvalProcessesList
    );

    // Update the context
    setAllVariableData({
      ...allVariableData,
    });

    // Navigate to the next page
    navigate("/setup/approval-processes/new/final");
  };
  const handleCancel = () => {
    navigate("/setup/approval-processes");
  };
  const handlePrevious = () => {
    navigate("/setup/approval-processes/new/step5");
  };
  return (
    <>
      <div
        className="pbWizardHeader"
        style={{
          overflow: "hidden",
          height: "1%",
          borderBottom: "1px solid rgb(219, 219, 219)",
          marginBottom: "0px",
          color: "rgb(0, 0, 0)",
          backgroundColor: "rgb(248, 248, 248)",
        }}
      >
        <div
          className="pbTopButtons"
          style={{
            color: "rgb(51, 51, 51)",
            cssFloat: "right",
            padding: "8px 12px",
            margin: "0px",
            whiteSpace: "nowrap",
          }}
        >
          <button
            className="btn"
            onClick={handlePrevious}
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              background:
                'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
              borderRadius: "3px",
              color: "rgb(51, 51, 51)",
              backgroundImage: 'url("/assets/downloaded/btn_sprite.png")',
              backgroundRepeat: "repeat-x",
              fontWeight: "bold",
              fontSize: "0.9em",
              padding: "4px 3px",
              paddingTop: "4px",
            }}
          >
            Previous
          </button>
          <button
            className="btn"
            onClick={handleNext}
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              background:
                'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
              borderRadius: "3px",
              color: "rgb(51, 51, 51)",
              backgroundImage: 'url("/assets/downloaded/btn_sprite.png")',
              backgroundRepeat: "repeat-x",
              fontWeight: "bold",
              fontSize: "0.9em",
              padding: "4px 3px",
              paddingTop: "4px",
            }}
          >
            Save
          </button>
          <button
            className="btnCancel"
            onClick={handleCancel}
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              borderRadius: "3px",
              background: "none transparent",
              border: "0px none",
              padding: "0px",
              backgroundImage: "none",
              backgroundRepeat: "initial",
              color: "rgb(1, 91, 167)",
              fontWeight: "normal",
              fontSize: "0.9em",
              marginLeft: "7px",
              paddingTop: "0px",
            }}
          >
            Cancel
          </button>
        </div>
      </div>
    </>
  );
}

function Component2() {
  const { approvalProcessData } = useContext(GlobalContext);
  const { data, setData } = approvalProcessData;
  return (
    <>
      <div
        className="pbWizardBody"
        style={{
          border: "0px solid rgb(229, 231, 235)",
          boxSizing: "border-box",
          borderColor: "hsl(0 0% 85%)",
          clear: "both",
          padding: "8px 12px",
          margin: "0px",
          paddingBottom: "16px",
          borderTop: "1px solid rgb(255, 255, 255)",
          borderBottom: "1px solid rgb(219, 219, 219)",
          color: "rgb(0, 0, 0)",
        }}
      >
        <div
          id="ep"
          className="bPageBlock brandSecondaryBrd bEditBlock secondaryPalette"
          style={{
            boxSizing: "border-box",
            borderColor: "hsl(0 0% 85%)",
            borderTop: "4px solid rgb(34, 34, 34)",
            backgroundPosition: "left bottom",
            clear: "both",
            padding: "0px",
            paddingBottom: "0px",
            margin: "0px",
            marginBottom: "0px",
            borderBottom: "1px solid rgb(234, 234, 234)",
            borderLeft: "1px solid rgb(234, 234, 234)",
            borderRight: "1px solid rgb(234, 234, 234)",
            borderRadius: "4px",
            background: "none",
            backgroundImage: "none",
            backgroundRepeat: "initial",
            backgroundColor: "initial",
            border: "0px none",
            borderTopWidth: "0px",
          }}
        >
          <div
            className="pbHeader pbHeaderEmpty"
            style={{
              boxSizing: "border-box",
              borderColor: "hsl(0 0% 85%)",
              margin: "0px",
              borderBottom: "1px solid rgb(255, 255, 255)",
              padding: "0px",
              paddingBottom: "0px",
              color: "rgb(255, 255, 255)",
              background: "none",
              border: "none",
              backgroundColor: "initial",
              display: "none",
            }}
          >
            <table
              cellPadding="0"
              cellSpacing="0"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "inherit",
                textIndent: "0px",
                borderCollapse: "collapse",
                borderSpacing: "0px",
                width: "100%",
                padding: "5px 0px",
                borderBottom: "1px solid rgb(219, 219, 219)",
              }}
            >
              <tbody
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                }}
              >
                <tr
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                  }}
                >
                  <td
                    className="pbTitle"
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      verticalAlign: "middle",
                      fontSize: "91%",
                      width: "30%",
                      color: "rgb(255, 255, 255)",
                      background: "none",
                      padding: "0px",
                      margin: "0px",
                    }}
                  >
                    <img
                      className="minWidth"
                      height={1}
                      width={1}
                      src="/assets/downloaded/s.gif"
                      style={{
                        boxSizing: "border-box",
                        borderColor: "hsl(0 0% 85%)",
                        maxWidth: "100%",
                        verticalAlign: "middle",
                        margin: "0px 0px -1px",
                        padding: "0px",
                        border: "0px",
                        height: "1px",
                        width: "190px",
                        marginRight: "0px",
                        visibility: "hidden",
                        display: "block",
                      }}
                    />
                    <h2
                      className="mainTitle"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        borderColor: "hsl(0 0% 85%)",
                        fontWeight: "bold",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        padding: "0px",
                        display: "block",
                        margin: "0px",
                        fontSize: "1.3em",
                        color: "rgb(255, 255, 255)",
                      }}
                    />
                  </td>
                  <td
                    id="topButtonRow"
                    className="pbButton"
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                      margin: "0px",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      color: "rgb(0, 0, 0)",
                      verticalAlign: "middle",
                      padding: "5px 12px",
                    }}
                  ></td>
                </tr>
              </tbody>
            </table>
          </div>
          <div
            className="pbBody"
            style={{
              border: "0px solid rgb(229, 231, 235)",
              boxSizing: "border-box",
              borderColor: "hsl(0 0% 85%)",
              color: "rgb(0, 0, 0)",
              padding: "0px",
              margin: "0px",
              marginRight: "0px",
              background: "none rgb(248, 248, 248)",
              backgroundImage: "none",
              backgroundRepeat: "initial",
              backgroundColor: "rgb(248, 248, 248)",
            }}
          >
            <div
              id="errorDiv_ep"
              className="pbError"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
                fontWeight: "bold",
                color: "rgb(204, 0, 0)",
                textAlign: "center",
                display: "none",
              }}
            >
              Error: Invalid Data.{" "}
              <br
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                }}
              />
              Review all error messages below to correct your data.
            </div>
            <div
              id="head_1_ep"
              className="brandTertiaryBgr first pbSubheader tertiaryPalette"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
                overflow: "hidden",
                fontWeight: "bold",
                fontSize: "91%",
                marginBottom: "2px",
                marginTop: "0px",
                backgroundColor: "#8e9dbe",
                borderStyle: "solid",
                borderWidth: "1px 0px 0px",
                padding: "4px 16px",
                backgroundImage:
                  "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFElEQVR4AWP4////fmIwnRWOKgQAQ091gLhk6q4AAAAASUVORK5CYII=)",
                color: "rgb(0, 0, 0)",
                borderTop: "1px solid rgb(255, 255, 255)",
                marginLeft: "-12px",
                marginRight: "-12px",
                paddingLeft: "12px",
                paddingRight: "12px",
                borderTopStyle: "solid",
                borderTopColor: "rgb(255, 255, 255)",
              }}
            >
              <h3
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                  margin: "0px",
                  display: "inline",
                  fontWeight: "bold",
                  fontFamily: "Arial, Helvetica, sans-serif",
                  fontSize: "1.2em",
                }}
              >
                Initial Submitters
                <span
                  className="titleSeparatingColon"
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                    display: "none",
                  }}
                >
                  :
                </span>
              </h3>
            </div>
            <div
              className="pbSubsection"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
              }}
            >
              <table
                className="detailList"
                cellPadding="0"
                cellSpacing="0"
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "inherit",
                  textIndent: "0px",
                  borderCollapse: "collapse",
                  width: "90%",
                }}
              >
                <tbody
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                  }}
                >
                  <tr
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                    }}
                  >
                    <td
                      className="labelCol"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        padding: "2px 10px 2px 2px",
                        textAlign: "right",
                        fontSize: "91%",
                        fontWeight: "bold",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        width: "18%",
                        color: "rgb(74, 74, 86)",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        backgroundColor: "transparent",
                        paddingTop: "3px",
                        paddingBottom: "3px",
                      }}
                    >
                      <label
                        htmlFor="subfilt"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                        }}
                      >
                        Submitter Type
                      </label>
                    </td>
                    <td
                      className="data2Col"
                      colSpan="3"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(0, 0, 0)",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        textAlign: "left",
                        width: "82%",
                        padding: "0px 2px 0px 10px",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        backgroundColor: "transparent",
                        paddingTop: "3px",
                        paddingBottom: "3px",
                      }}
                    >
                      <label
                        htmlFor="subfilt"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                        }}
                      >
                        Search:
                      </label>

                      <select
                        id="subfilt"
                        name="subfilt"
                        value={data.step6?.submitterType}
                        onChange={(e) => {
                          const { value } = e.target;
                          setData((prev) => ({
                            ...prev,
                            step6: {
                              ...prev.step6,
                              submitterType: value,
                            },
                          }));
                        }}
                        style={{
                          border: "1px solid black",
                          margin: "0px",
                          padding: "0px",
                          fontFamily: "inherit",
                          fontFeatureSettings: "inherit",
                          fontVariationSettings: "inherit",
                          fontSize: "100%",
                          fontWeight: "inherit",
                          lineHeight: "inherit",
                          letterSpacing: "inherit",
                          textTransform: "none",
                          color: "rgb(0, 0, 0)",
                          verticalAlign: "middle",
                          marginRight: "0.25em",
                        }}
                      >
                        {[
                          "Creator",
                          "Owner",
                          "Public Groups",
                          "Role",
                          "Role and Internal Subordinates",
                          "User",
                        ].map((item, index) => (
                          <option key={index + 1} value={item}>
                            {item}
                          </option>
                        ))}
                      </select>
                      <label
                        htmlFor="searchValue_subfilt"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                        }}
                      >
                        for:
                      </label>

                      <input
                        id="searchValue_subfilt"
                        name="searchValue_subfilt"
                        type="text"
                        maxLength="80"
                        size={20}
                        style={{
                          border: "1px solid black",
                          boxSizing: "border-box",
                          margin: "0px",
                          padding: "0px",
                          fontFamily: "inherit",
                          fontFeatureSettings: "inherit",
                          fontVariationSettings: "inherit",
                          fontSize: "100%",
                          fontWeight: "inherit",
                          lineHeight: "inherit",
                          letterSpacing: "inherit",
                          color: "inherit",
                          paddingTop: "0px",
                          verticalAlign: "middle",
                          marginRight: "0.25em",
                        }}
                      />

                      <input
                        id="go_submitters_select_0Var"
                        className="btn"
                        name="go_submitters_select_0"
                        type="button"
                        value=" Find "
                        onChange={() => {}}
                        title="Find"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          fontFeatureSettings: "inherit",
                          fontVariationSettings: "inherit",
                          lineHeight: "inherit",
                          letterSpacing: "inherit",
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          display: "inline",
                          verticalAlign: "middle",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderColor:
                            "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                          borderImage: "initial",
                          background:
                            "#e8e8e9 /*savepage-url=/img/alohaSkin/btn_sprite.png*/ url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAFKCAMAAAATwz5XAAACFlBMVEWk4/es5vij4/fo+f7x/P/e9//w+//l+P6n5Peq5fjV9f/h+P/g+P/h9/3u+//p+f7f9v3r+v7B8P/q+v/D7/3y/P+u6Puk5fre9v3g9v3M8//J8v/t+v/G8v+/7fzG8P3m+P7R8/7i9/7X9f7n+f+46/zN8v3O9P/K8f216vyx6fum5vrq+f7Y9v/U9P6o5/vb9//2/f/v+//c9v/l+f/a9f6r5/vk+P7S9P/s+v7t+//0/P/D8f/j9/687Pybw8+e0+TS8vujvMO76/mf3fCt5viixM/E7fqpvcSl0+Oe3fDa9Py16Pmr5feo3/DY8/yovcSj0+OnvMSew8+d0uOi3vDU8vym5Pep5PfC7Pql3vCgxM+86/m/7Pmcw8/W8/yg0uOz5/m76vmw5/ilvMSe0uOkvMTT8vy/8P/d9f2e5Prz8/P6+vr7+/v+/v7x8fH9/f309PT29vb5+fn39/fw8PD8/Pzv7+/19fXy8vL4+Pjm5ub////Y2NjOzs7l5eXp6enX19fQ0NDZ2dnPz8/o6Ojk5ORGwOZSyvAjocnh4eEno8z1+//w+f9Nxevr9/9VzPLm9P8tqdH8/v8/uuBDveP6/f/9/v/t9/8bmcMxrdQem8Xg4OD7/f88tt1Kw+n3+//+///y+f/MzMwqps/n9f8gnscZl8Hv+P/z+v/o9f/4/P9QyO7q9v84s9rl9P81sNcXlr8zfejjAAAC00lEQVR42sSUhZUdMRAEO99lRjMzMzPbKbr69N+YAnBpNSr16j7PaS/3ncml8sBeqvZal3VdizmmqWI8ohgL5XleQJcXWvO8W/Ou63J1K8vqob5bDVVt289zO/d9j84eXJqhaRqqsqxBM3bKliXLlmrJFi0VA6pK1cCo2A+aJm8mJjoM1eSqIYWgwavLpMmVE0eKcQTlJuaHkKvvTH4EHhj8uOkACOMEl1ODoVDZIgW9CvQg0J1AzwLdD/Ql0MtAVwN9CvQ60LVAHwM9DHQj0LdAnwO9C3Qr0I/g/+mbQGfPHj9++jj1tI4dO8M4derYGV1+alx1YjvBtVF14vyGujpNIQqEB3VK1bYdzH8WZymOmP+obVMKPXQSzp08B/4R+AfIoi7vcnAzMFldSL36jmzuEVLM7pRicZqylNqcWp2HOo4Hc6B0Do8/A6WnMjq0Noh2N+zkHqfTQawjSiLa3T3PIpqcfjfKC3c9fZ8LQR3Iz5vQ2jNSz/e9re3bVtY01fZ9CzM6u/NdGjX0/jxn9L9sDYLyb4CrIRYTd+wU9c6a4kysLL6TzjbeqcG575Tp1GrzQ3GABFzk8HAgPYOfWosVI/3ZbjxgxxUAABR949q2bZt/jNo2FlLbdmOuMM47WUCc3KP7SjwSNeKF+Ckeivfinfgh6kSD+CLeiH/iqXgsysRX8VmUi0YNdyA1IhJijIiJzWKrWCzWiYVimVgh5ospYp7YJ5aKlWKVmCa2ifVitZgu7mu4/agbxW4xVcTFLjFZLBFbxGgRExvEWjFK7BDLxSwxW8wVe8QkcVe90Uzm6PGbtw+1IJW6cOLy/lZks8eu3jp5oAWl0vlTQS4XBAHF4tkzhGkVCo0cQTjcp00mbaFw7UYy1Ip0+tyli4dbXCefP3Ll9J2DLbijHmtUXRsXm8Q4MUfsFBPFGjFeRMQCkRDbxV6xSIwVM8UEMUPcUy+0GQz0WWinhq+NAAAAAElFTkSuQmCC) repeat-x right top",
                          borderRadius: "3px",
                          color: "rgb(51, 51, 51)",
                          backgroundRepeat: "",
                          fontWeight: "bold",
                          fontSize: "0.9em",
                          padding: "4px 3px",
                          paddingTop: "4px",
                          margin: "0px 2px",
                          marginRight: "2px",
                          appearance: "button",
                          backgroundColor: "transparent",
                          backgroundImage: "none",
                        }}
                      />
                      <br
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                        }}
                      />
                      <div
                        id="error_submitters_select_0"
                        className="errorMsg"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                          color: "rgb(186, 5, 23)",
                          paddingLeft: "3px",
                          visibility: "hidden",
                        }}
                      />
                    </td>
                  </tr>
                  <tr
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                    }}
                  >
                    <td
                      className="labelCol last empty"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        padding: "2px 10px 2px 2px",
                        textAlign: "right",
                        fontSize: "91%",
                        fontWeight: "bold",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        width: "18%",
                        color: "rgb(74, 74, 86)",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        backgroundColor: "transparent",
                        paddingTop: "3px",
                        paddingBottom: "3px",
                      }}
                    ></td>
                    <td
                      className="last data2Col"
                      colSpan="3"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(0, 0, 0)",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        textAlign: "left",
                        width: "82%",
                        padding: "0px 2px 0px 0px",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        backgroundColor: "transparent",
                        paddingTop: "3px",
                        paddingBottom: "3px",
                      }}
                    >
                      <div
                        id="submitters"
                        className="duelingListBox"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                        }}
                      >
                        <div
                          id="stageForm_submitters_errorMsg"
                          className="errorMsg"
                          style={{
                            border: "0px solid rgb(229, 231, 235)",
                            boxSizing: "border-box",
                            borderColor: "hsl(0 0% 85%)",
                            color: "rgb(186, 5, 23)",
                            textAlign: "center",
                            paddingLeft: "3px",
                            display: "none",
                          }}
                        ></div>
                        {/* <APPicklist /> */}
                      </div>
                      <APPicklistContainer />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              id="head_2_ep"
              className="pbSubheader brandTertiaryBgr tertiaryPalette"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
                overflow: "hidden",
                fontWeight: "bold",
                fontSize: "91%",
                marginTop: "15px",
                marginBottom: "2px",
                backgroundColor: "#8e9dbe",
                borderStyle: "solid",
                borderWidth: "1px 0px 0px",
                padding: "4px 16px",
                backgroundImage:
                  "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFElEQVR4AWP4////fmIwnRWOKgQAQ091gLhk6q4AAAAASUVORK5CYII=)",
                color: "rgb(0, 0, 0)",
                borderTop: "1px solid rgb(255, 255, 255)",
                marginLeft: "-12px",
                marginRight: "-12px",
                paddingLeft: "12px",
                paddingRight: "12px",
                borderTopStyle: "solid",
                borderTopColor: "rgb(255, 255, 255)",
              }}
            >
              <h3
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                  margin: "0px",
                  display: "inline",
                  fontWeight: "bold",
                  fontFamily: "Arial, Helvetica, sans-serif",
                  fontSize: "1.2em",
                }}
              >
                Page Layout Settings
                <span
                  className="titleSeparatingColon"
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                    display: "none",
                  }}
                >
                  :
                </span>
              </h3>
            </div>
            <div
              className="pbSubsection"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
              }}
            >
              <table
                className="detailList"
                cellPadding="0"
                cellSpacing="0"
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "inherit",
                  textIndent: "0px",
                  borderCollapse: "collapse",
                  width: "100%",
                }}
              >
                <tbody
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                  }}
                >
                  <tr
                    className="detailRow"
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                      backgroundColor: "transparent",
                    }}
                  >
                    <td
                      colSpan="4"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(0, 0, 0)",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        padding: "10px 0px 0px 16px",
                        display: "flex",
                        alignItems: "center",
                        gap: "0.5em",
                      }}
                    >
                      <input
                        id="addRl"
                        name="addRl"
                        type="checkbox"
                        defaultChecked
                        value={data.step6.pageLayoutSettings}
                        onChange={(e) => {
                          const { checked } = e.target;
                          setData((prev) => ({
                            ...prev,
                            step6: {
                              ...prev.step6,
                              pageLayoutSettings: checked,
                            },
                          }));
                        }}
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                          margin: "0px",
                          padding: "0px",
                          fontFamily: "inherit",
                          fontFeatureSettings: "inherit",
                          fontVariationSettings: "inherit",
                          fontSize: "100%",
                          fontWeight: "inherit",
                          lineHeight: "inherit",
                          letterSpacing: "inherit",
                          color: "inherit",
                          paddingTop: "0px",
                          verticalAlign: "middle",
                          marginRight: "0.25em",
                        }}
                      />
                      <label
                        htmlFor="addRl"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                        }}
                      >
                        Add the Submit for Approval button and Approval History
                        related list to all Opportunity page layouts
                      </label>

                      <div
                        className="mouseOverInfoOuter"
                        checked="checked"
                        tabIndex="0"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                          textDecoration: "none",
                          position: "relative",
                          display: "inline",
                        }}
                      >
                        <img
                          className="infoIcon"
                          src="/assets/downloaded/s.gif"
                          style={{
                            boxSizing: "border-box",
                            borderColor: "hsl(0 0% 85%)",
                            display: "block",
                            maxWidth: "100%",
                            border: "0px",
                            width: "16px",
                            backgroundPosition: "left top",
                            backgroundImage:
                              "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAiCAYAAABWQVnHAAACTUlEQVR42qWU02IlTRSF903wXv8/M28yeI6xbdt2bPfp2LbT1sGeWt1x0qeCiy/Y9a3VLmpsaMhVFOWEmkiMq6rKMuDBR66+ro6orrb29MjwCPu+z+l0Rgo8+MjVVFcTVVVWzrqux6lUSpCWAg8+cpUVFUTlZWXs+kGIF4BkFiIPICeICkzbDbEcbxXb8zj/qMI/1fkN8xV3Q4Fm2CG66WwgZgY2FixoVsiibq/y/4Xu8AyuFU5smK+4GwrmlwwgFswNoOBKwdiG2YorPQOQf6SJrxXIzyD2HuAMbhROSu5BzFM4dKlbnIESlrSOaPFPoay0lG0XCw4bFtrNWLAOD77IAaLSkpJZXTfYNC3WNJ0XF5diwTo8+MgJiIoKC0+3tbbx3Nw8BCnw4BcXFZ0TEBX8/Zv798+fi39+/54SsAx48JETBw8LSAx3DXKAmHlf7L/gYxfT526mL4KvPXLgwUfuXQcTfuR+6uITYmFcCCwDHnzkXrUx0cs2Pq1MMls+cyotBx585J61MNHjZp5d8pidFLMrJfLgI/dQZaL7iWigAdGuZ0EDXuQjJyC6p2R42o6YcXgVy/XwMWFL2zBfcZETRAVjZsS4xasYlrv8Cdsb5iuutODAxWhLu1owvrcCkH8ssb8CbGnXCyb2URBtaZKCmKdw6GLXui1Nz/4UFt0MLzhpnrdTMuDBX1fQlJ6ds1Ksu0lesn1etLxYsA4PPnICoruNydPlgwHPmz52XSnw4IvcubtNKaI7DUHu7Xr/4u06b0rAMuDBR06U0D8ynEueMpfs+wAAAABJRU5ErkJggg==)",
                            height: "15px",
                            marginRight: "0.25em",
                            verticalAlign: "top",
                            marginTop: "3px",
                          }}
                        />
                        <div
                          className="mouseOverInfo"
                          style={{
                            boxSizing: "border-box",
                            border: "1px solid black",
                            whiteSpace: "normal",
                            position: "absolute",
                            left: "22px",
                            bottom: "20px",
                            width: "20em",
                            zIndex: 11,
                            opacity: 0,
                            fontWeight: "normal",
                            color: "rgb(0, 0, 0)",
                            borderColor: "rgb(51, 51, 51)",
                            padding: "8px 10px",
                            borderRadius: "3px",
                            backgroundColor: "rgb(255, 255, 208)",
                            fontSize: "1em",
                            display: "none",
                          }}
                        >
                          These items are added regardless of whether the
                          approval process is active.
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr
                    className="last detailRow"
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                      backgroundColor: "transparent",
                    }}
                  >
                    <td
                      colSpan="4"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(0, 0, 0)",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        padding: "10px 0px 0px 16px",
                      }}
                    ></td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              id="head_3_ep"
              className="pbSubheader brandTertiaryBgr tertiaryPalette"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
                overflow: "hidden",
                fontWeight: "bold",
                fontSize: "91%",
                marginTop: "15px",
                marginBottom: "2px",
                backgroundColor: "#8e9dbe",
                borderStyle: "solid",
                borderWidth: "1px 0px 0px",
                padding: "4px 16px",
                backgroundImage:
                  "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFElEQVR4AWP4////fmIwnRWOKgQAQ091gLhk6q4AAAAASUVORK5CYII=)",
                color: "rgb(0, 0, 0)",
                borderTop: "1px solid rgb(255, 255, 255)",
                marginLeft: "-12px",
                marginRight: "-12px",
                paddingLeft: "12px",
                paddingRight: "12px",
                borderTopStyle: "solid",
                borderTopColor: "rgb(255, 255, 255)",
              }}
            >
              <h3
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                  margin: "0px",
                  display: "inline",
                  fontWeight: "bold",
                  fontFamily: "Arial, Helvetica, sans-serif",
                  fontSize: "1.2em",
                }}
              >
                Submission Settings
                <span
                  className="titleSeparatingColon"
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                    display: "none",
                  }}
                >
                  :
                </span>
              </h3>
            </div>
            <div
              className="pbSubsection"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
              }}
            >
              <table
                className="detailList"
                cellPadding="0"
                cellSpacing="0"
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "inherit",
                  textIndent: "0px",
                  borderCollapse: "collapse",
                  width: "100%",
                }}
              >
                <tbody
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                  }}
                >
                  <tr
                    className="last detailRow"
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                      backgroundColor: "transparent",
                    }}
                  >
                    <td
                      colSpan="4"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        margin: "0px",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(0, 0, 0)",
                        verticalAlign: "top",
                        borderColor: "rgb(236, 236, 236)",
                        borderBottom: "none",
                        borderBottomWidth: "0px",
                        padding: "10px 0px 0px 16px",
                      }}
                    >
                      <input
                        id="recall"
                        name="recall"
                        type="checkbox"
                        value={
                          data.step6.allowSubmittersToRecallApprovalRequests
                        }
                        onChange={(e) => {
                          const { checked } = e.target;
                          setData((prev) => ({
                            ...prev,
                            step6: {
                              ...prev.step6,
                              allowSubmittersToRecallApprovalRequests: checked,
                            },
                          }));
                        }}
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                          margin: "0px",
                          padding: "0px",
                          fontFamily: "inherit",
                          fontFeatureSettings: "inherit",
                          fontVariationSettings: "inherit",
                          fontSize: "100%",
                          fontWeight: "inherit",
                          lineHeight: "inherit",
                          letterSpacing: "inherit",
                          color: "inherit",
                          paddingTop: "0px",
                          verticalAlign: "middle",
                          marginRight: "0.25em",
                        }}
                      />
                      <label
                        htmlFor="recall"
                        style={{
                          border: "0px solid rgb(229, 231, 235)",
                          boxSizing: "border-box",
                          borderColor: "hsl(0 0% 85%)",
                        }}
                      >
                        Allow submitters to recall approval requests
                      </label>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div
            className="pbFooter secondaryPalette"
            style={{
              boxSizing: "border-box",
              borderColor: "hsl(0 0% 85%)",
              backgroundPosition: "right bottom",
              height: "9px",
              width: "9px",
              cssFloat: "right",
              display: "none",
              background: "none",
              border: "none",
              backgroundImage: "none",
              backgroundRepeat: "initial",
              backgroundColor: "initial",
            }}
          >
            <div
              className="bg"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
              }}
            />
          </div>
        </div>
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
html {
  border: 0px solid rgb(229, 231, 235);
  box-sizing: border-box;
  border-color: hsl(0 0% 85%);
  line-height: 1.5;
  text-size-adjust: 100%;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
  overflow: auto;
}

body {
  border: 0px solid rgb(229, 231, 235);
  box-sizing: border-box;
  border-color: hsl(0 0% 85%);
  line-height: inherit;
  min-height: 100vh;
  margin: 0px;
  background-position: left top;
  background-repeat: repeat-x;
  color: rgb(34, 34, 34);
  background-color: rgb(255, 255, 255);
  font-size: 71%;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  -webkit-font-smoothing: antialiased;
}
`,
        }}
      />
    </>
  );
}
