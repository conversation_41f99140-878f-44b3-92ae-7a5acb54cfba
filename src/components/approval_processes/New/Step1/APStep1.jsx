import React, { useContext, useEffect } from "react";
import APForm from "./APForm/APForm";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "../../../../context/GlobalContext";

const APStep1 = () => {
  const { approvalProcessData } = useContext(GlobalContext);
  useEffect(() => {
    approvalProcessData.setData((prev) => ({
      ...prev,
      step1: {
        name: "",
        uniqueName: "",
        desc: "",
      },
    }));
  }, []);
  return (
    <>
      <NavigatonButtons />
      <Component4 />
      <Component5 />
      <NavigatonButtons />

      <style
        dangerouslySetInnerHTML={{
          __html: `
            html {
              overflow: auto;
            }

            body {
              background-position: left top;
              font-size: 71%;
              background: none transparent;
              margin: 0px;
              padding: 0px;
              overflow: auto;
              font-family: Helvetica;
              color: rgb(60, 61, 62);
              background-repeat: initial;
              background-color: transparent;
              height: 100%;
            }
            `,
        }}
      />
    </>
  );
};

export default APStep1;

function NavigatonButtons() {
  const { approvalProcessData } = useContext(GlobalContext);
  const { setErrorsStep1 } = approvalProcessData;
  const navigate = useNavigate();

  const handleNext = (e) => {
    if (
      approvalProcessData.data.step1.name === "" ||
      approvalProcessData.data.step1.uniqueName === ""
    ) {
      setErrorsStep1();
      return;
    }
    navigate("/setup/approval-processes/new/step2");
  };
  const handleCancel = () => {
    navigate("/setup/approval-processes");
  };
  return (
    <>
      <div
        className="pbWizardHeader"
        style={{
          overflow: "hidden",
          height: "1%",
          borderBottom: "1px solid rgb(219, 219, 219)",
          marginBottom: "0px",
          color: "rgb(0, 0, 0)",
          backgroundColor: "rgb(248, 248, 248)",
        }}
      >
        <div
          className="pbTopButtons"
          style={{
            color: "rgb(51, 51, 51)",
            cssFloat: "right",
            padding: "8px 12px",
            margin: "0px",
            whiteSpace: "nowrap",
          }}
        >
          <button
            className="btn"
            onClick={handleNext}
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              background:
                'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
              borderRadius: "3px",
              color: "rgb(51, 51, 51)",
              backgroundImage: 'url("/assets/downloaded/btn_sprite.png")',
              backgroundRepeat: "repeat-x",
              fontWeight: "bold",
              fontSize: "0.9em",
              padding: "4px 3px",
              paddingTop: "4px",
            }}
          >
            Next
          </button>
          <button
            className="btnCancel"
            onClick={handleCancel}
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              borderRadius: "3px",
              background: "none transparent",
              border: "0px none",
              padding: "0px",
              backgroundImage: "none",
              backgroundRepeat: "initial",
              color: "rgb(1, 91, 167)",
              fontWeight: "normal",
              fontSize: "0.9em",
              marginLeft: "7px",
              paddingTop: "0px",
            }}
          >
            Cancel
          </button>
        </div>
      </div>
    </>
  );
}

function Component4() {
  return (
    <>
      <div
        className="pbDescription"
        style={{
          color: "rgb(51, 51, 51)",
          fontSize: "109%",
          clear: "right",
          margin: "0px",
          borderTop: "1px solid rgb(255, 255, 255)",
          borderBottom: "1px solid rgb(219, 219, 219)",
          padding: "10px",
          backgroundColor: "rgb(248, 248, 248)",
        }}
      >
        {"Enter a name and description for your new approval process."}
      </div>
    </>
  );
}

function Component5() {
  const { approvalProcessData } = useContext(GlobalContext);
  const { errors } = approvalProcessData;
  return (
    <>
      <div
        className="pbWizardBody"
        style={{
          clear: "both",
          padding: "8px 12px",
          margin: "0px",
          paddingBottom: "16px",
          borderBottom: "1px solid rgb(219, 219, 219)",
          borderTop: "0px none",
          paddingTop: "0px",
          color: "rgb(0, 0, 0)",
          backgroundColor: "rgb(248, 248, 248)",
        }}
      >
        <div
          id="ep"
          className="bPageBlock brandSecondaryBrd bEditBlock secondaryPalette"
          style={{
            backgroundPosition: "left bottom",
            clear: "both",
            padding: "0px",
            paddingBottom: "0px",
            margin: "0px",
            marginBottom: "0px",
            borderColor: "rgb(116, 126, 150)",
            borderTop: "3px solid rgb(116, 126, 150)",
            borderBottom: "1px solid rgb(234, 234, 234)",
            borderLeft: "1px solid rgb(234, 234, 234)",
            borderRight: "1px solid rgb(234, 234, 234)",
            borderRadius: "4px",
            background: "none",
            backgroundImage: "none",
            backgroundRepeat: "initial",
            backgroundColor: "initial",
            border: "0px none",
            borderTopWidth: "0px",
          }}
        >
          <div
            className="pbHeader pbHeaderEmpty"
            style={{
              margin: "0px",
              borderBottom: "1px solid rgb(255, 255, 255)",
              padding: "0px",
              paddingBottom: "0px",
              color: "rgb(255, 255, 255)",
              background: "none",
              border: "none",
              backgroundColor: "initial",
              display: "none",
            }}
          >
            <table
              cellPadding="0"
              cellSpacing="0"
              style={{
                borderSpacing: "0px",
                width: "100%",
                padding: "5px 0px",
                borderBottom: "1px solid rgb(219, 219, 219)",
              }}
            >
              <tbody>
                <tr>
                  <td
                    className="pbTitle"
                    style={{
                      fontFamily: "Arial, Helvetica, sans-serif",
                      verticalAlign: "middle",
                      fontSize: "91%",
                      width: "30%",
                      color: "rgb(255, 255, 255)",
                      background: "none",
                      padding: "0px",
                      margin: "0px",
                    }}
                  >
                    <img
                      className="minWidth"
                      height={1}
                      width={1}
                      src="/assets/downloaded/s.gif"
                      style={{
                        verticalAlign: "middle",
                        margin: "0px 0px -1px",
                        padding: "0px",
                        border: "0px",
                        height: "1px",
                        width: "190px",
                        marginRight: "0px",
                        visibility: "hidden",
                        display: "block",
                      }}
                    />
                    <h2
                      className="mainTitle"
                      style={{
                        fontWeight: "bold",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        padding: "0px",
                        display: "block",
                        margin: "0px",
                        fontSize: "1.3em",
                        color: "rgb(255, 255, 255)",
                      }}
                    />
                  </td>
                  <td
                    id="topButtonRow"
                    className="pbButton"
                    style={{
                      margin: "0px",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      color: "rgb(0, 0, 0)",
                      verticalAlign: "middle",
                      padding: "5px 12px",
                    }}
                  >
                     
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div
            className="pbBody"
            style={{
              color: "rgb(0, 0, 0)",
              padding: "0px",
              margin: "0px",
              marginRight: "0px",
              background: "none rgb(248, 248, 248)",
              backgroundImage: "none",
              backgroundRepeat: "initial",
              backgroundColor: "rgb(248, 248, 248)",
            }}
          >
            {(errors.step1?.name || errors.step1?.uniqueName) && (
              <div
                id="errorDiv_ep"
                className="pbError"
                tabIndex="-1"
                style={{
                  fontWeight: "bold",
                  color: "rgb(204, 0, 0)",
                  textAlign: "center",
                }}
              >
                Error: Invalid Data. <br />
                Review all error messages below to correct your data.
              </div>
            )}
            <div
              id="head_1_ep"
              className="brandTertiaryBgr first pbSubheader tertiaryPalette"
              style={{
                overflow: "hidden",
                fontWeight: "bold",
                fontSize: "91%",
                marginBottom: "2px",
                marginTop: "0px",
                borderColor: "rgb(142, 157, 190)",
                backgroundColor: "rgb(142, 157, 190)",
                borderStyle: "solid",
                borderWidth: "1px 0px 0px",
                padding: "4px 16px",
                backgroundImage: 'url("/assets/downloaded/opacity75.png")',
                color: "rgb(0, 0, 0)",
                borderTop: "1px solid rgb(255, 255, 255)",
                marginLeft: "-12px",
                marginRight: "-12px",
                paddingLeft: "12px",
                paddingRight: "12px",
                borderTopStyle: "solid",
                borderTopColor: "rgb(255, 255, 255)",
              }}
            >
              <span
                className="pbSubExtra"
                style={{ cssFloat: "right", marginRight: "0px" }}
              >
                <span
                  className="requiredLegend brandTertiaryFgr"
                  style={{
                    padding: "0px 2px",
                    fontWeight: "normal",
                    color: "rgb(0, 0, 0)",
                    backgroundColor: "transparent",
                  }}
                >
                  <span
                    className="requiredExampleOuter"
                    style={{
                      margin: "0px 0.2em 0px 0.3em",
                      padding: "1px 0px",
                    }}
                  >
                    <span
                      className="requiredExample"
                      style={{
                        borderLeft: "3px solid rgb(204, 0, 0)",
                        fontSize: "80%",
                        verticalAlign: "1px",
                        width: "100%",
                      }}
                    >
                       
                    </span>
                  </span>
                  <span
                    className="requiredMark"
                    style={{
                      position: "absolute",
                      display: "inline",
                      color: "rgb(142, 3, 15)",
                      marginRight: "2px",
                    }}
                  >
                    *
                  </span>
                  <span className="requiredText"> = Required Information</span>
                </span>
              </span>
              <h3
                style={{
                  margin: "0px",
                  display: "inline",
                  fontWeight: "bold",
                  fontFamily: "Arial, Helvetica, sans-serif",
                  fontSize: "1.2em",
                }}
              >
                Enter Name and Description
                <span
                  className="titleSeparatingColon"
                  style={{ display: "none" }}
                >
                  :
                </span>
              </h3>
            </div>
            <div className="pbSubsection">
              <table
                className="detailList"
                cellPadding="0"
                cellSpacing="0"
                style={{ width: "100%" }}
              >
                <tbody>
                  <APForm />
                </tbody>
              </table>
            </div>
          </div>
          <div
            className="pbFooter secondaryPalette"
            style={{
              backgroundPosition: "right bottom",
              height: "9px",
              width: "9px",
              cssFloat: "right",
              borderColor: "rgb(116, 126, 150)",
              display: "none",
              background: "none",
              border: "none",
              backgroundImage: "none",
              backgroundRepeat: "initial",
              backgroundColor: "initial",
            }}
          >
            <div className="bg" />
          </div>
        </div>
      </div>
    </>
  );
}
