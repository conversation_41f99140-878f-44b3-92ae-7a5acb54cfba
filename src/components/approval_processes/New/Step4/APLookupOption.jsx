import React from "react";

const APLookupOption = ({ option, type }) => {
  const handleTemplateSelect = (item) => {
    if (window.opener && !window.opener.closed) {
      console.log("item: ", { ...item, type });

      window.opener.postMessage(
        { type: "TEMPLATE_SELECTED", item: { ...item, type } },
        "*"
      );
      window.close();
    }
  };
  return (
    <tr className="dataRow even first">
      <th
        scope="row"
        style={{
          textAlign: "left",
          padding: "4px 2px 4px 5px",
          borderBottom: "1px solid rgb(227, 222, 184)",
          borderColor: "rgb(236, 236, 236)",
          whiteSpace: "normal",
          fontWeight: "normal",
          border: "1px solid rgb(237, 237, 237)",
          color: "rgb(0, 0, 0)",
          borderWidth: "0px 0px 1px",
          verticalAlign: "middle",
        }}
      >
        <a
          className="dataCell"
          href="#"
          style={{ color: "rgb(0, 0, 0)" }}
          onClick={() => handleTemplateSelect(option)}
        >
          {option.name}
        </a>
      </th>
      <td
        className="dataCell"
        style={{
          margin: "0px",
          fontFamily: "Arial, Helvetica, sans-serif",
          padding: "4px 2px 4px 5px",
          borderBottom: "1px solid rgb(227, 222, 184)",
          borderColor: "rgb(236, 236, 236)",
          border: "1px solid rgb(237, 237, 237)",
          color: "rgb(0, 0, 0)",
          borderWidth: "0px 0px 1px",
          verticalAlign: "middle",
        }}
      >
        {option.desc}
      </td>
      <td
        className="dataCell"
        style={{
          margin: "0px",
          fontFamily: "Arial, Helvetica, sans-serif",
          padding: "4px 2px 4px 5px",
          borderBottom: "1px solid rgb(227, 222, 184)",
          borderColor: "rgb(236, 236, 236)",
          border: "1px solid rgb(237, 237, 237)",
          color: "rgb(0, 0, 0)",
          borderWidth: "0px 0px 1px",
          verticalAlign: "middle",
        }}
      >
        {option.template}
      </td>
    </tr>
  );
};

export default APLookupOption;
