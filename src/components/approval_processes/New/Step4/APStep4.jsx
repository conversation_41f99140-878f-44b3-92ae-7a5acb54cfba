import React, { useContext, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "../../../../context/GlobalContext";

const APStep4 = () => {
  const { approvalProcessData, setAllVariableData, saveToLocalStorage } =
    useContext(GlobalContext);
  useEffect(() => {
    setAllVariableData((prev) => {
      prev.approvalProcesses = {
        ...prev.approvalProcesses,
        step4: approvalProcessData.data.step4,
        step5: {},
        step6: {},
      };
      saveToLocalStorage("approvalProcesses", prev.approvalProcesses);
      return { ...prev };
    });
  }, [approvalProcessData.data]);
  return (
    <>
      <NavigatonButtons />
      <Component1 />
      <Component2 />
      <NavigatonButtons />
      <style
        dangerouslySetInnerHTML={{
          __html: `
                    html {
                    border: 0px solid rgb(229, 231, 235);
                    box-sizing: border-box;
                    border-color: hsl(0 0% 85%);
                    line-height: 1.5;
                    text-size-adjust: 100%;
                    tab-size: 4;
                    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                    font-feature-settings: normal;
                    font-variation-settings: normal;
                    -webkit-tap-highlight-color: transparent;
                    overflow: auto;
                    }

                    body {
                    border: 0px solid rgb(229, 231, 235);
                    box-sizing: border-box;
                    border-color: hsl(0 0% 85%);
                    line-height: inherit;
                    min-height: 100vh;
                    margin: 0px;
                    background-position: left top;
                    background-repeat: repeat-x;
                    color: rgb(34, 34, 34);
                    font-size: 71%;
                    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                    -webkit-font-smoothing: antialiased;
                    }
                `,
        }}
      />
    </>
  );
};

export default APStep4;

function NavigatonButtons() {
  const navigate = useNavigate();
  const handleNext = () => {
    navigate("/setup/approval-processes/new/step5");
  };
  const handleCancel = () => {
    navigate("/setup/approval-processes");
  };
  const handlePrevious = () => {
    navigate("/setup/approval-processes/new/step3");
  };
  return (
    <>
      <div
        className="pbWizardHeader"
        style={{
          overflow: "hidden",
          height: "1%",
          borderBottom: "1px solid rgb(219, 219, 219)",
          marginBottom: "0px",
          color: "rgb(0, 0, 0)",
          backgroundColor: "rgb(248, 248, 248)",
        }}
      >
        <div
          className="pbTopButtons"
          style={{
            color: "rgb(51, 51, 51)",
            cssFloat: "right",
            padding: "8px 12px",
            margin: "0px",
            whiteSpace: "nowrap",
          }}
        >
          <button
            className="btn"
            onClick={handlePrevious}
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              background:
                'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
              borderRadius: "3px",
              color: "rgb(51, 51, 51)",
              backgroundImage: 'url("/assets/downloaded/btn_sprite.png")',
              backgroundRepeat: "repeat-x",
              fontWeight: "bold",
              fontSize: "0.9em",
              padding: "4px 3px",
              paddingTop: "4px",
            }}
          >
            Previous
          </button>
          <button
            className="btn"
            onClick={handleNext}
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              background:
                'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
              borderRadius: "3px",
              color: "rgb(51, 51, 51)",
              backgroundImage: 'url("/assets/downloaded/btn_sprite.png")',
              backgroundRepeat: "repeat-x",
              fontWeight: "bold",
              fontSize: "0.9em",
              padding: "4px 3px",
              paddingTop: "4px",
            }}
          >
            Next
          </button>
          <button
            className="btnCancel"
            onClick={handleCancel}
            style={{
              backgroundPosition: "left top",
              borderRight: "1px solid rgb(92, 93, 97)",
              borderBottom: "1px solid rgb(92, 93, 97)",
              borderTop: "none",
              borderLeft: "none",
              cursor: "pointer",
              display: "inline",
              fontFamily: "Arial, Helvetica, sans-serif",
              margin: "1px",
              borderWidth: "1px",
              borderStyle: "solid",
              borderColor:
                "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
              borderImage: "initial",
              borderRadius: "3px",
              background: "none transparent",
              border: "0px none",
              padding: "0px",
              backgroundImage: "none",
              backgroundRepeat: "initial",
              color: "rgb(1, 91, 167)",
              fontWeight: "normal",
              fontSize: "0.9em",
              marginLeft: "7px",
              paddingTop: "0px",
            }}
          >
            Cancel
          </button>
        </div>
      </div>
    </>
  );
}

function Component1() {
  return (
    <>
      <div
        className="pbDescription"
        style={{
          border: "0px solid rgb(229, 231, 235)",
          boxSizing: "border-box",
          borderColor: "hsl(0 0% 85%)",
          color: "rgb(51, 51, 51)",
          fontSize: "109%",
          clear: "right",
          padding: "8px 12px",
          margin: "0px",
          borderTop: "1px solid rgb(255, 255, 255)",
          borderBottom: "1px solid rgb(219, 219, 219)",
        }}
      >
        {
          "Select the email template that will be used to notify approvers that an approval request has been assigned to them. Note that this template will be used for all steps for this process. Create a new email template"
        }
      </div>
    </>
  );
}
function Component2() {
  const [selectedOption, setSelectedOption] = useState(null);
  const { approvalProcessData } = useContext(GlobalContext);
  const handleLookupClick = (event) => {
    event.preventDefault(); // Prevent default behavior
    event.stopPropagation();
    const lookupWindow = window.open(
      "/setup/approval-processes/lookup",
      "ApprovalAssignmentEmailTemplate",
      "width=780,height=500,resizable=yes,scrollbars=yes"
    );
    lookupWindow?.focus();
  };
  useEffect(() => {
    approvalProcessData.setData((prev) => ({
      ...prev,
      step4: { approvalAssignmentEmailTemplate: selectedOption },
    }));
    const handleMessage = (event) => {
      if (event.data.type === "TEMPLATE_SELECTED") {
        const selected = event.data.item;
        setSelectedOption(selected);
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, [selectedOption]);
  return (
    <>
      <div
        className="pbWizardBody"
        style={{
          border: "0px solid rgb(229, 231, 235)",
          boxSizing: "border-box",
          borderColor: "hsl(0 0% 85%)",
          clear: "both",
          padding: "8px 12px",
          margin: "0px",
          paddingBottom: "16px",
          borderTop: "1px solid rgb(255, 255, 255)",
          borderBottom: "1px solid rgb(219, 219, 219)",
          color: "rgb(0, 0, 0)",
          backgroundColor: "rgb(248, 248, 248)",
        }}
      >
        <div
          id="ep"
          className="bPageBlock brandSecondaryBrd bEditBlock secondaryPalette"
          style={{
            boxSizing: "border-box",
            borderColor: "hsl(0 0% 85%)",
            borderTop: "4px solid rgb(34, 34, 34)",
            backgroundPosition: "left bottom",
            clear: "both",
            padding: "0px",
            paddingBottom: "0px",
            margin: "0px",
            marginBottom: "0px",
            borderBottom: "1px solid rgb(234, 234, 234)",
            borderLeft: "1px solid rgb(234, 234, 234)",
            borderRight: "1px solid rgb(234, 234, 234)",
            borderRadius: "4px",
            background: "none",
            backgroundImage: "none",
            backgroundRepeat: "initial",
            backgroundColor: "initial",
            border: "0px none",
            borderTopWidth: "0px",
          }}
        >
          <div
            className="pbHeader pbHeaderEmpty"
            style={{
              boxSizing: "border-box",
              borderColor: "hsl(0 0% 85%)",
              margin: "0px",
              borderBottom: "1px solid rgb(255, 255, 255)",
              padding: "0px",
              paddingBottom: "0px",
              color: "rgb(255, 255, 255)",
              background: "none",
              border: "none",
              backgroundColor: "initial",
              display: "none",
            }}
          >
            <table
              cellPadding="0"
              cellSpacing="0"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "inherit",
                textIndent: "0px",
                borderCollapse: "collapse",
                borderSpacing: "0px",
                width: "100%",
                padding: "5px 0px",
                borderBottom: "1px solid rgb(219, 219, 219)",
              }}
            >
              <tbody
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                }}
              >
                <tr
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                  }}
                >
                  <td
                    className="pbTitle"
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      verticalAlign: "middle",
                      fontSize: "91%",
                      width: "30%",
                      color: "rgb(255, 255, 255)",
                      background: "none",
                      padding: "0px",
                      margin: "0px",
                    }}
                  >
                    <img
                      className="minWidth"
                      height={1}
                      width={1}
                      src="/assets/downloaded/s.gif"
                      style={{
                        boxSizing: "border-box",
                        borderColor: "hsl(0 0% 85%)",
                        maxWidth: "100%",
                        verticalAlign: "middle",
                        margin: "0px 0px -1px",
                        padding: "0px",
                        border: "0px",
                        height: "1px",
                        width: "190px",
                        marginRight: "0px",
                        visibility: "hidden",
                        display: "block",
                        backgroundColor: "#8e9dbe",
                      }}
                    />
                    <h2
                      className="mainTitle"
                      style={{
                        border: "0px solid rgb(229, 231, 235)",
                        boxSizing: "border-box",
                        borderColor: "hsl(0 0% 85%)",
                        fontWeight: "bold",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        padding: "0px",
                        display: "block",
                        margin: "0px",
                        fontSize: "1.3em",
                        color: "rgb(255, 255, 255)",
                      }}
                    />
                  </td>
                  <td
                    id="topButtonRow"
                    className="pbButton"
                    style={{
                      border: "0px solid rgb(229, 231, 235)",
                      boxSizing: "border-box",
                      borderColor: "hsl(0 0% 85%)",
                      margin: "0px",
                      fontFamily: "Arial, Helvetica, sans-serif",
                      color: "rgb(0, 0, 0)",
                      verticalAlign: "middle",
                      padding: "5px 12px",
                    }}
                  >
                     
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div
            className="pbBody"
            style={{
              border: "0px solid rgb(229, 231, 235)",
              boxSizing: "border-box",
              borderColor: "hsl(0 0% 85%)",
              color: "rgb(0, 0, 0)",
              padding: "0px",
              margin: "0px",
              marginRight: "0px",
              background: "none rgb(248, 248, 248)",
              backgroundImage: "none",
              backgroundRepeat: "initial",
              backgroundColor: "rgb(248, 248, 248)",
            }}
          >
            <div
              id="errorDiv_ep"
              className="pbError"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
                fontWeight: "bold",
                color: "rgb(204, 0, 0)",
                textAlign: "center",
                display: "none",
              }}
            >
              Error: Invalid Data.{" "}
              <br
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                }}
              />
              Review all error messages below to correct your data.
            </div>
            <div
              id="head_1_ep"
              className="brandTertiaryBgr first pbSubheader tertiaryPalette"
              style={{
                border: "0px solid rgb(229, 231, 235)",
                boxSizing: "border-box",
                borderColor: "hsl(0 0% 85%)",
                overflow: "hidden",
                fontWeight: "bold",
                fontSize: "91%",
                marginBottom: "2px",
                marginTop: "0px",
                borderStyle: "solid",
                borderWidth: "1px 0px 0px",
                padding: "4px 16px",
                backgroundImage:
                  "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFElEQVR4AWP4////fmIwnRWOKgQAQ091gLhk6q4AAAAASUVORK5CYII=)",
                color: "rgb(0, 0, 0)",
                borderTop: "1px solid rgb(255, 255, 255)",
                marginLeft: "-12px",
                marginRight: "-12px",
                paddingLeft: "12px",
                paddingRight: "12px",
                borderTopStyle: "solid",
                borderTopColor: "rgb(255, 255, 255)",
                backgroundColor: "#8e9dbe",
              }}
            >
              <h3
                style={{
                  border: "0px solid rgb(229, 231, 235)",
                  boxSizing: "border-box",
                  borderColor: "hsl(0 0% 85%)",
                  margin: "0px",
                  display: "inline",
                  fontWeight: "bold",
                  fontFamily: "Arial, Helvetica, sans-serif",
                  fontSize: "1.2em",
                }}
              >
                Email Template
                <span
                  className="titleSeparatingColon"
                  style={{
                    border: "0px solid rgb(229, 231, 235)",
                    boxSizing: "border-box",
                    borderColor: "hsl(0 0% 85%)",
                    display: "none",
                  }}
                >
                  :
                </span>
              </h3>
            </div>
            <div
              style={{
                display: "flex",
                gap: "15px",
                marginTop: "5px",
                marginLeft: "70px",
              }}
            >
              <div>
                <h3 style={{ color: "#4a4a56", fontWeight: "900" }}>
                  Approval Assignment Email Template{" "}
                </h3>
              </div>
              <div style={{ display: "flex", gap: "5px" }}>
                <input
                  type=""
                  name=""
                  value={!!selectedOption ? selectedOption.name : ""}
                  readOnly
                />
                <div
                  onClick={handleLookupClick}
                  style={{
                    backgroundImage: `url("/assets/images/lookup.gif")`,
                    width: "20px",
                    height: "20px",
                    cursor: "pointer",
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
