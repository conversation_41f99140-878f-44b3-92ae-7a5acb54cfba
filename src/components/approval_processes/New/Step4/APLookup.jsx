import React, { useContext, useEffect, useState } from "react";
import lookupOptions from "../../data/LoopupData.json";
import APLookupOption from "./APLookupOption";
const APLookup = () => {
  const [templatesType, setTemplatesType] = useState("classic");

  return (
    <>
      <div
        style={{
          //   backgroundColor: "white",
          minHeight: "100vh",
          padding: 0,
          margin: 0,
          display: "relative",
          overflow: "hidden",
        }}
      >
        <div style={{ height: "20vh", display: "fixed" }}>
          <Component1 />
          <Component2
            templatesType={templatesType}
            setTemplatesType={setTemplatesType}
          />
        </div>
        <div style={{ height: "80vh", overflow: "auto" }}>
          <Component3
            templatesType={templatesType}
            setTemplatesType={setTemplatesType}
          />
        </div>
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
            body {
            background-position: left top;
            font-size: 75%;
            font-family: Arial, Helvetica, sans-serif;
            color: rgb(34, 34, 34);
            background: url("/assets/images/lookup_bg.png") repeat-x rgb(255, 255, 255);
            padding: 0px;
            background-repeat: repeat-x;
            background-color: rgb(255, 255, 255);
            }
            `,
        }}
      />
    </>
  );
};

export default APLookup;

function Component1() {
  return (
    <>
      <div
        className="bPageTitle"
        style={{
          margin: "0px",
          marginBottom: "0px",
          padding: "0 15px",
        }}
      >
        <div
          className="ptBody secondaryPalette"
          style={{
            overflow: "hidden",
            width: "100%",
            borderColor: "rgb(3, 45, 96)",
            padding: "0px",
            backgroundColor: "transparent",
            color: "rgb(0, 0, 0)",
            paddingTop: "0px",
            paddingBottom: "0px",
          }}
        >
          <div
            className="content"
            style={{
              cssFloat: "left",
              verticalAlign: "middle",
              width: "70%",
              paddingLeft: "0px",
            }}
          >
            <img
              className="pageTitleIcon"
              src="/assets/downloaded/s.gif"
              style={{
                border: "0px",
                display: "inline",
                cssFloat: "left",
                marginRight: "5px",
                backgroundPosition: "0px -1202px",
                backgroundImage: 'url("/assets/downloaded/master.png")',
                width: "32px",
                height: "32px",
                marginTop: "5px",
              }}
            />
            <h1
              style={{
                fontWeight: "bold",
                fontFamily: "Arial, Helvetica, sans-serif",
                display: "block",
                margin: "8px 0px 4px",
                fontSize: "1.8em",
                color: "rgb(51, 52, 53)",
              }}
            >
              Lookup
            </h1>
          </div>
        </div>
      </div>
    </>
  );
}

function Component2({ templatesType, setTemplatesType }) {
  return (
    <>
      <div
        className="pBody"
        style={{
          fontWeight: "bold",
          padding: "0 15px",
          paddingBottom: "5px",
        }}
      >
        <select
          id="folderFilterElement"
          name="folderFilterElement"
          value={templatesType}
          onChange={(e) => {
            setTemplatesType(e.target.value);
          }}
          aria-label="Select Lightning or Classic email templates"
          style={{ color: "rgb(0, 0, 0)", margin: "0px 0.65em 0px 0px" }}
        >
          <option value="classic" style={{ fontSize: "100%" }}>
            Classic
          </option>
          <option value="lightening" style={{ fontSize: "100%" }}>
            Lightning
          </option>
        </select>
        <select
          id="classicFilterElement"
          name="fcf"
          value="00DgK000000DdjV"
          onChange={() => {}}
          aria-label="Select an email template folder"
          style={{
            color: "rgb(0, 0, 0)",
            margin: "0px 0.65em 0px 0px",
            display: "inline-block",
          }}
        >
          <option value="00DgK000000DdjV" style={{ fontSize: "100%" }}>
            Unfiled Public Classic Email Templates
          </option>
        </select>
        <select
          id="lightningFilterElement"
          name="fcf"
          value="00DgK000000DdjV"
          onChange={() => {}}
          aria-label="Select an email template folder"
          style={{
            color: "rgb(0, 0, 0)",
            margin: "0px 0.65em 0px 0px",
            display: "none",
          }}
        >
          <option value="00DgK000000DdjV" style={{ fontSize: "100%" }}>
            Public Email Templates
          </option>
        </select>{" "}
        <label
          className="assistiveText"
          htmlFor="lksrch"
          style={{
            overflow: "hidden",
            height: "1px",
            width: "1px",
            clip: "rect(1px, 1px, 1px, 1px)",
            margin: "0px 5px 0px 0px",
            color: "rgb(74, 74, 86)",
            fontSize: "0.9em",
            position: "absolute",
          }}
        >
          Search
        </label>
        <input
          id="lksrch"
          name="lksrch"
          type="text"
          maxLength="80"
          placeholder="Search this folder..."
          aria-label="Search this folder "
          size={20}
          style={{ paddingTop: "0px", margin: "0px 0.65em 0px 0px" }}
        />
        <input
          className="btn"
          name="go"
          type="submit"
          value=" Go! "
          onChange={() => {}}
          title="Go!"
          style={{
            backgroundPosition: "left top",
            borderRight: "1px solid rgb(92, 93, 97)",
            borderBottom: "1px solid rgb(92, 93, 97)",
            borderTop: "none",
            borderLeft: "none",
            cursor: "pointer",
            display: "inline",
            fontFamily: "Arial, Helvetica, sans-serif",
            margin: "1px",
            borderWidth: "1px",
            borderStyle: "solid",
            borderColor:
              "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
            borderImage: "initial",
            background:
              'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
            borderRadius: "3px",
            color: "rgb(51, 51, 51)",
            backgroundImage: 'url("/assets/downloaded/btn_sprite.png")',
            backgroundRepeat: "repeat-x",
            fontWeight: "bold",
            fontSize: "0.9em",
            padding: "4px 3px",
            paddingTop: "4px",
          }}
        />
        <div
          className="bDescription"
          style={{
            textAlign: "left",
            padding: "0px",
            fontWeight: "normal",
            paddingTop: "5px",
            paddingBottom: "0px",
            margin: "0px",
            fontSize: "0.9em",
            paddingLeft: "0px",
          }}
        >
          You can use "*" as a wildcard next to other characters to improve your
          search results.
        </div>
      </div>
    </>
  );
}

function Component3({ templatesType, setTemplatesType }) {
  return (
    <>
      <form
        id="new"
        name="new"
        acceptCharset="UTF-8"
        action="/_ui/common/data/LookupResultsFrame"
        encType="application/x-www-form-urlencoded"
        method="GET"
        style={{
          margin: "0px",
          marginTop: "30px",
          padding: "0 15px",
          paddingBottom: "10px",
        }}
      >
        <input
          id="_CONFIRMATIONTOKEN"
          name="_CONFIRMATIONTOKEN"
          type="hidden"
          value="VmpFPSxNakF5TlMwd05TMHhOVlF3TURveE9Ub3dPUzR5TXpWYSxta05VZDc4R1lDZW0zU292ZkNKNklnbmUtNEh4QzMwM05QR3RTejRBYTZnPSxNVFppT0RBNQ=="
          onChange={() => {}}
          style={{ paddingTop: "0px" }}
        />
        <input
          id="lkenhmd"
          name="lkenhmd"
          type="hidden"
          value="SEARCH_NAME"
          onChange={() => {}}
          style={{ paddingTop: "0px" }}
        />
        <input
          id="lkfm"
          name="lkfm"
          type="hidden"
          value="stageForm"
          onChange={() => {}}
          style={{ paddingTop: "0px" }}
        />
        <input
          id="lknm"
          name="lknm"
          type="hidden"
          value="email"
          onChange={() => {}}
          style={{ paddingTop: "0px" }}
        />
        <input
          id="lkrf"
          name="lkrf"
          type="hidden"
          style={{ paddingTop: "0px" }}
        />
        <input
          id="lksrch"
          name="lksrch"
          type="hidden"
          value="Appointment for Unauthenticated User using Engagement Channels-For Amazon Chime."
          style={{ paddingTop: "0px", margin: "0px 0.65em 0px 0px" }}
        />
        <input
          id="lktp"
          name="lktp"
          type="hidden"
          value="00X"
          onChange={() => {}}
          style={{ paddingTop: "0px", margin: "0px 0.65em 0px 0px" }}
        />
        <div
          className="lookup"
          style={{
            padding: "0px",
            paddingBottom: "0px",
            paddingLeft: "0px",
            width: "100%",
            display: "table",
            overflow: "auto",
          }}
        >
          <div
            className="srch"
            style={{
              padding: "4px 3px",
              fontFamily: "Arial, Helvetica, sans-serif",
              fontWeight: "bold",
              fontSize: "118%",
            }}
          >
            <h3
              style={{
                margin: "0px",
                fontSize: "100%",
                display: "inline",
                fontWeight: "bold",
                fontFamily: "Arial, Helvetica, sans-serif",
              }}
            >
              Recently Viewed Email Templates
            </h3>
          </div>
          <div className="listRelatedObject lookupBlock">
            <div
              className="bPageBlock brandSecondaryBrd secondaryPalette"
              style={{
                backgroundPosition: "left bottom",
                backgroundRepeat: "no-repeat",
                clear: "both",
                margin: "0px",
                padding: "0px",
                marginBottom: "10px",
                paddingRight: "0px",
                paddingBottom: "0px",
                backgroundImage: "none",
                borderColor: "rgb(3, 45, 96)",
                borderTop: "3px solid rgb(3, 45, 96)",
                borderTopWidth: "3px",
                borderBottom: "1px solid rgb(234, 234, 234)",
                borderLeft: "1px solid rgb(234, 234, 234)",
                borderRight: "1px solid rgb(234, 234, 234)",
                borderRadius: "4px",
                backgroundColor: "rgb(248, 248, 248)",
                borderBottomWidth: "1px",
                borderBottomStyle: "solid",
                borderLeftColor: "rgb(224, 227, 229)",
                borderRightColor: "rgb(224, 227, 229)",
                borderBottomColor: "rgb(224, 227, 229)",
              }}
            >
              <div
                className="pbHeader"
                style={{
                  margin: "0px",
                  backgroundColor: "transparent",
                  display: "none",
                  borderBottom: "none",
                  padding: "0px",
                  paddingBottom: "0px",
                  border: "0px",
                }}
              />
              <div
                className="pbBody"
                style={{
                  color: "rgb(0, 0, 0)",
                  backgroundColor: "transparent",
                  backgroundImage: "none",
                  padding: "0px",
                  margin: "0px",
                  marginRight: "0px",
                }}
              >
                <table
                  className="list"
                  cellPadding="0"
                  cellSpacing="0"
                  style={{
                    width: "100%",
                    borderTop: "1px solid rgb(224, 227, 229)",
                    backgroundColor: "rgb(255, 255, 255)",
                    border: "1px solid rgb(224, 227, 229)",
                  }}
                >
                  <tbody>
                    <tr className="headerRow">
                      <th
                        className="zen-deemphasize"
                        scope="col"
                        style={{
                          textAlign: "left",
                          borderBottom: "2px solid rgb(204, 204, 204)",
                          whiteSpace: "nowrap",
                          border: "1px solid rgb(237, 237, 237)",
                          background: "rgb(242, 243, 243)",
                          borderWidth: "0px 0px 1px 1px",
                          borderColor: "rgb(224, 227, 229)",
                          padding: "5px 2px 4px 5px",
                          color: "rgb(0, 0, 0)",
                          fontSize: "0.9em",
                          fontWeight: "bold",
                          borderLeftWidth: "0px",
                        }}
                      >
                        Name
                      </th>
                      <th
                        className="zen-deemphasize"
                        scope="col"
                        style={{
                          textAlign: "left",
                          borderBottom: "2px solid rgb(204, 204, 204)",
                          whiteSpace: "nowrap",
                          border: "1px solid rgb(237, 237, 237)",
                          background: "rgb(242, 243, 243)",
                          borderWidth: "0px 0px 1px 1px",
                          borderColor: "rgb(224, 227, 229)",
                          padding: "5px 2px 4px 5px",
                          color: "rgb(0, 0, 0)",
                          fontSize: "0.9em",
                          fontWeight: "bold",
                        }}
                      >
                        Description
                      </th>
                      <th
                        className="zen-deemphasize"
                        scope="col"
                        style={{
                          textAlign: "left",
                          borderBottom: "2px solid rgb(204, 204, 204)",
                          whiteSpace: "nowrap",
                          border: "1px solid rgb(237, 237, 237)",
                          background: "rgb(242, 243, 243)",
                          borderWidth: "0px 0px 1px 1px",
                          borderColor: "rgb(224, 227, 229)",
                          padding: "5px 2px 4px 5px",
                          color: "rgb(0, 0, 0)",
                          fontSize: "0.9em",
                          fontWeight: "bold",
                        }}
                      >
                        Template Type
                      </th>
                    </tr>
                    {lookupOptions[templatesType].length === 0 && (
                      <tr>
                        <td
                          style={{
                            padding: "3px",
                          }}
                        >
                          <p>No records found due to one of the following:</p>
                          <ul
                            style={{
                              listStyle: "disc",
                              marginLeft: "70px",
                              marginTop: "20px",
                              padding: "5px",
                            }}
                          >
                            <li>There are no records of this type</li>
                            <li>
                              You don't have permission to see any of the
                              records
                            </li>
                          </ul>
                        </td>
                      </tr>
                    )}
                    {lookupOptions[templatesType].map((option) => (
                      <APLookupOption
                        key={option.id}
                        option={option}
                        type={templatesType}
                      />
                    ))}
                  </tbody>
                </table>
              </div>
              <div
                className="pbFooter secondaryPalette"
                style={{
                  backgroundPosition: "right bottom",
                  height: "9px",
                  width: "9px",
                  cssFloat: "right",
                  backgroundImage:
                    'url("/assets/downloaded/bgPageBlockRight.gif")',
                  backgroundRepeat: "repeat-x",
                  borderColor: "rgb(3, 45, 96)",
                  backgroundColor: "rgb(3, 45, 96)",
                  display: "none",
                }}
              >
                <div className="bg" />
              </div>
            </div>
          </div>
          <div
            className="footer"
            style={{
              borderTop: "2px solid rgb(217, 217, 217)",
              marginTop: "20px",
              paddingTop: "0.5em",
              color: "rgb(135, 135, 135)",
              border: "0px",
              textAlign: "center",
              fontSize: "0.8em",
            }}
          >
            Copyright © 2000-2025 salesforce.com, inc. All rights reserved.
          </div>
        </div>
      </form>
    </>
  );
}
