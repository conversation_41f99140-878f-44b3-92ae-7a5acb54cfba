{"classic": [{"id": 1, "name": "Appointment for Unauthenticated User using Appointment Types - For Amazon Chime.", "desc": "Email template for confirmation of an appointment...", "template": "Custom"}, {"id": 2, "name": "Appointment for Unauthenticated User using Appointment Types - For third party.", "desc": "Email template for confirmation of an appointment...", "template": "Custom"}, {"id": 3, "name": "Appointment for Unauthenticated User using Engagement Channels-For Amazon Chime.", "desc": "Email template for confirmation of an appointment...", "template": "Custom"}, {"id": 4, "name": "Appointment for Unauthenticated User using Engagement Channels-For third party.", "desc": "Email template for confirmation of an appointment...", "template": "Custom"}, {"id": 5, "name": "Canceled Service Appointment Confirmation Email", "desc": "Email Template to confirm canceling of a service ...", "template": "Custom"}, {"id": 6, "name": "Commerce Reorder Portal: Invitation", "desc": "Invite a contact to a Commerce Reorder Portal. ", "template": "Custom"}, {"id": 7, "name": "Group Service Appointments Enrollment Confirmation Email", "desc": "Email Template to confirm enrollment of an attend...", "template": "Custom"}, {"id": 8, "name": "Marketing: Product Inquiry Response", "desc": "Standard email response to website product inquir...", "template": "Text"}, {"id": 9, "name": "Rescheduled Service Appointment Confirmation Email", "desc": "Email Template to confirm rescheduling of a servi...", "template": "Custom"}, {"id": 10, "name": "Sales: New Customer Email", "desc": "Email to new customers", "template": "Text"}, {"id": 11, "name": "Scheduled Service Appointment Confirmation Email", "desc": "Email Template to confirm scheduling of a service...", "template": "Custom"}, {"id": 12, "name": "Scheduler Payments: Payment Reminder for Service Appointment Email", "desc": "Email Template to remind customers to pay for the...", "template": "Custom"}, {"id": 13, "name": "Scheduler Payments: Service Appointment Cancellation Email", "desc": "Email Template to confirm the cancellation of a p...", "template": "Custom"}, {"id": 14, "name": "Scheduler Payments: Service Appointment Rescheduled Email - Authenticated Users", "desc": "Email Template to confirm the rescheduling of a p...", "template": "Custom"}, {"id": 15, "name": "Scheduler Payments: Service Appointment Rescheduled Email - Guest Users", "desc": "Email Template to confirm the rescheduling of a p...", "template": "Custom"}, {"id": 16, "name": "SUPPORT: Self-Service New Comment Notification (SAMPLE)", "desc": "Sample email template that can be sent to your Se...", "template": "Text"}, {"id": 17, "name": "SUPPORT: Self-Service New User Login Information (SAMPLE)", "desc": "Notification of login and password to new Self-Se...", "template": "Text"}, {"id": 18, "name": "SUPPORT: Self-Service Reset Password (SAMPLE)", "desc": "Notification of new password when Self-Service pa...", "template": "Text"}, {"id": 19, "name": "Support: Case Assignment Notification", "desc": "Notification to rep when case is auto-assigned", "template": "Text"}, {"id": 20, "name": "Support: Case Created (Phone Inquiries)", "desc": "Notification to customer about case created throu...", "template": "Text"}, {"id": 21, "name": "Support: Case Created (Web Inquiries)", "desc": "Notification to customer about case created onlin...", "template": "Text"}, {"id": 22, "name": "Support: Case Response", "desc": "Standardized template for responses to customer i...", "template": "Text"}, {"id": 23, "name": "Support: Escalated Case Notification", "desc": "Notification email on case escalation", "template": "Text"}, {"id": 24, "name": "Support: Escalated Case Reassignment", "desc": "Case reassignment email to new case owner on esca...", "template": "Text"}, {"id": 25, "name": "Support: Self-Service New Login and Password", "desc": "Notification of login and password to new self-se...", "template": "Text"}, {"id": 26, "name": "Support: Self-Service Reset Password", "desc": "Notification of new password when self-service pa...", "template": "Text"}], "lightening": [{"id": 1, "name": "High-value Opportunity Notification", "desc": "", "template": "Custom"}, {"id": 2, "name": "Opportunity Pending Approval Submitter", "desc": "Notification for the submitter", "template": "Custom"}]}