["Account", "Activation", "Activation Target", "Activation Target Platform", "Activation Target Platform Field Value", "Address", "AI Evaluation", "AI Evaluation Copilot Test Case Result", "AI Evaluation Prompt RAG Test Case Result", "AI Evaluation Prompt Test Case Result", "AI Evaluation Test Case Criteria Result", "AI Evaluation Test Case Result", "AI Grounding File Reference", "Analytics User Attribute Function Token", "Appointment Invitation", "Appointment Invitee", "Appointment Schedule Log", "Approval Submission", "Approval Submission Detail", "Approval Work Item", "<PERSON><PERSON>", "Asset Action", "Asset Action Source", "Asset Relationship", "Asset State Period", "Assigned Resource", "Assistant Progress", "Associated Location", "Authorization Form", "Authorization Form Consent", "Authorization Form Data Use", "Authorization Form Text", "Bec", "Broadcast Conversation", "Broadcast Message", "Business Brand", "Buyer Group", "Calculated Insight", "Campaign", "<PERSON><PERSON>", "Cart Adjustment Basis", "Cart Adjustment Group", "Cart Checkout Session", "Cart Delivery Group", "Cart Delivery Group Method", "Cart Delivery Group Method Adjustment", "<PERSON><PERSON>em", "Cart Item Price Adjustment", "Cart Related Item", "Cart Tax", "Cart Validation Output", "Case", "Catalog", "Category", "Change Request", "Communication Capping", "Communication Subscription", "Communication Subscription Channel Type", "Communication Subscription Consent", "Communication Subscription Timing", "Consumption Rate", "Consumption Schedule", "Contact", "Contact Point Address", "Contact Point Consent", "Contact Point Email", "Contact Point Phone", "Contact Point Type Consent", "Contract", "Conversation Api Log", "Conversation Api Log Object Summary", "Coupon", "Coupon Code Redemption", "Credit Memo", "Credit Memo Invoice Application", "Credit Memo Line", "Customer", "Custom Library", "Data Action", " Data Action Job Summary ", "Data Action Target", "Data Communication Capping Activation Target", "Data Graph", "Data Kit", "Data Kit Deployment Log", "Data Lake Object", "Data Lineage Definition Sync Log", "Data Lineage Node Definition Sync Log", "Data Model Relation Constraint", "Data Share", "Data Share Target", "Data Source Bundle", "Data Stream", "Data Transform", "Data Use Legal Basis", "Data Use Purpose", "Delivery Estimation Setup", "Duplicate Record Item", "Duplicate Record Set", "Email Message", "Enablement Program", "Enablement Program Section", "Enablement Program Task Definition", "Enablement Program Task Progress", "Engagement Channel Type", "Engagement Channel Work Type", "Entitlement", "Entity", "External Event Mapping", "File Based Data Import", "Finance Balance Snapshot", "Finance Transaction", "Fulfillment Order", "Fulfillment Order Item Adjustment", "Fulfillment Order Item Tax", "Fulfillment Order Product", "Groject", "Identity Resolution", "Image", "Inventory Item Reservation", "Inventory Reservation", "Invoice", "Invoice Line", "Lead", "Learning Item", "Learning Item Assignment", "Learning Item Progress", "Legal Entity", "Location", "Location Group", "Location Group Assignment", "Location Shipping Carrier Method", "Macro Usage", "Managed Content Translation Job Language", "Market Segment Field", "Messaging Session", "Messaging User", "ML Model", "ML Model Factor", "ML Model Factor Component", "ML Model Partition Run", "Mobile Home Configuration", "Model", "Model Prediction Job", "Object Milestone", "Opportunity", "Orchestration Run", "Orchestration Work Item", "Order", "Org Delete Request", "<PERSON><PERSON>", "<PERSON><PERSON>", "Party Consent", "Payment Gateway Credential Store", "Pending Service Routing", "Privacy Processor Orchestrator", "Privacy RTBF Request", "Process Exception", "Product", "Product Category Product", "Proj", "Project", "<PERSON>j<PERSON>", "Promotion", "Promotion Line Item Rule", "Promotion Market Segment", "Promotion Qualifier", "Promotion Segment", "Promotion Segment Buyer Group", "Promotion Segment Sales Store", "Promotion Target", "Promotion Tier", "Prompt Action", "Prompt Error", "Query Editor", "Queue", "Queued Party", "Queue Messaging Template", "Quick Text Usage", "Rebate Payout Snapshot", "Record Merge History", "Request Access to SIQ Object", "Resource Absence", "Resource Preference", "Return Order", "Return Order Item Adjustment", "Return Order Item Tax", "Return Order Line Item", "saa", "SearchRecencyIndexingJob", "Security Center Alert Rule Selected Tenant", "Security Center Api Anomaly", "Security Center Certificate", "Security Center Connected App", "Security Center Credential Stuffing", "Security Center Custom Metric Detail", "Security Center Custom Metric Setup", "Security Center Custom Metric Statistic", "Security Center Encrypted Field", "Security Center Encryption Policy", "Security Center Guest User Anomaly", "Security Center Health Check Baseline Trend", "Security Center Health Check Detail", "Security Center Health Check Trend", "Security Center License", "Security Center Login", "Security Center Login IP Range Trend", "Security Center Metric Detail Link", "Security Center Mobile Policy Trend", "Security Center Monitor Metric", "Security Center Notification", "Security Center Notification Rule", "Security Center Package", "Security Center Policy", "Security Center Policy Change Log", "Security Center Policy Deployment", "Security Center Policy Selected Tenant", "Security Center Report Anomaly", "Security Center Session Hijacking", "Security Center Tenant Change Log", "Security Center Tenant Information", "Security Center Transaction Policy Trend", "Security Center Triggered Transaction Security Policy", "Security Center Trusted IP Range Trend", "Security Center User Activity", "Security Center User Permission", "Security Center Website", "Segment", "<PERSON><PERSON>", "Service Appointment", "Service Appointment Attendee", "Service Appointment Shift", "Service Contract", "Service Resource", "Service Resource Skill", "Service Territory", "Service Territory Member", "Service Territory Work Type", "Shift", "Shift Engagement Channel", "Shift Work Topic", "Shipment", "Shipment Item", "Shipping Carrier", "Shipping Carrier Method", "Shipping Configuration Set", "Shipping Rate Area", "Shipping Rate Group", "Site User ViewMode", "Skill Requirement", "Solution", "Standard Shipping Rate", "Start Data", "Store", "Store Buyer Group", "Store Catalog", "Store Managed Content Channel", "Streaming Channel", "Tableau Host Mapping", "Tenant Security Feature", "Tenant Security Metric Detail", "User Capability Preference", "User External Credential", "User Local Web Server Identity", "User Mapping", "User Presence", "User Prioritized Record", "User Provisioning Request", "Waitlist", "Waitlist Participant", "Waitlist Service Resource", "Waitlist Work Type", "Web Cart Document", "Web Store Inventory Source", "Work Order", "Work Order Line Item", "Work Plan", "Work Plan Template", "Work Plan Template Entry", "Work Step", "Work Step Template", "Work Type", "Work Type Group", "Work Type Group Member"]