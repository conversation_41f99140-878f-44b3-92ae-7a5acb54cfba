"use client";

import React from "react";
import SetupHome from "../components/MainContent/SetupHome";

import Sidebar from "../components/sidebar";
import TabsetHeader from "../components/TabsetHeader/TabsetHeader";
import GlobalHeaderComponent from "../components/GlobalHeader/GlobalHeader";
import styled from "styled-components";
import NewEmailAlert from "./NewEmailAlert";

const PageLayout = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
`;

const HeadersContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
`;

const MainContainer = styled.div`
  display: flex;
  flex: 1;
  margin-top: 92px; /* Height of GlobalHeader (50px) + TabsetHeader (45px) */
  min-height: calc(100vh - 92px);
`;

const ContentArea = styled.main`
  flex: 1;
  margin-left: 250px; /* Width of sidebar */
  padding: 1rem;
  background-image: url("/assets/images/lightning_blue_background.png");
  background-repeat: no-repeat;
  background-color: #b0c4e0;
  min-height: calc(100vh - 92px);
  overflow-y: auto;
`;

export default function NewEmailAlertPage() {
  return (
    <PageLayout>
      <HeadersContainer>
        <GlobalHeaderComponent />
        <TabsetHeader />
      </HeadersContainer>
      <MainContainer>
        <Sidebar />
        <ContentArea>
          <SetupHome title="Email Alerts" showActions={false} />
          <NewEmailAlert />
        </ContentArea>
      </MainContainer>
    </PageLayout>
  );
}
