"use client";

import React from "react";
import { useState, useEffect, useContext, useRef } from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import { GlobalContext } from "../../context/GlobalContext";
import Sidebar from "../Sidebar";
import ApprovalHeader from "../NewFieldUpdate/ApprovalHeader";

const Container = styled.div`
  width: 100%;
  border-radius: 4px;
  margin-bottom: 12px;
  background-color: #fff;
  padding: 0 12px;
  height: calc(100vh - 205px);
  font-family: -apple-system, system-ui, "Segoe UI", Roboto, sans-serif;
  overflow: auto;
  display: flex;
  overflow-x: hidden;
  flex-direction: column;
`;
const ContentArea = styled.main`
  flex: 1;
  padding: 1rem;
  background-image: url("/assets/images/themes/oneSalesforce/lightning_blue_background.png");
  background-repeat: no-repeat;
  background-color: #b0c4e0;
  height: auto;
  overflow-y: auto;
`;
const Header = styled.div`
  display: flex;
  padding: 0;
  justify-content: space-between;
  margin-bottom: 0px;
`;

const Title = styled.h1`
  font-size: 22.72px;
  font-weight: 400;
  margin-bottom: 15px;
  padding-top: 15px;
  font-family: Arial, Helvetica, sans-serif;
  color: #1a202c;
`;

const HelpLink = styled.div`
  display: flex;
  padding-top: 15px;
  margin-bottom: 15px;
  align-items: center;
  color: rgb(1, 91, 167);
  cursor: pointer;
  font-size: 10.3px;
  font-family: Helvetica;

  &:hover {
    text-decoration: underline;
  }
`;

const Description = styled.p`
  color: #3c3d3e;
  font-size: 11.36px;
  font-family: Helvetica, sans-serif;
  margin-bottom: 12px;

  line-height: 1.5;
`;

const EditSection = styled.div`
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: #fff;
  padding-bottom: 6px;
  margin-bottom: 16px;
`;

const SectionHeader = styled.div`
  background-color: #e2e6ef;
  padding: 8px;
  font-weight: 700;
  font-size: 11.36px;
  font-family: Helvetica;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const EditHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #d1d5db;
`;
const ArrowIcon = styled.div`
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  ${(props) =>
    props.direction === "right"
      ? `
    border-left: 6px solid #666;
  `
      : `
    border-right: 6px solid #666;
  `}
`;

const EditTitle = styled.div`
  font-size: 13.4px;
  font-family: Arial, Helvetica, sans-serif;
  color: #000;
  font-weight: 700;
  white-space: nowrap;
`;

const RequiredInfo = styled.div`
  font-size: 10.36px;
  color: black;
  font-family: Helvetica;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 4px;

  &::before {
    content: "";
    display: inline-block;
    width: 3px;
    height: 12px;
    background-color: #c00;
    margin-right: 4px;
  }
`;

const FormGrid = styled.div`
  padding: 16px;
`;

const FormRow = styled.div`
  display: flex;
  margin: 12px 0;
  align-items: start;
`;

const Label = styled.label`
  width: 200px;
  text-align: right;
  margin-left: 5px;
  font-weight: 700;
  padding-right: 16px;
  color: #4a4a56;
  font-size: 10.36px;
  font-family: Arial, Helvetica, sans-serif;
  padding-top: 2px;
`;

const Input = styled.input`
  width: 430px;
  padding: 2px 4px;
  border: 1px solid #000;
  font-size: 11.36px;
  font-family: Arial, Helvetica, sans-serif;
`;

// Update the RequiredInput component to handle error states
const RequiredInput = styled.div`
  position: relative;
  display: inline-block;

  ${(props) =>
    props.$beforeSearch
      ? `
    &::before {
      content: "";
      position: absolute;
      left: -4px;
      top: 0;
      bottom: 0;
      width: 3px;
      background: #c00;
    }
  `
      : props.$hasError
      ? `
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      bottom: -24px; /* Extend past the input to include error message */
      width: 3px;
      background: #c00;
    }
    input, select, textarea {
      border-left: 3px solid #c00;
    }
  `
      : `
    input, select, textarea {
      margin-left: 1px;
      border-left: 3px solid #c00;
    }
  `}
`;

const Select = styled.select`
  width: 430px;
  padding: 2px 4px;
  border: 1px solid #000;
  font-size: 11.36px;
  font-family: Arial, Helvetica, sans-serif;
  background: white;
`;

// Update the RecipientSection to handle error states
const RecipientSection = styled.div`
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 16px;
  align-items: start;
  margin-left: 15px;
  position: relative;

  ${(props) =>
    props.$hasError &&
    `
    &::before {
      content: "";
      position: absolute;
      right: 0;
      top: 0;
      bottom: -24px;
      width: 3px;
      background: #c00;
    }
  `}
`;

const ListBox = styled.select`
  width: 100%;
  height: 150px;
  border: 1px solid #000;
  font-size: 11.36px;
  font-family: Arial, Helvetica, sans-serif;
  background: white;
`;

const TransferButtons = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 0;
`;

const TransferButton = styled.button`
  width: 24px;
  height: 24px;
  padding: 0;
  background-color: rgb(232, 232, 233);
  background: #e8e8e9 url(/assets/images/btn_sprite.png) repeat-x right top;
  color: rgb(51, 51, 51);
  border: 1px solid rgb(199, 199, 199);
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #e5e7eb;
  }
`;

const ButtonLabel = styled.div`
  font-size: 11.36px;
  color: #000;
  font-family: Helvetica;
`;

const TextArea = styled.textarea`
  width: 430px;
  height: 100px;
  padding: 2px 4px;
  border: 1px solid #000;
  font-size: 11.36px;
  font-family: Arial, Helvetica, sans-serif;
  resize: vertical;
`;

const SearchSection = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
`;

const SearchLabel = styled.span`
  font-size: 11.36px;
  color: #000;
  font-family: Helvetica;
`;

const SearchInput = styled(Input)`
  width: 200px;
`;

const Button = styled.button`
  background-color: rgb(232, 232, 233);
  background: #e8e8e9 url(/assets/images/btn_sprite.png) repeat-x right top;
  color: rgb(51, 51, 51);
  border: 1px solid rgb(199, 199, 199);
  border-radius: 0.25rem;

  font-family: Arial, Helvetica, sans-serif;
  font-size: 10.36px;
  font-weight: 700;
  cursor: pointer;
  border-bottom-color: rgb(127, 127, 127);
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-image-outset: 0;
  border-image-repeat: stretch;
  border-image-slice: 100%;
  border-image-source: none;
  border-image-width: 1;
  border-left-color: rgb(181, 181, 181);
  border-left-style: solid;
  border-left-width: 1px;
  border-right-color: rgb(181, 181, 181);
  border-right-style: solid;
  border-right-width: 1px;
  border-top-color: rgb(181, 181, 181);
  border-top-style: solid;
  border-top-width: 1px;
  cursor: pointer;
  appearance: auto;
  text-rendering: auto;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  overflow-clip-margin: 0px;
  padding-block-end: 4px;
  padding-block-start: 4px;
  padding-inline-end: 3px;
  padding-inline-start: 3px;
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: center;
  gap: 8px;
  width: 100%;
`;

const Checkbox = styled.input.attrs({ type: "checkbox" })`
  margin-right: 8px;
`;

const ListBoxLabel = styled.div`
  font-size: 11.36px;
  color: #000;
  font-family: Arial, Helvetica, sans-serif;
  margin-bottom: 2px;
  font-weight: 700;
`;

const ErrorBanner = styled.div`
  background-color: #fff;
  color: #c00;
  padding: 8px 16px;
  margin: 8px 16px;
  border: 1px solid #c00;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  font-family: Helvetica;
`;

// Update the ErrorMessage component to align with the red border
const ErrorMessage = styled.div`
  color: #c00;
  font-size: 11.36px;
  font-family: Helvetica;
  margin-top: 4px;
  margin-left: 8px; /* Add left margin to align with input */
`;

const recipientTypeOptions = [
  "Account Owner",
  "Case Team",
  "Creator",
  "Email Field",
  "Owner",
  "Public Groups",
  "Related Contact",
  "Related User",
  "Role",
  "Role and Internal Subordinates",
  "User",
];

export default function NewEmailAlert() {
  const navigate = useNavigate();
  const {
    saveToLocalStorage,
    allVariableData,
    setAllVariableData,
    trackInteraction,
  } = useContext(GlobalContext);

  // State management following NewFieldUpdate pattern
  const [availableRecipients, setAvailableRecipients] = useState([]);
  const [selectedRecipients, setSelectedRecipients] = useState([]);
  const [selectedAvailable, setSelectedAvailable] = useState([]);
  const [selectedSelected, setSelectedSelected] = useState([]);
  const [uniqueName, setUniqueName] = useState("");
  const [description, setDescription] = useState("");
  const [emailTemplate, setEmailTemplate] = useState("");
  const [fromName, setFromName] = useState("");
  const [fromEmail, setFromEmail] = useState("Current User's email address");
  const [additionalEmails, setAdditionalEmails] = useState("");
  const [isDefault, setIsDefault] = useState(false);
  const [isProtected, setIsProtected] = useState(false);
  const [recipientType, setRecipientType] = useState("User");
  const [errors, setErrors] = useState({});
  const [showErrors, setShowErrors] = useState(false);

  // Approval process integration states
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isNavigating, setIsNavigating] = useState(false);
  const [approvalActionSection, setApprovalActionSection] = useState(null);

  // Add ref to track programmatic resets (following NewFieldUpdate pattern)
  const isProgrammaticResetRef = useRef(false);

  // Initialize approval action section from localStorage
  useEffect(() => {
    const section = localStorage.getItem("approvalActionSection");
    if (section) {
      setApprovalActionSection(section);
    }
    setIsLoading(false);
  }, []);

  // Initialize the global window.currentInfo object and form data
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Check if we have saved data in localStorage
      const savedCurrentInfo = localStorage.getItem("currentInfo");

      if (savedCurrentInfo) {
        // Restore from localStorage
        window.currentInfo = JSON.parse(savedCurrentInfo);

        // Populate form fields with saved data
        if (window.currentInfo && window.currentInfo.emailAlert) {
          const emailAlert = window.currentInfo.emailAlert;
          setUniqueName(emailAlert.uniqueName || "");
          setDescription(emailAlert.description || "");
          setEmailTemplate(emailAlert.emailTemplate || "");
          setFromName(emailAlert.fromName || "");
          setFromEmail(emailAlert.fromEmail || "Current User's email address");
          setSelectedRecipients(emailAlert.recipients || []);
          setAdditionalEmails(emailAlert.additionalEmails || "");
          setIsDefault(emailAlert.isDefault || false);
          setIsProtected(emailAlert.isProtected || false);
          setRecipientType(emailAlert.recipientType || "User");
        }
      } else {
        // Initialize the global object if it doesn't exist
        window.currentInfo = {
          emailAlert: {
            uniqueName: "",
            description: "",
            emailTemplate: "",
            fromName: "",
            fromEmail: "",
            recipients: [],
            additionalEmails: "",
            isDefault: false,
            isProtected: false,
            recipientType: "User",
          },
          workflowRule: {
            ruleName: "",
            description: "",
            selectedObject: "",
            evaluationCriteria: "",
            isActive: false,
            criteriaLogic: "criteria_are_met",
            filters: [],
            actions: [],
          },
        };
      }
    }
  }, []);

  // Update the global window.currentInfo object and allVariableData whenever form values change
  useEffect(() => {
    // Skip updates during programmatic resets (following NewFieldUpdate pattern)
    if (isProgrammaticResetRef.current) {
      return;
    }

    if (typeof window !== "undefined" && window.currentInfo) {
      window.currentInfo.emailAlert = {
        uniqueName,
        description,
        emailTemplate,
        fromName,
        fromEmail,
        recipients: selectedRecipients,
        additionalEmails,
        isDefault,
        isProtected,
        recipientType,
      };

      // Save to localStorage
      localStorage.setItem("currentInfo", JSON.stringify(window.currentInfo));
    }

    // Also update allVariableData.emailAlert (following NewFieldUpdate pattern)
    if (typeof window !== "undefined") {
      const existingData = JSON.parse(
        localStorage.getItem("allVariableData") || "{}"
      );

      const updatedData = {
        ...existingData,
        emailAlert: {
          uniqueName,
          description,
          emailTemplate,
          fromName,
          fromEmail,
          recipients: selectedRecipients,
          additionalEmails,
          isDefault,
          isProtected,
          recipientType,
          // Track the source approval action section if available
          approvalActionSection: approvalActionSection || null,
          // Track the source approval process if available
          sourceApprovalProcessUniqueName:
            approvalActionSection &&
            allVariableData.approvalProcessesList?.length > 0
              ? allVariableData.approvalProcessesList[
                  allVariableData.approvalProcessesList.length - 1
                ]?.step1?.uniqueName || null
              : null,
        },
      };

      window.allVariableData = updatedData;
      saveToLocalStorage("emailAlert", updatedData.emailAlert);
    }
  }, [
    uniqueName,
    description,
    emailTemplate,
    fromName,
    fromEmail,
    selectedRecipients,
    additionalEmails,
    isDefault,
    isProtected,
    recipientType,
    approvalActionSection,
    allVariableData.approvalProcessesList,
  ]);

  useEffect(() => {
    if (recipientType === "Account Owner") {
      setAvailableRecipients(["Account Owner"]);
    } else {
      setAvailableRecipients([
        "User: Integration User",
        "User: Security User",
        "User: seven steven",
      ]);
    }
  }, [recipientType]);

  useEffect(() => {
    // Listen for messages from the lookup window
    const handleMessage = (event) => {
      if (event.data.type === "TEMPLATE_SELECTED") {
        setEmailTemplate(event.data.templateName);
        // Clear any email template error when a template is selected
        setErrors((prev) => ({ ...prev, emailTemplate: "" }));
      }
    };

    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, []);

  const generateUniqueNameFromDescription = (description) => {
    if (!description || typeof description !== "string") {
      return "";
    }
    return (description || "")
      .replace(/[^a-zA-Z0-9_ ]/g, "") // Keep letters, numbers, underscore, space
      .replace(/ /g, "_") // Convert spaces to underscores
      .replace(/__+/g, "_") // Collapse multiple underscores
      .replace(/^_+|_+$/g, "") // Trim leading/trailing underscores
      .substring(0, 40); // Limit to 40 characters
  };

  const validateUniqueName = (name) => {
    const regex = /^[a-zA-Z][a-zA-Z0-9_]*[a-zA-Z0-9]$/;
    const hasConsecutiveUnderscores = /__/.test(name);
    return regex.test(name) && !hasConsecutiveUnderscores;
  };

  const handleDescriptionChange = (e) => {
    const newDescriptionValue = e.target.value;
    const oldDescriptionValue = description;

    setDescription(newDescriptionValue);

    const currentUniqueNameValue = uniqueName;

    const oldGeneratedUniqueName =
      generateUniqueNameFromDescription(oldDescriptionValue);

    const newGeneratedUniqueName =
      generateUniqueNameFromDescription(newDescriptionValue);

    if (
      !currentUniqueNameValue ||
      currentUniqueNameValue === oldGeneratedUniqueName
    ) {
      setUniqueName(newGeneratedUniqueName);
    }

    trackInteraction({
      type: "field_input",
      action: "update_email_alert_description",
      component: "NewEmailAlert",
      data: {
        previousValue: oldDescriptionValue,
        newValue: newDescriptionValue,
        fieldName: "description",
      },
    });
  };

  const handleUniqueNameChange = (e) => {
    const previousValue = uniqueName;
    const newValue = e.target.value;
    setUniqueName(newValue);

    // Track the interaction (following NewFieldUpdate pattern)
    trackInteraction({
      type: "field_input",
      action: "update_email_alert_unique_name",
      component: "NewEmailAlert",
      data: {
        previousValue,
        newValue,
        fieldName: "uniqueName",
        isValid: validateUniqueName(newValue),
      },
    });
  };

  const validate = () => {
    const newErrors = {};
    let hasErrors = false;

    if (!description) {
      newErrors.description = "Error: You must enter a value";
      hasErrors = true;
    }

    if (!uniqueName || !validateUniqueName(uniqueName)) {
      newErrors.uniqueName =
        "Error: The Unique Name field can only contain underscores and alphanumeric characters. It must be unique, begin with a letter, not include spaces, not end with an underscore, and not contain two consecutive underscores.";
      hasErrors = true;
    }

    if (!emailTemplate) {
      newErrors.emailTemplate = "Error: You must select an email template";
      hasErrors = true;
    }

    if (!fromEmail) {
      newErrors.fromEmail = "Error: You must select a from email address";
      hasErrors = true;
    }

    if (selectedRecipients.length === 0 && !additionalEmails.trim()) {
      newErrors.recipients =
        "Error: At least one recipient or additional email is required";
      hasErrors = true;
    }

    setErrors(newErrors);
    setShowErrors(hasErrors);
    return !hasErrors;
  };

  const handleCancel = () => {
    // Check if we're coming from an approval process
    if (approvalActionSection) {
      // Navigate back to approval process detail page
      navigate("/setup/approval-processes/detail");
    } else {
      console.log("Canceling email alert creation");
    }
  };

  const handleSave = () => {
    if (validate()) {
      setIsSaving(true);

      // Create a complete email alert data object
      const emailAlertDataObject = {
        uniqueName,
        description,
        emailTemplate,
        fromName,
        fromEmail,
        recipients: selectedRecipients,
        additionalEmails,
        isDefault,
        isProtected,
        recipientType,
        // Track the source approval action section if available
        approvalActionSection: approvalActionSection || null,
        // Track the source approval process if available
        sourceApprovalProcessUniqueName:
          approvalActionSection &&
          allVariableData.approvalProcessesList?.length > 0
            ? allVariableData.approvalProcessesList[
                allVariableData.approvalProcessesList.length - 1
              ]?.step1?.uniqueName || null
            : null,
      };

      // Save to emailAlertList in allVariableData
      const updatedEmailAlertList = [
        ...(allVariableData.emailAlertList || []),
        emailAlertDataObject,
      ];

      saveToLocalStorage("emailAlertList", updatedEmailAlertList);

      // Reset the emailAlert object in global state (following NewFieldUpdate pattern)
      isProgrammaticResetRef.current = true;
      saveToLocalStorage("emailAlert", {});

      setAllVariableData((prev) => ({
        ...prev,
        emailAlertList: updatedEmailAlertList,
        emailAlert: {}, // Reset to empty object after saving
      }));

      // Reset the programmatic flag after a brief delay
      setTimeout(() => {
        isProgrammaticResetRef.current = false;
      }, 0);

      // Track the interaction
      trackInteraction({
        type: "form_submission",
        action: "save_email_alert",
        component: "NewEmailAlert",
        data: {
          uniqueName: emailAlertDataObject.uniqueName,
          description: emailAlertDataObject.description,
          approvalActionSection: emailAlertDataObject.approvalActionSection,
        },
      });

      // Immediate navigation setup for approval process context
      if (approvalActionSection) {
        setIsNavigating(true);

        try {
          const latestData = JSON.parse(
            localStorage.getItem("allVariableData") || "{}"
          );

          // Create an email alert action object
          const emailAlertAction = {
            type: "Email Alert",
            uniqueName: emailAlertDataObject.uniqueName,
            description: emailAlertDataObject.description,
            emailTemplate: emailAlertDataObject.emailTemplate,
            recipients: emailAlertDataObject.recipients,
            // Include source tracking information
            approvalActionSection: emailAlertDataObject.approvalActionSection,
            sourceApprovalProcessUniqueName:
              emailAlertDataObject.sourceApprovalProcessUniqueName,
          };

          // Get the current approval processes list
          const approvalProcessesList = latestData.approvalProcessesList || [];

          if (approvalProcessesList.length > 0) {
            // Get the last approval process
            const lastIndex = approvalProcessesList.length - 1;
            const approvalProcess = approvalProcessesList[lastIndex];

            // Initialize the appropriate actions array if it doesn't exist
            if (!approvalProcess[approvalActionSection]) {
              approvalProcess[approvalActionSection] = [];
            }

            // Add the email alert action to the appropriate section
            try {
              approvalProcess[approvalActionSection].push(emailAlertAction);
            } catch (error) {
              // Create the array if it's not properly initialized
              approvalProcess[approvalActionSection] = [emailAlertAction];
            }

            // Save the updated approval processes list
            saveToLocalStorage("approvalProcessesList", approvalProcessesList);

            // Update the context state as well
            setAllVariableData((prev) => ({
              ...prev,
              approvalProcessesList,
            }));
          }

          // Clear the approval action section from localStorage
          localStorage.removeItem("approvalActionSection");

          // Navigate immediately after data operations
          navigate("/setup/approval-processes/detail");
        } catch (error) {
          // Silent error handling - still navigate on error
          navigate("/setup/approval-processes/detail");
        }
      } else {
        console.log("Canceling email alert creation");
        setTimeout(() => {
          setIsSaving(false);
          setIsLoading(false);
        }, 1000);
      }
    } else {
      setShowErrors(true);
    }
  };

  const handleSaveAndNew = () => {
    if (validate()) {
      setIsSaving(true);

      // Create a complete email alert data object
      const emailAlertDataObject = {
        uniqueName,
        description,
        emailTemplate,
        fromName,
        fromEmail,
        recipients: selectedRecipients,
        additionalEmails,
        isDefault,
        isProtected,
        recipientType,
        // Track the source approval action section if available
        approvalActionSection: approvalActionSection || null,
        // Track the source approval process if available
        sourceApprovalProcessUniqueName:
          approvalActionSection &&
          allVariableData.approvalProcessesList?.length > 0
            ? allVariableData.approvalProcessesList[
                allVariableData.approvalProcessesList.length - 1
              ]?.step1?.uniqueName || null
            : null,
      };

      // Save to emailAlertList in allVariableData
      const updatedEmailAlertList = [
        ...(allVariableData.emailAlertList || []),
        emailAlertDataObject,
      ];

      saveToLocalStorage("emailAlertList", updatedEmailAlertList);

      // Reset the emailAlert object in global state (following NewFieldUpdate pattern)
      isProgrammaticResetRef.current = true;
      saveToLocalStorage("emailAlert", {});

      setAllVariableData((prev) => ({
        ...prev,
        emailAlertList: updatedEmailAlertList,
        emailAlert: {}, // Reset to empty object after saving
      }));

      // Reset the programmatic flag after a brief delay
      setTimeout(() => {
        isProgrammaticResetRef.current = false;
      }, 0);

      // Immediate navigation setup for approval process context
      if (approvalActionSection) {
        setIsNavigating(true);

        try {
          const latestData = JSON.parse(
            localStorage.getItem("allVariableData") || "{}"
          );

          // Create an email alert action object
          const emailAlertAction = {
            type: "Email Alert",
            uniqueName: emailAlertDataObject.uniqueName,
            description: emailAlertDataObject.description,
            emailTemplate: emailAlertDataObject.emailTemplate,
            recipients: emailAlertDataObject.recipients,
            // Include source tracking information
            approvalActionSection: emailAlertDataObject.approvalActionSection,
            sourceApprovalProcessUniqueName:
              emailAlertDataObject.sourceApprovalProcessUniqueName,
          };

          // Get the current approval processes list
          const approvalProcessesList = latestData.approvalProcessesList || [];

          if (approvalProcessesList.length > 0) {
            // Get the last approval process
            const lastIndex = approvalProcessesList.length - 1;
            const approvalProcess = approvalProcessesList[lastIndex];

            // Initialize the appropriate actions array if it doesn't exist
            if (!approvalProcess[approvalActionSection]) {
              approvalProcess[approvalActionSection] = [];
            }

            // Add the email alert action to the appropriate section
            try {
              approvalProcess[approvalActionSection].push(emailAlertAction);
            } catch (error) {
              console.error(
                `Error adding email alert to ${approvalActionSection}:`,
                error
              );
              // Create the array if it's not properly initialized
              approvalProcess[approvalActionSection] = [emailAlertAction];
            }

            // Save the updated approval processes list
            saveToLocalStorage("approvalProcessesList", approvalProcessesList);

            // Update the context state as well
            setAllVariableData((prev) => ({
              ...prev,
              approvalProcessesList,
            }));
          }

          // Clear the approval action section from localStorage
          localStorage.removeItem("approvalActionSection");

          // Track the interaction before navigation
          trackInteraction({
            type: "form_reset",
            action: "reset_email_alert_form",
            component: "NewEmailAlert",
            data: {
              reason: "save_and_new",
            },
          });

          // Navigate immediately after data operations
          navigate("/setup/approval-processes/detail");
        } catch (error) {
          // Silent error handling - still navigate on error
          navigate("/setup/approval-processes/detail");
        }
      } else {
        // For non-approval process context, clear form for new email alert
        setTimeout(() => {
          trackInteraction({
            type: "form_reset",
            action: "reset_email_alert_form",
            component: "NewEmailAlert",
            data: {
              reason: "save_and_new",
            },
          });

          // Clear form for new email alert (following NewFieldUpdate pattern)
          isProgrammaticResetRef.current = true;

          setUniqueName("");
          setDescription("");
          setEmailTemplate("");
          setFromName("");
          setFromEmail("Current User's email address");
          setSelectedRecipients([]);
          setAdditionalEmails("");
          setIsDefault(false);
          setIsProtected(false);
          setRecipientType("User");
          setErrors({});
          setShowErrors(false);
          setIsSaving(false);
          setIsLoading(false);

          // Reset the programmatic flag after form reset
          setTimeout(() => {
            isProgrammaticResetRef.current = false;
          }, 0);
        }, 100);
      }
    } else {
      setShowErrors(true);
    }
  };

  const handleAdd = () => {
    if (selectedAvailable.length === 0) return;
    setSelectedRecipients([...selectedRecipients, ...selectedAvailable]);
    setAvailableRecipients(
      availableRecipients.filter((r) => !selectedAvailable.includes(r))
    );
    setSelectedAvailable([]);
  };

  const handleRemove = () => {
    if (selectedSelected.length === 0) return;
    setAvailableRecipients([...availableRecipients, ...selectedSelected]);
    setSelectedRecipients(
      selectedRecipients.filter((r) => !selectedSelected.includes(r))
    );
    setSelectedSelected([]);
  };

  const handleLookupClick = () => {
    const lookupWindow = window.open(
      "/NewEmailAlert/Lookup",
      "EmailTemplateLookup",
      "width=800,height=600,resizable=yes,scrollbars=yes"
    );
    if (lookupWindow) {
      lookupWindow.focus();
    }
  };

  return (
    <div
      style={{
        boxSizing: "border-box",
        position: "relative",
        left: "var(--lwc-setupSplitViewWidth,250px)",
        width: "calc(100% - var(--lwc-setupSplitViewWidth,250px))",
        backgroundImage:
          "url('/assets/images/themes/oneSalesforce/lightning_blue_background.png')",
        backgroundSize: "cover",
        marginTop: "85px",
      }}
    >
      <Sidebar />
      <ContentArea>
        <ApprovalHeader />
        {isLoading || isNavigating ? (
          <Container></Container>
        ) : (
          <Container>
            <Header>
              <Title>New Email Alert</Title>
              <HelpLink>
                Help for this Page
                <div
                  style={{
                    backgroundImage: `url("/assets/images/icons_img.png")`,
                    backgroundPosition: "0 -142px",
                    width: "20px",
                    height: "16px",
                    marginLeft: "4px",
                  }}
                />{" "}
              </HelpLink>
            </Header>

            <Description>
              Create an email alert to associate with one or more workflow
              rules, approval processes, or entitlement processes. When changing
              an email alert, any modifications will apply to all rules,
              approvals, or entitlement processes associated with it.
            </Description>

            <EditSection>
              <EditHeader>
                <EditTitle>Email Alert Edit</EditTitle>
                <ButtonGroup>
                  <Button onClick={handleSave}>Save</Button>
                  <Button onClick={handleSaveAndNew}>Save & New</Button>
                  <Button onClick={handleCancel}>Cancel</Button>
                </ButtonGroup>
              </EditHeader>

              {showErrors && (
                <ErrorBanner>
                  Error: Invalid Data.
                  <br />
                  Review all error messages below to correct your data.
                </ErrorBanner>
              )}

              <SectionHeader>
                <div
                  style={{
                    flex: "no-wrap",
                    flexShrink: 0,
                    paddingLeft: "10px",
                  }}
                >
                  Edit Email Alert
                </div>
                <RequiredInfo>= Required Information</RequiredInfo>
              </SectionHeader>

              <FormGrid>
                <FormRow>
                  <Label>Description</Label>
                  <div>
                    <RequiredInput $hasError={!!errors.description}>
                      <Input
                        value={description}
                        onChange={handleDescriptionChange}
                      />
                    </RequiredInput>
                    {errors.description && (
                      <ErrorMessage>{errors.description}</ErrorMessage>
                    )}
                  </div>
                </FormRow>
                <FormRow>
                  <Label>Unique Name</Label>
                  <div>
                    <RequiredInput $hasError={!!errors.uniqueName}>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "8px",
                        }}
                      >
                        <Input
                          value={uniqueName}
                          onChange={handleUniqueNameChange}
                        />
                        <div
                          style={{
                            backgroundImage: `url("/assets/images/info_sprite.png")`,
                            backgroundPosition: "left top",
                            width: "16px",
                            height: "15px",
                          }}
                        />
                      </div>
                    </RequiredInput>
                    {errors.uniqueName && (
                      <ErrorMessage>{errors.uniqueName}</ErrorMessage>
                    )}
                  </div>
                </FormRow>
                <FormRow>
                  <Label>Object</Label>
                  <div>Opportunity</div>
                </FormRow>
                <FormRow>
                  <Label>Email Template</Label>
                  <div>
                    <RequiredInput $hasError={!!errors.emailTemplate}>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "8px",
                        }}
                      >
                        <Input
                          value={emailTemplate}
                          onChange={(e) => setEmailTemplate(e.target.value)}
                          readOnly
                        />
                        <div
                          onClick={handleLookupClick}
                          style={{
                            backgroundImage: `url("/assets/images/lookup.gif")`,
                            width: "20px",
                            height: "20px",
                            cursor: "pointer",
                          }}
                        />
                      </div>
                    </RequiredInput>
                    {errors.emailTemplate && (
                      <ErrorMessage>{errors.emailTemplate}</ErrorMessage>
                    )}
                  </div>
                </FormRow>
                <FormRow>
                  <Label>Protected Component</Label>
                  <Checkbox
                    checked={isProtected}
                    onChange={(e) => setIsProtected(e.target.checked)}
                  />
                </FormRow>
                <FormRow>
                  <Label>Recipient Type</Label>
                  <RequiredInput $beforeSearch>
                    <SearchSection>
                      <SearchLabel>Search:</SearchLabel>
                      <Select
                        value={recipientType}
                        onChange={(e) => setRecipientType(e.target.value)}
                        style={{ width: "auto" }}
                      >
                        {recipientTypeOptions.map((option) => (
                          <option key={option} value={option}>
                            {option}
                          </option>
                        ))}
                      </Select>
                      <SearchLabel>for:</SearchLabel>
                      <SearchInput />
                      <Button>Find</Button>
                    </SearchSection>
                  </RequiredInput>
                </FormRow>
                <FormRow>
                  <Label>Recipients</Label>
                  <div>
                    <RecipientSection $hasError={!!errors.recipients}>
                      <div>
                        <ListBoxLabel>Available Recipients</ListBoxLabel>
                        <ListBox
                          multiple
                          value={selectedAvailable}
                          onChange={(e) => {
                            const selected = Array.from(
                              e.target.selectedOptions,
                              (option) => option.value
                            );
                            setSelectedAvailable(selected);
                          }}
                        >
                          {availableRecipients.length > 0 ? (
                            availableRecipients.map((recipient) => (
                              <option key={recipient} value={recipient}>
                                {recipient}
                              </option>
                            ))
                          ) : (
                            <option value="">--None--</option>
                          )}
                        </ListBox>
                      </div>

                      <TransferButtons>
                        <ButtonLabel>Add</ButtonLabel>
                        <TransferButton onClick={handleAdd}>
                          <ArrowIcon direction="right" />
                        </TransferButton>
                        <TransferButton onClick={handleRemove}>
                          <ArrowIcon direction="left" />
                        </TransferButton>
                        <ButtonLabel>Remove</ButtonLabel>
                      </TransferButtons>

                      <div>
                        <ListBoxLabel>Selected Recipients</ListBoxLabel>
                        <ListBox
                          multiple
                          value={selectedSelected}
                          onChange={(e) => {
                            const selected = Array.from(
                              e.target.selectedOptions,
                              (option) => option.value
                            );
                            setSelectedSelected(selected);
                          }}
                        >
                          {selectedRecipients.length > 0 ? (
                            selectedRecipients.map((recipient) => (
                              <option key={recipient} value={recipient}>
                                {recipient}
                              </option>
                            ))
                          ) : (
                            <option value="">--None--</option>
                          )}
                        </ListBox>
                      </div>
                    </RecipientSection>
                    {errors.recipients && (
                      <ErrorMessage>{errors.recipients}</ErrorMessage>
                    )}
                  </div>
                </FormRow>
                <FormRow>
                  <Label>Additional Emails</Label>
                  <div>
                    <div
                      style={{
                        marginBottom: "8px",
                        fontSize: "11.36px",
                        fontFamily: "Helvetica",
                      }}
                    >
                      You can enter up to five (5) email addresses to be
                      notified.
                    </div>
                    <TextArea
                      value={additionalEmails}
                      onChange={(e) => setAdditionalEmails(e.target.value)}
                    />
                  </div>
                </FormRow>
                <FormRow>
                  <Label>From Email Address</Label>
                  <RequiredInput>
                    <Select
                      value={fromEmail}
                      onChange={(e) => setFromEmail(e.target.value)}
                    >
                      <option value="Current User's email address">
                        Current User&apos;s email address
                      </option>
                    </Select>
                    {errors.fromEmail && (
                      <ErrorMessage>{errors.fromEmail}</ErrorMessage>
                    )}
                  </RequiredInput>
                </FormRow>
                <FormRow>
                  <Label></Label>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "flex-start",
                      width: "430px",
                      whiteSpace: "nowrap",
                      gap: "8px",
                    }}
                  >
                    <Checkbox
                      checked={isDefault}
                      onChange={(e) => setIsDefault(e.target.checked)}
                    />
                    <div
                      style={{
                        fontSize: "11.36px",
                        fontFamily: "Helvetica",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        flex: 1,
                      }}
                    >
                      <span>
                        Make this address the default From email address for
                        this object&apos;s email alerts.
                      </span>
                      <div
                        style={{
                          backgroundImage: `url("/assets/images/info_sprite.png")`,
                          backgroundPosition: "left top",
                          width: "16px",
                          height: "15px",
                          marginLeft: "4px",
                          flexShrink: 0,
                        }}
                      />
                    </div>
                  </div>
                </FormRow>
              </FormGrid>

              <ButtonGroup>
                <Button onClick={handleSave}>Save</Button>
                <Button onClick={handleSaveAndNew}>Save & New</Button>
                <Button onClick={handleCancel}>Cancel</Button>
              </ButtonGroup>
            </EditSection>
          </Container>
        )}
      </ContentArea>
    </div>
  );
}
