"use client";

import { useState } from "react";
import styled from "styled-components";

const Container = styled.div`
  padding: 12px;
  min-height: 100vh;
  background: #e8f3fc;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 75%;
  color: #222;
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
`;

const HeaderTitle = styled.h1`
  font-size: 21.6px;
  font-weight: 700;
  font-family: Arial, Helvetica, sans-serif;
  margin: 0;
  color: var(--lt-color-text-dark, #1d1e20);
`;

const SearchSection = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 14px;
  background: transparent;
  padding: 12px;
  border-radius: 0;
`;

const Select = styled.select`
  padding: 4px;
  font-size: 14px;
  border: 1px solid #000;
  border-radius: 0;
  background: var(--lt-color-white, #fff);
  min-width: 120px;
  font-family: Arial, Helvetica, sans-serif;

  &:first-child {
    width: 120px;
  }

  &:nth-child(2) {
    width: 300px;
  }
`;

const SearchInput = styled.input`
  padding: 4px 8px;
  font-size: 14px;
  border: 1px solid #000;
  border-radius: 0;
  width: 250px;
  color: var(--lt-color-text-default, #2f3237);
  background: var(--lt-color-white, #fff);
  font-family: Arial, Helvetica, sans-serif;
`;

const GoButton = styled.button`
  padding: 4px 16px;
  font-size: 14px;
  background: #f4f4f4;
  border: 1px solid #999;
  border-radius: 0;
  cursor: pointer;
  font-family: Arial, Helvetica, sans-serif;

  &:hover {
    background: var(--lt-color-background-dark, #dee3ed);
  }
`;

const HelpText = styled.div`
  font-size: 12px;
  color: var(--lt-color-text-default, #2f3237);
  margin: 8px 0 24px 0;
  font-family: Arial, Helvetica, sans-serif;
`;

const TableSection = styled.div`
  margin-top: 24px;
  position: relative;
  padding-top: 1px;

  &::before {
    content: "";
    position: absolute;
    top: -50px;
    left: -12px;
    right: -12px;
    bottom: -12px;
    background: linear-gradient(to bottom, #e8f3fc 0%, #ffffff 100px);
    z-index: -1;
  }
`;

const TableTitle = styled.h2`
  font-size: 14.5px;
  font-weight: 700;
  color: var(--lt-color-text-dark, #1d1e20);
  margin: 0 0 0 0;
  border-bottom: 2px solid #1a4f85;
  padding-bottom: 4px;
  font-family: Arial, Helvetica, sans-serif;
  position: relative;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 0;
  border: 1px solid #e0e3e5;
  background: #fff;
  position: relative;
`;

const Th = styled.th`
  text-align: left;
  padding: 8px;
  background: #f2f3f3;
  border: 1px solid #e0e3e5;
  font-size: 13px;
  font-weight: bold;
  font-family: Arial, Helvetica, sans-serif;
  color: #000;
`;

const Td = styled.td`
  padding: 8px;
  border: 1px solid #e0e3e5;
  font-size: 13px;
  vertical-align: top;
  font-family: Arial, Helvetica, sans-serif;
  background: #fff;
`;

const TableRow = styled.tr`
  &:nth-child(even) {
    background-color: #f8f8f8;

    ${Td} {
      background-color: #f8f8f8;
    }
  }

  &:hover {
    background-color: #f3f3f3;

    ${Td} {
      background-color: #f3f3f3;
    }
  }
`;

const TemplateLink = styled.a`
  color: black;
  text-decoration: underline;
  cursor: pointer;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 12px;
  font-weight: 400;

  &:hover {
    color: #014785;
  }
`;

// Classic email templates
const classicTemplates = [
  {
    name: "Appointment for Unauthenticated User using Appointment Types - For Amazon Chime",
    description: "Email template for confirmation of an appointment...",
    type: "Custom",
  },
  {
    name: "Appointment for Unauthenticated User using Appointment Types - For third party",
    description: "Email template for confirmation of an appointment...",
    type: "Custom",
  },
  {
    name: "Appointment for Unauthenticated User using Engagement Channels-For Amazon Chime",
    description: "Email template for confirmation of an appointment...",
    type: "Custom",
  },
  {
    name: "Appointment for Unauthenticated User using Engagement Channels-For third party",
    description: "Email template for confirmation of an appointment...",
    type: "Custom",
  },
  {
    name: "Canceled Service Appointment Confirmation Email",
    description: "Email Template to confirm canceling of a service ...",
    type: "Custom",
  },
  {
    name: "Commerce Reorder Portal: Invitation",
    description: "Invite a contact to a Commerce Reorder Portal.",
    type: "Custom",
  },
  {
    name: "Group Service Appointments Enrollment Confirmation Email",
    description: "Email Template to confirm enrollment of an attend...",
    type: "Custom",
  },
  {
    name: "Marketing: Product Inquiry Response",
    description: "Standard email response to website product inquir...",
    type: "Text",
  },
  {
    name: "welcome email template",
    description: "welcome email template",
    type: "Custom",
  },
  {
    name: "Sales: New Customer Email",
    description: "Sales: New Customer Email",
    type: "Custom",
  },
];

// Lightning email templates (as shown in the screenshot)
const lightningTemplates = [
  {
    name: "High-value Opportunity Notification",
    description: "",
    type: "Custom",
  },
  {
    name: "Opportunity Pending Approval Submitter",
    description: "Notification for the submitter",
    type: "Custom",
  },
];

export default function LookupPage() {
  const [view, setView] = useState("Classic");
  const [templateType, setTemplateType] = useState(
    "Unfiled Public Classic Email Templates"
  );
  const [searchTerm, setSearchTerm] = useState("");

  // Handle view change
  const handleViewChange = (e) => {
    const newView = e.target.value;
    setView(newView);
    if (newView === "Lightning") {
      setTemplateType("Public Email Templates");
    } else {
      setTemplateType("Unfiled Public Classic Email Templates");
    }
  };

  // Get templates based on selected view
  const getTemplatesForView = () => {
    if (view === "Lightning") {
      return lightningTemplates;
    }
    return classicTemplates;
  };

  // Filter templates based on search term and view
  const filteredTemplates = getTemplatesForView().filter((template) => {
    if (searchTerm) {
      return (
        template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    return true;
  });

  const handleTemplateSelect = (templateName) => {
    if (window.opener && !window.opener.closed) {
      window.opener.postMessage(
        {
          type: "TEMPLATE_SELECTED",
          templateName,
        },
        "*"
      );
      window.close();
    }
  };

  return (
    <Container>
      <Header>
        <div
          style={{
            backgroundImage: `url("/assets/images/icons_img.png")`,
            backgroundPosition: "0 -1202px",
            width: "32px",
            height: "32px",
            marginLeft: "4px",
          }}
        />
        <HeaderTitle>Lookup</HeaderTitle>
      </Header>

      <SearchSection>
        <Select value={view} onChange={handleViewChange}>
          <option value="Classic">Classic</option>
          <option value="Lightning">Lightning</option>
        </Select>
        <Select
          value={templateType}
          onChange={(e) => setTemplateType(e.target.value)}
        >
          <option value="Unfiled Public Classic Email Templates">
            Unfiled Public Classic Email Templates
          </option>
          <option value="Public Email Templates">Public Email Templates</option>
        </Select>
        <SearchInput
          type="text"
          placeholder="Search this folder..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <GoButton>Go!</GoButton>
      </SearchSection>

      <HelpText>
        You can use &quot;*&quot; as a wildcard next to other characters to
        improve your search results.
      </HelpText>

      <TableSection>
        <TableTitle>Recently Viewed Email Templates</TableTitle>
        <Table>
          <thead>
            <tr>
              <Th>Name</Th>
              <Th>Description</Th>
              <Th>Template Type</Th>
            </tr>
          </thead>
          <tbody>
            {filteredTemplates.map((template, index) => (
              <TableRow key={index}>
                <Td>
                  <TemplateLink
                    onClick={() => handleTemplateSelect(template.name)}
                  >
                    {template.name}
                  </TemplateLink>
                </Td>
                <Td>{template.description}</Td>
                <Td>{template.type}</Td>
              </TableRow>
            ))}
          </tbody>
        </Table>
      </TableSection>
    </Container>
  );
}
