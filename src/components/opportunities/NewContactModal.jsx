// src/components/opportunities/NewContactModal.jsx
import React, { useState, useRef, useEffect } from "react";

import { createPortal } from "react-dom";

export default function NewContactModal({ onClose }) {
  const [salOpen, setSalOpen] = useState(false);
  const [selectedSalutation, setSelectedSalutation] = useState(""); // "" === “--None--”
  const wrapperRef = useRef();

  // close the popup if you click anywhere else
  useEffect(() => {
    function onClickOutside(e) {
      if (wrapperRef.current && !wrapperRef.current.contains(e.target)) {
        setSalOpen(false);
      }
    }
    document.addEventListener("mousedown", onClickOutside);
    return () => document.removeEventListener("mousedown", onClickOutside);
  }, []);

  const salOptions = ["--None--", "Mr.", "Ms.", "Mrs.", "Dr.", "Prof.", "Mx."];

  return createPortal(
    <div
      onClick={onClose}
      style={{
        position: "fixed",
        inset: 0,
        backgroundColor: "rgba(0,0,0,0.4)",
        display: "flex",
        alignItems: "flex-start",
        paddingTop: "9.5vh",
        justifyContent: "center",
        zIndex: 9999,
      }}
    >
      <div
        onClick={(e) => e.stopPropagation()}
        style={{
          position: "relative",
          overflow: "visible", // outer shell never clips
          backgroundColor: "#fff",
          borderRadius: "4px",
          width: "840px",
          maxWidth: "90%",
          boxShadow: "0 2px 8px rgba(0,0,0,0.2)",
        }}
      >
        {/* ── top‐corner close icon */}
        <button
          onClick={onClose}
          title="Cancel and close"
          style={{
            position: "absolute",
            top: "-40px", // lifts it above the box
            right: "0px", // inset from right edge
            width: "32px",
            height: "32px",
            padding: 0,
            margin: 0,
            border: "1px solid transparent",
            borderRadius: "4px",
            backgroundColor: "#fff",
            cursor: "pointer",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <svg
            viewBox="0 0 520 520"
            aria-hidden="true"
            focusable="false"
            style={{
              width: "24px",
              height: "24px",
              fill: "rgb(53, 93, 150)",
              pointerEvents: "none",
            }}
          >
            <path d="M310 254l130-131c6-6 6-15 0-21l-20-21c-6-6-15-6-21 0L268 212a10 10 0 01-14 0L123 80c-6-6-15-6-21 0l-21 21c-6 6-6 15 0 21l131 131c4 4 4 10 0 14L80 399c-6 6-6 15 0 21l21 21c6 6 15 6 21 0l131-131a10 10 0 0114 0l131 131c6 6 15 6 21 0l21-21c6-6 6-15 0-21L310 268a10 10 0 010-14z" />
          </svg>
          <span
            style={{
              position: "absolute",
              width: 1,
              height: 1,
              overflow: "hidden",
              clip: "rect(0,0,0,0)",
            }}
          >
            Cancel and close
          </span>
        </button>
        <header
          style={{
            position: "relative",
            borderTopRightRadius: "4px",
            borderTopLeftRadius: "4px",
            backgroundColor: "#fff",
            borderBottom: "2px solid rgb(201,201,201)",
            padding: "16px",
            textAlign: "center",
          }}
        >
          <h2
            style={{
              margin: 0,
              fontSize: 20,
              lineHeight: "25px",
              fontWeight: 400,
            }}
          >
            New Contact
          </h2>
          <button
            onClick={onClose}
            aria-label="Close"
            style={{
              position: "absolute",
              top: 8,
              right: 8,
              background: "transparent",
              border: "none",
              fontSize: 20,
              cursor: "pointer",
              lineHeight: 1,
            }}
          ></button>
        </header>

        <div
          style={{
            padding: 16,
            backgroundColor: "#fff",
            color: "#181818",
            fontSize: 13,
            lineHeight: "19.5px",
            fontFamily:
              '-apple-system, system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
          }}
        >
          <form>
            <fieldset style={{ marginBottom: 8 }}>
              {/* Name group */}
              <legend style={{ fontSize: 13, color: "#444", marginBottom: 4 }}>
                <span style={{ color: "#ba0517", paddingRight: 2 }}>*</span>Name
              </legend>

              {/* Salutation (custom dropdown) */}
              <div style={{ marginBottom: 12 }} ref={wrapperRef}>
                <label
                  htmlFor="salutation"
                  style={{ display: "block", marginBottom: 4, color: "#444" }}
                >
                  Salutation
                </label>

                {/* the “fake select” trigger */}
                <div
                  onClick={() => setSalOpen((o) => !o)}
                  style={{
                    position: "relative",
                    width: "100%",
                    minHeight: 36,
                    lineHeight: "30px",
                    padding: "0 12px",
                    border: "1px solid #747474",
                    borderRadius: 4,
                    backgroundColor: "#fff",
                    cursor: "pointer",
                    userSelect: "none",
                  }}
                >
                  {/* show currently chosen */}
                  {selectedSalutation || "--None--"}
                  {/* bigger arrow */}
                  <span
                    style={{
                      position: "absolute",
                      right: 12,
                      top: "50%",
                      transform: "translateY(-50%)",
                      fontSize: "16px",
                      pointerEvents: "none",
                    }}
                  >
                    ▾
                  </span>

                  {salOpen && (
                    <div
                      role="listbox"
                      style={{
                        position: "absolute",
                        top: "100%",
                        left: 0,
                        width: "100%",
                        zIndex: 9999,
                        backgroundColor: "#fff",
                        border: "1px solid rgb(229,229,229)",
                        borderRadius: 4,
                        boxShadow: "0 2px 3px rgba(0,0,0,0.16)",
                        marginTop: 4,
                        maxHeight: "15rem",
                        overflowY: "auto",
                      }}
                    >
                      <ul style={{ listStyle: "none", margin: 0, padding: 0 }}>
                        {salOptions.map((opt) => (
                          <li key={opt}>
                            <a
                              href="#!"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setSelectedSalutation(opt);
                                setSalOpen(false);
                              }}
                              role="option"
                              title={opt}
                              style={{
                                display: "flex",
                                alignItems: "center",
                                padding: "0.5rem 0.75rem",
                                textDecoration: "none",
                                color: "#181818",
                                cursor: "pointer",
                              }}
                            >
                              {/* tick for the selected one */}
                              <span
                                style={{
                                  display: "inline-block",
                                  width: 16,
                                  height: 16,
                                  marginRight: 8,
                                  lineHeight: 1,
                                  textAlign: "center",
                                }}
                              >
                                {opt === (selectedSalutation || "--None--")
                                  ? "✓"
                                  : ""}
                              </span>
                              {opt}
                            </a>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>

              {/* First Name */}
              <label
                htmlFor="firstName"
                style={{ display: "block", marginTop: 8, marginBottom: 4 }}
              >
                First Name
              </label>
              <input
                id="firstName"
                type="text"
                placeholder="First Name"
                style={{
                  width: "100%",
                  minHeight: 36,
                  padding: "0 12px",
                  border: "1px solid #747474",
                  borderRadius: 4,
                  marginBottom: 12,
                }}
              />

              {/* Last Name */}
              <label
                htmlFor="lastName"
                style={{ display: "block", marginTop: 8, marginBottom: 4 }}
              >
                <span style={{ color: "#ba0517", paddingRight: 2 }}>*</span>Last
                Name
              </label>
              <input
                id="lastName"
                type="text"
                placeholder="Last Name"
                required
                style={{
                  width: "100%",
                  minHeight: 36,
                  padding: "0 12px",
                  border: "1px solid #747474",
                  borderRadius: 4,
                  marginBottom: 12,
                }}
              />

              {/* Email */}
              <label
                htmlFor="email"
                style={{ display: "block", marginTop: 8, marginBottom: 4 }}
              >
                Email
              </label>
              <input
                id="email"
                type="email"
                placeholder="Email"
                style={{
                  width: "100%",
                  minHeight: 36,
                  padding: "0 12px",
                  border: "1px solid #747474",
                  borderRadius: 4,
                  marginBottom: 12,
                }}
              />

              {/* Phone */}
              <label
                htmlFor="phone"
                style={{ display: "block", marginTop: 8, marginBottom: 4 }}
              >
                Phone
              </label>
              <input
                id="phone"
                type="tel"
                placeholder="Phone"
                style={{
                  width: "100%",
                  minHeight: 36,
                  padding: "0 12px",
                  border: "1px solid #747474",
                  borderRadius: 4,
                  marginBottom: 12,
                }}
              />

              <label
                htmlFor="accountName"
                style={{ display: "block", marginTop: 8, marginBottom: 4 }}
              >
                Account Name
              </label>
              {/* “Lookup bar” container */}
              <div
                style={{
                  display: "inline-flex",
                  alignItems: "center",
                  border: "1px solid #747474",
                  borderRadius: 4,
                  padding: "4px",
                  marginBottom: 12,
                  minHeight: 36,
                  boxSizing: "border-box",
                  width: "810px",
                }}
              >
                {/* inline‐list of pills (here just one) */}
                <ul
                  style={{
                    display: "flex",
                    margin: 0,
                    padding: 0,
                    listStyle: "none",
                    flex: "1 1 auto", // ← UL fills the container
                    minWidth: 0,
                    boxSizing: "border-box",
                  }}
                >
                  <li
                    style={{
                      listStyleType: "none",
                      margin: 0,
                      flex: "1 1 auto", // ← LI fills the UL
                      minWidth: 0,
                      boxSizing: "border-box",
                    }}
                  >
                    <span
                      tabIndex="0"
                      style={{
                        display: "flex",
                        alignItems: "center",
                        backgroundColor: "transparent",
                        border: "1px solid #CCCCCC",
                        borderRadius: 5,
                        padding: 4,
                        whiteSpace: "nowrap",
                        boxSizing: "border-box",
                        width: "100%",
                      }}
                    >
                      {/* pill icon */}
                      <span
                        style={{
                          display: "inline-block",
                          width: 20,
                          height: 20,
                          marginRight: 8,
                          borderRadius: 2,
                          backgroundColor: "rgb(88,103,232)",
                          boxSizing: "border-box",
                          overflow: "hidden",
                        }}
                      >
                        <img
                          src="/assets/images/account_120.png"
                          alt=""
                          style={{
                            width: "100%",
                            height: "100%",
                            verticalAlign: "middle",
                            borderRadius: 2,
                            boxSizing: "border-box",
                          }}
                        />
                      </span>
                      {/* pill text */}
                      <span
                        style={{
                          flex: 1, // fill remaining space
                          minWidth: 0, // allow shrinking
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          boxSizing: "border-box",
                        }}
                      >
                        Burlington Textiles Corp of America
                      </span>
                      {/* remove button */}
                      <button
                        onClick={() => {
                          /* clear selection */
                        }}
                        aria-label="Remove selection"
                        style={{
                          background: "none",
                          border: "none",
                          marginLeft: 8,
                          color: "#888888",
                          cursor: "pointer",
                          fontSize: "16px",
                          lineHeight: 1,
                          padding: 0,
                          boxSizing: "border-box",
                        }}
                      >
                        ×
                      </button>
                    </span>
                  </li>
                </ul>
              </div>

              {/* Title */}
              <label
                htmlFor="title"
                style={{ display: "block", marginTop: 8, marginBottom: 4 }}
              >
                Title
              </label>
              <input
                id="title"
                type="text"
                placeholder="Title"
                style={{
                  width: "100%",
                  minHeight: 36,
                  padding: "0 12px",
                  border: "1px solid #747474",
                  borderRadius: 4,
                  marginBottom: 12,
                }}
              />
            </fieldset>
          </form>

          {/* ↓ add this: */}
          <footer
            style={{
              display: "block",
              boxSizing: "border-box",
              borderBottomRightRadius: "4px",
              borderBottomLeftRadius: "4px",
              backgroundColor: "rgb(243, 243, 243)",
              color: "rgb(24, 24, 24)",
              textAlign: "right",
              borderTop: "2px solid rgb(201, 201, 201)", // the top border that “touches” the form container
              padding: "12px 16px",
              width: "calc(100% + 32px)", // add 32px to cover the parent’s 16px padding on both sides
              marginLeft: "-16px", // pull it back into the left edge
              marginRight: "-16px",
              marginBottom: "-16px",
              boxShadow: "rgba(0, 0, 0, 0.16) 0px 2px 3px 0px",
              fontSize: "13px",
              lineHeight: "19.5px",
              fontFamily:
                '-apple-system, system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
            }}
          >
            <button
              type="button"
              onClick={onClose}
              style={{
                display: "inline-flex",
                alignItems: "center",
                justifyContent: "center",
                color: "rgb(1, 118, 211)",
                backgroundColor: "#fff",
                border: "1px solid rgb(116, 116, 116)",
                borderRadius: "4px",
                padding: "0 16px",
                fontSize: "13px",
                lineHeight: "30px",
                cursor: "pointer",
              }}
            >
              Cancel
            </button>
            <button
              type="button"
              style={{
                display: "inline-flex",
                alignItems: "center",
                justifyContent: "center",
                color: "#fff",
                backgroundColor: "rgb(1, 118, 211)",
                border: "1px solid rgb(1, 118, 211)",
                borderRadius: "4px",
                padding: "0 16px",
                fontSize: "13px",
                lineHeight: "30px",
                cursor: "pointer",
                marginLeft: "8px",
              }}
              onClick={() => {
                /* your save handler */
              }}
            >
              Save
            </button>
          </footer>
        </div>
      </div>
    </div>,
    document.body
  );
}
