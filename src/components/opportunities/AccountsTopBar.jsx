import React, { useState } from "react";
import NewContactModal from "./NewContactModal";

export default function AccountsTopBar() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  return (
    <>
      <div
        className="highlights slds-clearfix slds-page-header slds-page-header_record-home fixed-position"
        style={{
          boxSizing: "border-box",
          padding:
            "var(--lwc-varSpacingVerticalMedium,1rem) var(--lwc-varSpacingHorizontalMedium,1rem)",
          border:
            "var(--lwc-borderWidthThin,1px) solid var(--slds-g-color-border-base-1, var(--lwc-pageHeaderColorBorder,rgb(201, 201, 201)))",
          backgroundImage: "",
          backgroundPositionX: "",
          backgroundPositionY: "",
          backgroundSize: "",
          backgroundRepeat: "",
          backgroundAttachment: "",
          backgroundOrigin: "",
          backgroundColor: "",
          backgroundClip: "padding-box",
          boxShadow:
            "var(--lwc-pageHeaderShadow,0 2px 2px 0 rgba(0, 0, 0, 0.10))",
          position: "fixed",
          zIndex: 98,
          borderRadius: "4px",
          height: "139.5px",
          left: "11.1111px",
          right: "15px",
          paddingLeft: "16.8889px",
          paddingRight: "16.8889px",
          transform: "translate3d(0px, -4px, 0px)",
        }}
      >
        <div
          className="slds-grid primaryFieldRow"
          style={{ boxSizing: "border-box", display: "flex" }}
        >
          <div
            className="slds-grid slds-col slds-has-flexi-truncate"
            style={{
              boxSizing: "border-box",
              display: "flex",
              flex: "1 1 0%",
              minWidth: "0px",
            }}
          >
            <div
              className="slds-media slds-no-space"
              style={{
                boxSizing: "border-box",
                minWidth: "0px",
                display: "flex",
                alignItems: "flex-start",
              }}
            >
              <slot name="icon" style={{ boxSizing: "border-box" }}>
                <force-social-record-avatar-wrapper
                  style={{ boxSizing: "border-box" }}
                >
                  <div
                    className="slds-media__figure"
                    style={{
                      boxSizing: "border-box",
                      flexShrink: 0,
                      marginRight: "var(--lwc-spacingSmall,0.75rem)",
                    }}
                  >
                    <div
                      className="slds-clearfix slds-grid"
                      style={{ boxSizing: "border-box", display: "flex" }}
                    >
                      <span
                        className="slds-col"
                        style={{ boxSizing: "border-box", flex: "1 1 auto" }}
                      >
                        <force-record-avatar
                          style={{ boxSizing: "border-box" }}
                        >
                          <span
                            className="record-avatar-container slds-avatar slds-avatar_medium icon"
                            style={{
                              boxSizing: "border-box",
                              display: "inline-block",
                              verticalAlign: "middle",
                              lineHeight: "var(--lwc-lineHeightReset,1)",
                              color:
                                "var(--slds-c-avatar-text-color, var(--sds-c-avatar-text-color, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))))",
                              width: "var(--lwc-squareIconMediumBoundary,2rem)",
                              height:
                                "var(--lwc-squareIconMediumBoundary,2rem)",
                              fontSize:
                                "var(--lwc-fontSizeHeadingSmall,0.875rem)",
                              overflow: "hidden",
                              borderRadius:
                                "var(--slds-s-icon-radius-border, 0.25rem)",
                              backgroundColor: "rgb(88, 103, 232)",
                            }}
                          >
                            <img
                              src="/assets/images/account_120.png"
                              style={{
                                boxSizing: "border-box",
                                border: "0px",
                                verticalAlign: "middle",
                                maxWidth: "100%",
                                height: "auto",
                              }}
                            />
                          </span>
                        </force-record-avatar>
                      </span>
                    </div>
                  </div>
                </force-social-record-avatar-wrapper>
              </slot>
            </div>
            <div
              className="slds-media__body"
              style={{
                boxSizing: "border-box",
                flex: "1 1 0%",
                minWidth: "0px",
                marginBottom: "0px",
              }}
            >
              <h1
                style={{
                  boxSizing: "border-box",
                  margin: "0px",
                  padding: "0px",
                  fontWeight: "inherit",
                  fontSize: "1em",
                  marginBottom: "0px",
                }}
              >
                <div
                  className="entityNameTitle slds-line-height--reset"
                  style={{
                    boxSizing: "border-box",
                    lineHeight: "var(--lwc-lineHeightReset,1)",
                    fontSize: "0.8125rem",
                    color:
                      "var(--slds-g-color-neutral-base-30, rgb(68, 68, 68))",
                  }}
                >
                  <slot name="entityLabel" style={{ boxSizing: "border-box" }}>
                    <records-entity-label style={{ boxSizing: "border-box" }}>
                      Account
                    </records-entity-label>
                  </slot>
                </div>
                <slot
                  className="slds-page-header__title slds-m-right--small slds-align-middle slds-line-clamp clip-text"
                  name="primaryField"
                  style={{
                    boxSizing: "border-box",
                    fontSize: "var(--lwc-pageHeaderTitleFontSize,1.125rem)",
                    fontWeight: "var(--lwc-pageHeaderTitleFontWeight,700)",
                    lineHeight: "var(--lwc-lineHeightHeading,1.25)",
                    verticalAlign: "middle",
                    alignSelf: "center",
                    marginRight: "var(--lwc-spacingSmall,0.75rem)",
                    whiteSpace: "pre-line",
                    display: "-webkit-box",
                    WebkitBoxOrient: "vertical",
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                    hyphens: "auto",
                    overflowWrap: "break-word",
                    wordBreak: "break-word",
                    position: "relative",
                    maxHeight: "2.8125rem",
                    WebkitLineClamp: "2",
                  }}
                >
                  <lightning-formatted-text style={{ boxSizing: "border-box" }}>
                    Burlington Textiles Corp of America
                  </lightning-formatted-text>
                </slot>
              </h1>
            </div>
            <div
              className="header-right-content"
              style={{
                boxSizing: "border-box",
                display: "flex",
                alignItems: "center",
              }}
            >
              <slot
                name="headerRightContent"
                style={{ boxSizing: "border-box" }}
              >
                <sfa-output-name-with-hierarchy-icon-account
                  style={{
                    boxSizing: "border-box",
                    margin: "0px 4px",
                    display: "block",
                  }}
                >
                  <sfa-output-name-with-hierarchy-icon-wrapper
                    style={{ boxSizing: "border-box" }}
                  >
                    <force-aura-action-wrapper
                      style={{ boxSizing: "border-box" }}
                    >
                      <div style={{ boxSizing: "border-box" }}>
                        <div
                          className="testonly-outputNameWithHierarchyIcon slds-grid sfaOutputNameWithHierarchyIcon"
                          style={{
                            boxSizing: "border-box",
                            display: "flex",
                            overflow: "inherit",
                            textOverflow: "inherit",
                          }}
                        >
                          <span
                            className="slds-m-left_x-small slds-text-body_regular sfaNavigateToHierarchyButton"
                            style={{
                              boxSizing: "border-box",
                              marginLeft: "var(--lwc-spacingXSmall,0.5rem)",
                              fontSize: "var(--lwc-fontSize3,0.8125rem)",
                              display: "inline-block",
                            }}
                          >
                            <button
                              className="slds-button slds-button_icon testonly-navigateToHierarchyButton slds-button_icon-border-filled"
                              type="button"
                              title="View Account Hierarchy"
                              style={{
                                boxSizing: "border-box",
                                font: "inherit",
                                margin: "0px",
                                overflow: "visible",
                                textTransform: "none",
                                cursor: "pointer",
                                backgroundPosition: "initial",
                                borderStyle: "solid",
                                borderWidth:
                                  "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                borderRadius:
                                  "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                textDecoration: "none",
                                whiteSpace: "normal",
                                position: "relative",
                                display: "inline-flex",
                                alignItems: "center",
                                paddingTop:
                                  "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                paddingRight:
                                  "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                paddingBottom:
                                  "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                paddingLeft:
                                  "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                backgroundImage: "none",
                                backgroundSize: "initial",
                                backgroundRepeat: "initial",
                                backgroundAttachment: "initial",
                                backgroundOrigin: "initial",
                                backgroundClip: "border-box",
                                boxShadow:
                                  "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                appearance: "none",
                                userSelect: "none",
                                justifyContent: "center",
                                flexShrink: 0,
                                width:
                                  "var(--lwc-squareIconMediumBoundary,2rem)",
                                height:
                                  "var(--lwc-squareIconMediumBoundary,2rem)",
                                transition: "border 0.15s linear",
                                border: "1px solid rgb(116,116,116)",
                                lineHeight: "var(--lwc-lineHeightReset,1)",
                                verticalAlign: "middle",
                                color:
                                  "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                borderTopStyle: "",
                                borderTopWidth: "",
                                borderRightStyle: "",
                                borderRightWidth: "",
                                borderBottomStyle: "",
                                borderBottomWidth: "",
                                borderLeftStyle: "",
                                borderLeftWidth: "",
                                borderImageSource: "",
                                borderImageSlice: "",
                                borderImageWidth: "",
                                borderImageOutset: "",
                                borderImageRepeat: "",
                                backgroundColor:
                                  "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                              }}
                            >
                              <lightning-primitive-icon
                                style={{ boxSizing: "border-box" }}
                              >
                                <svg
                                  className="slds-button__icon"
                                  aria-hidden="true"
                                  focusable="false"
                                  viewBox="0 0 520 520"
                                  style={{
                                    boxSizing: "border-box",
                                    verticalAlign: "middle",
                                    width:
                                      "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                    height:
                                      "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                    fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                    overflow: "hidden",
                                  }}
                                >
                                  <g style={{ boxSizing: "border-box" }}>
                                    <path
                                      d="M231 230H108c-7 0-14 6-14 13v105H53c-7 0-14 7-14 14v100c0 7 7 14 14 14h137c7 0 14-7 14-14V362c0-7-7-14-14-14h-41v-64h219v64h-41c-7 0-14 7-14 14v100c0 7 7 14 14 14h137c7 0 13-7 13-14V362c0-7-6-14-13-14h-42V243c0-7-7-13-14-13H286v-64h41c7 0 13-7 13-14V52c0-7-6-14-13-14H190c-7 0-14 7-14 14v100c0 7 7 14 14 14h42v64z"
                                      style={{ boxSizing: "border-box" }}
                                    />
                                  </g>
                                </svg>
                              </lightning-primitive-icon>
                              <span
                                className="slds-assistive-text"
                                style={{
                                  boxSizing: "border-box",
                                  margin: "-1px",
                                  border: "0px",
                                  padding: "0px",
                                  overflow: "hidden",
                                  whiteSpace: "nowrap",
                                  position: "absolute",
                                  width: "1px",
                                  height: "1px",
                                  clip: "rect(0px, 0px, 0px, 0px)",
                                  textTransform: "none",
                                }}
                              >
                                View Account Hierarchy
                              </span>
                            </button>
                          </span>
                        </div>
                      </div>
                    </force-aura-action-wrapper>
                  </sfa-output-name-with-hierarchy-icon-wrapper>
                </sfa-output-name-with-hierarchy-icon-account>
              </slot>
            </div>
          </div>
          <div
            className="slds-col slds-no-flex slds-grid slds-grid_vertical-align-center horizontal slds-m-right--xx-small chatterActionContainer"
            style={{
              display: "flex",
              alignItems: "center",
              boxSizing: "border-box",
            }}
          >
            {/* 1) Follow button styled per original UI */}
            <div
              className="slds-button-group"
              style={{ marginRight: "0.75rem" }}
            >
              <button
                type="button"
                aria-live="off"
                style={{
                  display: "inline-flex",
                  cursor: "pointer",
                  color: "rgb(53, 93, 150)",
                  fontSize: "13px",
                  lineHeight: "30px",
                  color: "rgb(6,106,254)",
                  fontFamily:
                    '-apple-system, system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                  backgroundColor: "#FFFFFF",
                  boxShadow: "none",
                  border: "1px solid rgb(116, 116, 116)",
                  borderRadius: "4px",
                  padding: "0px 16px",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {/* left icon */}
                <svg
                  viewBox="0 0 520 520"
                  aria-hidden="true"
                  style={{
                    fill: "rgb(6,106,254)",
                    width: "14px",
                    height: "14px",
                    marginRight: "8px",
                  }}
                >
                  <path d="M300 290h165c8 0 15-7 15-15v-30c0-8-7-15-15-15H300c-6 0-10-4-10-10V55c0-8-7-15-15-15h-30c-8 0-15 7-15 15v165c0 6-4 10-10 10H55c-8 0-15 7-15 15v30c0 8 7 15 15 15h165c6 0 10 4 10 10v165c0 8 7 15 15 15h30c8 0 15-7 15-15V300c0-6 4-10 10-10z" />
                </svg>
                <span>Follow</span>
              </button>
            </div>

            {/* 2) Attached group: New Contact / New Case / New Note / … */}
            {/* 2) Attached group: New Contact / New Case / New Note / … */}
            <div className="slds-button-group" role="group">
              {/* New Contact (leftmost) */}
              <button
                type="button"
                onClick={() => setIsModalOpen(true)}
                style={{
                  display: "inline-flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "32px",
                  padding: "0 12px",
                  fontSize: "13px",
                  lineHeight: "30px",
                  fontFamily:
                    '-apple-system, system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                  color: "rgb(6,106,254)",
                  backgroundColor: "#FFFFFF",
                  borderTop: "1px solid rgb(116,116,116)",
                  borderLeft: "1px solid rgb(116,116,116)",
                  borderBottom: "1px solid rgb(116,116,116)",
                  borderRight: "none", // ← turn this one off
                  borderRadius: "4px 0 0 4px",
                  cursor: "pointer",
                }}
              >
                New Contact
              </button>

              {/* New Case */}
              <button
                type="button"
                style={{
                  display: "inline-flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "32px",
                  padding: "0 12px",
                  fontSize: "13px",
                  lineHeight: "30px",
                  fontFamily:
                    '-apple-system, system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                  color: "rgb(6,106,254)",
                  backgroundColor: "#FFFFFF",
                  borderTop: "1px solid rgb(116,116,116)",
                  borderLeft: "1px solid rgb(116,116,116)", // ← your single separator line
                  borderBottom: "1px solid rgb(116,116,116)",
                  borderRight: "none",
                  cursor: "pointer",
                }}
              >
                New Case
              </button>

              {/* New Note */}
              <button
                type="button"
                style={{
                  display: "inline-flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "32px",
                  padding: "0 12px",
                  fontSize: "13px",
                  lineHeight: "30px",
                  fontFamily:
                    '-apple-system, system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                  color: "rgb(6,106,254)",
                  backgroundColor: "#FFFFFF",
                  borderTop: "1px solid rgb(116,116,116)",
                  borderLeft: "1px solid rgb(116,116,116)",
                  borderBottom: "1px solid rgb(116,116,116)",
                  borderRight: "none",
                  cursor: "pointer",
                }}
              >
                New Note
              </button>

              {/* Dropdown (rightmost) */}
              <button
                type="button"
                aria-haspopup="true"
                aria-expanded="false"
                style={{
                  display: "inline-flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "32px",
                  width: "32px",
                  backgroundColor: "#FFFFFF",
                  borderTop: "1px solid rgb(116,116,116)",
                  borderLeft: "1px solid rgb(116,116,116)",
                  borderBottom: "1px solid rgb(116,116,116)",
                  borderRight: "1px solid rgb(116,116,116)",
                  borderRadius: "0 4px 4px 0",
                  cursor: "pointer",
                }}
              >
                <svg
                  viewBox="0 0 520 520"
                  aria-hidden="true"
                  className="slds-button__icon"
                  style={{
                    fill: "currentColor",
                    width: "0.875rem",
                    height: "0.875rem",
                  }}
                >
                  <path d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z" />
                </svg>
                <span className="slds-assistive-text">Show more actions</span>
              </button>
            </div>
            {isModalOpen && (
              <NewContactModal onClose={() => setIsModalOpen(false)} />
            )}
          </div>
        </div>
        <div
          className="secondaryFields"
          role="presentation"
          style={{
            boxSizing: "border-box",
            visibility: "visible",
            transform: "translate3d(0px, 0px, 0px)",
            opacity: 1,
          }}
        >
          <slot
            className="slds-grid slds-page-header__detail-row"
            name="secondaryFields"
            style={{
              boxSizing: "border-box",
              margin:
                "var(--lwc-varSpacingVerticalSmall,0.75rem) calc(-1 * var(--lwc-varSpacingHorizontalMedium,1rem)) calc(-1 * var(--lwc-varSpacingVerticalMedium,1rem))",
              borderRadius:
                "0 0 var(--lwc-pageHeaderBorderRadius,0.25rem) var(--lwc-pageHeaderBorderRadius,0.25rem)",
              backgroundColor:
                "var(--slds-g-color-neutral-base-100, var(--lwc-pageHeaderColorBackgroundAlt,rgb(255, 255, 255)))",
              position: "relative",
              zIndex: 2,
              display: "flex",
              padding: "1rem 1rem",
              fill: "var(--slds-g-color-neutral-base-80, rgb(243, 243, 243))",
              marginTop: "1rem",
              marginRight: "calc(-1 * 1rem)",
              marginLeft: "calc(-1 * 1rem)",
              marginBottom: "calc(-1 * 1rem)",
            }}
          >
            <records-highlights-details-item
              className="slds-page-header__detail-block"
              role="listitem"
              style={{
                boxSizing: "border-box",
                paddingRight: "var(--lwc-spacingXLarge,2rem)",
                overflow: "hidden",
                paddingLeft: "0px",
                maxWidth: "60px",
              }}
            >
              <div style={{ boxSizing: "border-box" }}>
                <p
                  className="slds-text-title slds-truncate"
                  title="Type"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    maxWidth: "100%",
                    textOverflow: "ellipsis",
                    fontSize: "var(--lwc-fontSize2,0.75rem)",
                    lineHeight: "var(--lwc-lineHeightHeading,1.25)",
                    color:
                      "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                  }}
                >
                  Type
                </p>
                <p
                  className="fieldComponent slds-text-body--regular slds-show_inline-block slds-truncate"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    textOverflow: "ellipsis",
                    display: "inline-block",
                    fontSize: "0.875rem",
                    verticalAlign: "middle",
                    maxWidth: "100%",
                  }}
                >
                  <slot style={{ boxSizing: "border-box" }}>
                    <lightning-formatted-text
                      style={{ boxSizing: "border-box" }}
                    />
                  </slot>
                </p>
              </div>
            </records-highlights-details-item>
            <records-highlights-details-item
              className="slds-page-header__detail-block"
              role="listitem"
              style={{
                boxSizing: "border-box",
                paddingRight: "var(--lwc-spacingXLarge,2rem)",
                paddingLeft: "var(--lwc-spacingXLarge,2rem)",
                overflow: "hidden",
                maxWidth: "100px",
              }}
            >
              <div style={{ boxSizing: "border-box" }}>
                <p
                  className="slds-text-title slds-truncate"
                  title="Phone"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    maxWidth: "100%",
                    textOverflow: "ellipsis",
                    fontSize: "var(--lwc-fontSize2,0.75rem)",
                    lineHeight: "var(--lwc-lineHeightHeading,1.25)",
                    color:
                      "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                  }}
                >
                  Phone
                </p>
                <p
                  className="fieldComponent slds-text-body--regular slds-show_inline-block slds-truncate"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    textOverflow: "ellipsis",
                    display: "inline-block",
                    fontSize: "0.875rem",
                    verticalAlign: "middle",
                    maxWidth: "100%",
                  }}
                >
                  <slot style={{ boxSizing: "border-box" }}>
                    <records-output-phone style={{ boxSizing: "border-box" }}>
                      <lightning-formatted-phone
                        style={{ boxSizing: "border-box" }}
                      />
                    </records-output-phone>
                  </slot>
                </p>
              </div>
            </records-highlights-details-item>
            <records-highlights-details-item
              className="slds-page-header__detail-block"
              role="listitem"
              style={{
                boxSizing: "border-box",
                paddingRight: "var(--lwc-spacingXLarge,2rem)",
                paddingLeft: "var(--lwc-spacingXLarge,2rem)",
                overflow: "hidden",
                maxWidth: "110px",
              }}
            >
              <div style={{ boxSizing: "border-box" }}>
                <p
                  className="slds-text-title slds-truncate"
                  title="Website"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    maxWidth: "100%",
                    textOverflow: "ellipsis",
                    fontSize: "var(--lwc-fontSize2,0.75rem)",
                    lineHeight: "var(--lwc-lineHeightHeading,1.25)",
                    color:
                      "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                  }}
                >
                  Website
                </p>
                <p
                  className="fieldComponent slds-text-body--regular slds-show_inline-block slds-truncate"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    textOverflow: "ellipsis",
                    display: "inline-block",
                    fontSize: "0.875rem",
                    verticalAlign: "middle",
                    maxWidth: "100%",
                  }}
                >
                  <slot style={{ boxSizing: "border-box" }}>
                    <lightning-formatted-url
                      style={{ boxSizing: "border-box" }}
                    />
                  </slot>
                </p>
              </div>
            </records-highlights-details-item>
            <records-highlights-details-item
              className="slds-page-header__detail-block"
              role="listitem"
              style={{
                boxSizing: "border-box",
                paddingRight: "var(--lwc-spacingXLarge,2rem)",
                paddingLeft: "var(--lwc-spacingXLarge,2rem)",
                overflow: "hidden",
                maxWidth: "208px",
              }}
            >
              <div style={{ boxSizing: "border-box" }}>
                <p
                  className="slds-text-title slds-truncate"
                  title="Account Owner"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    maxWidth: "100%",
                    textOverflow: "ellipsis",
                    fontSize: "var(--lwc-fontSize2,0.75rem)",
                    lineHeight: "var(--lwc-lineHeightHeading,1.25)",
                    color:
                      "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                  }}
                >
                  Account Owner
                </p>
                <p
                  className="fieldComponent slds-text-body--regular slds-show_inline-block slds-truncate"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    textOverflow: "ellipsis",
                    display: "inline-block",
                    fontSize: "0.875rem",
                    verticalAlign: "middle",
                    maxWidth: "100%",
                  }}
                >
                  <slot style={{ boxSizing: "border-box" }}>
                    <force-owner-lookup
                      style={{ boxSizing: "border-box", display: "block" }}
                    >
                      <div
                        className="slds-grid"
                        style={{ boxSizing: "border-box", display: "flex" }}
                      >
                        <span
                          className="owner-name slds-truncate slds-grow"
                          style={{
                            boxSizing: "border-box",
                            flexGrow: 1,
                            overflow: "hidden",
                            whiteSpace: "nowrap",
                            maxWidth: "100%",
                            textOverflow: "ellipsis",
                            wordBreak: "break-all",
                            textAlign: "start",
                          }}
                        >
                          <force-lookup
                            style={{
                              boxSizing: "border-box",
                              display: "inline-block",
                              maxWidth: "100%",
                              verticalAlign: "middle",
                            }}
                          >
                            <div
                              className="slds-grid"
                              style={{
                                boxSizing: "border-box",
                                display: "flex",
                              }}
                            >
                              <force-record-avatar
                                className="slds-shrink-none"
                                style={{
                                  boxSizing: "border-box",
                                  flexShrink: 0,
                                }}
                              >
                                <img
                                  src="/assets/images/blue_profile_avatar_96.png"
                                  alt="Owner avatar"
                                  style={{
                                    display: "inline-block",
                                    verticalAlign: "middle",
                                    width: "1.25rem",
                                    height: "1.25rem",
                                    borderRadius: "0.25rem",
                                    marginRight: "0.25rem",
                                    objectFit: "cover",
                                  }}
                                />
                              </force-record-avatar>
                              <records-hoverable-link
                                className="slds-grow has-avatar"
                                tabIndex="-1"
                                style={{
                                  boxSizing: "border-box",
                                  flexGrow: 1,
                                  display: "block",
                                  width: "100%",
                                  overflow: "hidden",
                                  padding:
                                    "var(--slds-g-sizing-border-2, var(--lwc-borderWidthThick, 2px))",
                                }}
                              >
                                <div
                                  className="slds-grid"
                                  style={{
                                    boxSizing: "border-box",
                                    display: "flex",
                                  }}
                                >
                                  <a
                                    className="slds-truncate"
                                    style={{
                                      boxSizing: "border-box",
                                      backgroundColor: "transparent",
                                      textDecoration: "none",
                                      transition: "color 0.1s linear",
                                      color:
                                        "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                                      cursor: "pointer",
                                      overflow: "hidden",
                                      whiteSpace: "nowrap",
                                      maxWidth: "100%",
                                      textOverflow: "ellipsis",
                                    }}
                                  >
                                    <span
                                      style={{
                                        boxSizing: "border-box",
                                        textDecorationLine: "underline",
                                        textDecorationThickness:
                                          "var(--slds-g-sizing-border-1, var(--lwc-borderWidthThin, 1px))",
                                        textDecorationStyle: "dotted",
                                        textUnderlineOffset: "2px",
                                      }}
                                    >
                                      <slot style={{ boxSizing: "border-box" }}>
                                        <span
                                          className="slds-truncate"
                                          style={{
                                            boxSizing: "border-box",
                                            overflow: "hidden",
                                            whiteSpace: "nowrap",
                                            maxWidth: "100%",
                                            textOverflow: "ellipsis",
                                          }}
                                        >
                                          <slot
                                            style={{ boxSizing: "border-box" }}
                                          >
                                            <span
                                              style={{
                                                boxSizing: "border-box",
                                              }}
                                            >
                                              abhishek tomar
                                            </span>
                                          </slot>
                                        </span>
                                      </slot>
                                    </span>
                                  </a>
                                  <lightning-button-icon
                                    className="slds-button_icon hover-button-icon-element hide-hover-icon"
                                    style={{
                                      boxSizing: "border-box",
                                      lineHeight:
                                        "var(--lwc-lineHeightReset,1)",
                                      verticalAlign: "middle",
                                      justifyContent: "center",
                                      color:
                                        "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                      flexShrink: 0,
                                      position: "absolute",
                                      clip: "rect(1px, 1px, 1px, 1px)",
                                      clipPath:
                                        "polygon(0px 0px, 0px 0px, 0px 0px, 0px 0px)",
                                    }}
                                  >
                                    <button
                                      className="slds-button slds-button_icon slds-button_icon-bare"
                                      type="button"
                                      title="Open abhishek tomar Preview"
                                      style={{
                                        boxSizing: "border-box",
                                        font: "inherit",
                                        margin: "0px",
                                        overflow: "visible",
                                        textTransform: "none",
                                        cursor: "pointer",
                                        backgroundPosition: "initial",
                                        borderColor:
                                          "var(--slds-c-button-color-border, var(--sds-c-button-color-border, transparent))",
                                        borderStyle: "solid",
                                        borderWidth:
                                          "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                        borderRadius:
                                          "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                        textDecoration: "none",
                                        whiteSpace: "normal",
                                        position: "relative",
                                        display: "inline-flex",
                                        alignItems: "center",
                                        paddingTop:
                                          "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                        paddingRight:
                                          "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                        paddingBottom:
                                          "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                        paddingLeft:
                                          "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                        backgroundImage: "none",
                                        backgroundSize: "initial",
                                        backgroundRepeat: "initial",
                                        backgroundAttachment: "initial",
                                        backgroundOrigin: "initial",
                                        backgroundColor:
                                          "var(--slds-c-button-color-background, var(--sds-c-button-color-background, transparent))",
                                        backgroundClip: "border-box",
                                        boxShadow:
                                          "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                        appearance: "none",
                                        userSelect: "none",
                                        lineHeight:
                                          "var(--lwc-lineHeightReset,1)",
                                        verticalAlign: "middle",
                                        justifyContent: "center",
                                        color:
                                          "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                        flexShrink: 0,
                                      }}
                                    >
                                      <lightning-primitive-icon
                                        style={{ boxSizing: "border-box" }}
                                      >
                                        <svg
                                          className="slds-button__icon"
                                          aria-hidden="true"
                                          focusable="false"
                                          viewBox="0 0 520 520"
                                          style={{
                                            boxSizing: "border-box",
                                            verticalAlign: "middle",
                                            width:
                                              "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                            height:
                                              "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                            fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                            overflow: "hidden",
                                          }}
                                        >
                                          <g
                                            style={{ boxSizing: "border-box" }}
                                          >
                                            <path
                                              d="M518 251a288 288 0 00-516 0 20 20 0 000 18 288 288 0 00516 0 20 20 0 000-18zM260 370c-61 0-110-49-110-110s49-110 110-110 110 49 110 110-49 110-110 110zm0-180c-39 0-70 31-70 70s31 70 70 70 70-31 70-70-31-70-70-70z"
                                              style={{
                                                boxSizing: "border-box",
                                              }}
                                            />
                                          </g>
                                        </svg>
                                      </lightning-primitive-icon>
                                      <span
                                        className="slds-assistive-text"
                                        style={{
                                          boxSizing: "border-box",
                                          margin: "-1px",
                                          border: "0px",
                                          padding: "0px",
                                          overflow: "hidden",
                                          whiteSpace: "nowrap",
                                          position: "absolute",
                                          width: "1px",
                                          height: "1px",
                                          clip: "rect(0px, 0px, 0px, 0px)",
                                          textTransform: "none",
                                        }}
                                      >
                                        Open abhishek tomar Preview
                                      </span>
                                      <span
                                        className="slds-assistive-text"
                                        style={{
                                          boxSizing: "border-box",
                                          margin: "-1px",
                                          border: "0px",
                                          padding: "0px",
                                          overflow: "hidden",
                                          whiteSpace: "nowrap",
                                          position: "absolute",
                                          width: "1px",
                                          height: "1px",
                                          clip: "rect(0px, 0px, 0px, 0px)",
                                          textTransform: "none",
                                        }}
                                      >
                                        Open abhishek tomar Preview
                                      </span>
                                    </button>
                                  </lightning-button-icon>
                                </div>
                              </records-hoverable-link>
                            </div>
                          </force-lookup>
                        </span>
                        <lightning-button-icon
                          className="slds-shrink-none change-owner-trigger"
                          size="medium"
                          style={{ boxSizing: "border-box", flexShrink: 0 }}
                        >
                          <button
                            className="slds-button slds-button_icon slds-button_icon-bare"
                            type="button"
                            title="Change Owner"
                            style={{
                              boxSizing: "border-box",
                              font: "inherit",
                              margin: "0px",
                              overflow: "visible",
                              textTransform: "none",
                              cursor: "pointer",
                              backgroundPosition: "initial",
                              borderColor:
                                "var(--slds-c-button-color-border, var(--sds-c-button-color-border, transparent))",
                              borderStyle: "solid",
                              borderWidth:
                                "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                              borderRadius:
                                "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                              textDecoration: "none",
                              whiteSpace: "normal",
                              position: "relative",
                              display: "inline-flex",
                              alignItems: "center",
                              paddingTop:
                                "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                              paddingRight:
                                "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                              paddingBottom:
                                "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                              paddingLeft:
                                "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                              backgroundImage: "none",
                              backgroundSize: "initial",
                              backgroundRepeat: "initial",
                              backgroundAttachment: "initial",
                              backgroundOrigin: "initial",
                              backgroundColor:
                                "var(--slds-c-button-color-background, var(--sds-c-button-color-background, transparent))",
                              backgroundClip: "border-box",
                              boxShadow:
                                "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                              appearance: "none",
                              userSelect: "none",
                              lineHeight: "var(--lwc-lineHeightReset,1)",
                              verticalAlign: "middle",
                              justifyContent: "center",
                              color:
                                "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                              flexShrink: 0,
                            }}
                          >
                            <lightning-primitive-icon
                              style={{ boxSizing: "border-box" }}
                            >
                              <svg
                                className="slds-button__icon slds-button__icon_hint"
                                aria-hidden="true"
                                focusable="false"
                                viewBox="0 0 520 520"
                                style={{
                                  boxSizing: "border-box",
                                  verticalAlign: "middle",
                                  width:
                                    "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                  height:
                                    "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                  fill: "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefaultHint,rgb(174, 174, 174)))",
                                  overflow: "hidden",
                                }}
                              >
                                <g style={{ boxSizing: "border-box" }}>
                                  <path
                                    d="M273 376c-30-12-35-23-35-35s8-23 18-32a82 82 0 0026-64c0-47-29-85-83-85s-83 38-83 85c0 25 8 49 26 64 10 9 18 20 18 32s-5 23-35 35c-44 18-86 38-87 76 2 26 22 48 47 48h230c25 0 45-22 45-47-1-38-43-59-87-77zm172-186c0-74-61-135-135-135V20l-68 55c-3 3-2 8 1 11l67 54v-35c47 0 85 38 85 85h-35l55 68c3 3 8 3 11 0l54-68z"
                                    style={{ boxSizing: "border-box" }}
                                  />
                                </g>
                              </svg>
                            </lightning-primitive-icon>
                            <span
                              className="slds-assistive-text"
                              style={{
                                boxSizing: "border-box",
                                margin: "-1px",
                                border: "0px",
                                padding: "0px",
                                overflow: "hidden",
                                whiteSpace: "nowrap",
                                position: "absolute",
                                width: "1px",
                                height: "1px",
                                clip: "rect(0px, 0px, 0px, 0px)",
                                textTransform: "none",
                              }}
                            >
                              Change Owner
                            </span>
                          </button>
                        </lightning-button-icon>
                      </div>
                    </force-owner-lookup>
                  </slot>
                </p>
              </div>
            </records-highlights-details-item>
            <records-highlights-details-item
              className="slds-page-header__detail-block"
              role="listitem"
              style={{
                boxSizing: "border-box",
                paddingRight: "var(--lwc-spacingXLarge,2rem)",
                paddingLeft: "var(--lwc-spacingXLarge,2rem)",
                overflow: "hidden",
                maxWidth: "136px",
              }}
            >
              <div style={{ boxSizing: "border-box" }}>
                <p
                  className="slds-text-title slds-truncate"
                  title="Account Site"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    maxWidth: "100%",
                    textOverflow: "ellipsis",
                    fontSize: "var(--lwc-fontSize2,0.75rem)",
                    lineHeight: "var(--lwc-lineHeightHeading,1.25)",
                    color:
                      "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                  }}
                >
                  Account Site
                </p>
                <p
                  className="fieldComponent slds-text-body--regular slds-show_inline-block slds-truncate"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    textOverflow: "ellipsis",
                    display: "inline-block",
                    fontSize: "0.875rem",
                    verticalAlign: "middle",
                    maxWidth: "100%",
                  }}
                >
                  <slot style={{ boxSizing: "border-box" }}>
                    <lightning-formatted-text
                      style={{ boxSizing: "border-box" }}
                    />
                  </slot>
                </p>
              </div>
            </records-highlights-details-item>
            <records-highlights-details-item
              className="slds-page-header__detail-block"
              role="listitem"
              style={{
                boxSizing: "border-box",
                paddingLeft: "var(--lwc-spacingXLarge,2rem)",
                overflow: "hidden",
                paddingRight: "0px",
                maxWidth: "79px",
              }}
            >
              <div style={{ boxSizing: "border-box" }}>
                <p
                  className="slds-text-title slds-truncate"
                  title="Industry"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    maxWidth: "100%",
                    textOverflow: "ellipsis",
                    fontSize: "var(--lwc-fontSize2,0.75rem)",
                    lineHeight: "var(--lwc-lineHeightHeading,1.25)",
                    color:
                      "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                  }}
                >
                  Industry
                </p>
                <p
                  className="fieldComponent slds-text-body--regular slds-show_inline-block slds-truncate"
                  style={{
                    boxSizing: "border-box",
                    margin: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    textOverflow: "ellipsis",
                    display: "inline-block",
                    fontSize: "0.875rem",
                    verticalAlign: "middle",
                    maxWidth: "100%",
                  }}
                >
                  <slot style={{ boxSizing: "border-box" }}>
                    <lightning-formatted-text
                      style={{ boxSizing: "border-box" }}
                    />
                  </slot>
                </p>
              </div>
            </records-highlights-details-item>
          </slot>
        </div>
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
html {
  box-sizing: border-box;
  text-size-adjust: 100%;
  background: var(--lwc-brandBackgroundPrimary,rgba(176, 196, 223, 1));
  font-family: var(--lwc-fontFamily,-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol');
  font-size: 100%;
  line-height: var(--lwc-varLineHeightText,1.5);
  color: var(--slds-g-color-neutral-base-10, var(--lwc-colorTextDefault,rgb(24, 24, 24)));
  -webkit-tap-highlight-color: transparent;
}

body {
  box-sizing: border-box;
  margin: 0px;
  background: transparent;
  font-size: var(--lwc-fontSize3,0.8125rem);
  overflow: visible;
}
`,
        }}
      />
    </>
  );
}
