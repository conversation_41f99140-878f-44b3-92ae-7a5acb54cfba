import React, { useContext } from "react";
import Related from "./Related";
import {
  LightningButton,
  LightningButtonMenu,
  ButtonGroup,
  BrandButton,
} from "../../common/LightningButton";
import styled from "styled-components";
import { GlobalContext } from "../../../context/GlobalContext";

const StageProgressContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: white;
  border-radius: 4px;
  margin: 10px 0;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
`;

const StageProgressBar = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
`;

const CompleteButton = styled(BrandButton)`
  background-color: #0070d2;
  color: white;
  padding: 0 1rem;
  height: 32px;
  font-size: 0.8125rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover,
  &:focus {
    background-color: #005fb2;
    color: white;
  }
`;

export default function Completepage() {
  const { allVariableData } = useContext(GlobalContext);
  return (
    <>
      <flexipage-record-home-with-subheader-template-desktop2
        className="forcegenerated-flexipage-template"
        style={{
          boxSizing: "border-box",
          height: "100%",
          display: "block",
          minWidth: "968px",
        }}
      >
        <div
          className="slds-grid slds-wrap"
          style={{
            boxSizing: "border-box",
            display: "flex",
            flexWrap: "wrap",
            alignItems: "flex-start",
          }}
        >
          <div
            className="slds-col slds-size_1-of-1 row region-header"
            style={{
              boxSizing: "border-box",
              width: "100%",
              flex: "0 0 auto",
              margin: "0px auto",
            }}
          >
            <slot name="header" style={{ boxSizing: "border-box" }}>
              <flexipage-component2
                style={{
                  boxSizing: "border-box",
                  display: "block",
                  gridColumnStart:
                    "var(--flexipage-fieldsection-column-index, auto)",
                }}
              >
                <slot style={{ boxSizing: "border-box" }}>
                  <records-lwc-highlights-panel
                    style={{ boxSizing: "border-box" }}
                  >
                    <force-placeholder2 style={{ boxSizing: "border-box" }} />
                    <records-lwc-record-layout
                      style={{ boxSizing: "border-box", display: "block" }}
                    >
                      <forcegenerated-highlightspanel_opportunity___012000000000000aaa___compact___view___recordlayout2
                        className="forcegenerated-record-layout2"
                        style={{ boxSizing: "border-box", display: "block" }}
                      >
                        <records-highlights2
                          style={{ boxSizing: "border-box" }}
                        >
                          <div
                            className="highlights slds-clearfix slds-page-header slds-page-header_record-home fixed-position"
                            style={{
                              boxSizing: "border-box",
                              padding:
                                "var(--lwc-varSpacingVerticalMedium,1rem) var(--lwc-varSpacingHorizontalMedium,1rem)",
                              border:
                                "var(--lwc-borderWidthThin,1px) solid var(--slds-g-color-border-base-1, var(--lwc-pageHeaderColorBorder,rgb(201, 201, 201)))",
                              backgroundImage: "",
                              backgroundPositionX: "",
                              backgroundPositionY: "",
                              backgroundSize: "",
                              backgroundRepeat: "",
                              backgroundAttachment: "",
                              backgroundOrigin: "",
                              backgroundColor: "",
                              backgroundClip: "padding-box",
                              boxShadow:
                                "var(--lwc-pageHeaderShadow,0 2px 2px 0 rgba(0, 0, 0, 0.10))",
                              position: "fixed",
                              zIndex: 98,
                              borderRadius: "4px",
                              height: "139.5px",
                              left: "11.1111px",
                              right: "15px",
                              paddingLeft: "16.8889px",
                              paddingRight: "16.8889px",
                              transform: "translate3d(0px, -4px, 0px)",
                            }}
                          >
                            <div
                              className="slds-grid primaryFieldRow"
                              style={{
                                boxSizing: "border-box",
                                display: "flex",
                              }}
                            >
                              <div
                                className="slds-grid slds-col slds-has-flexi-truncate"
                                style={{
                                  boxSizing: "border-box",
                                  display: "flex",
                                  flex: "1 1 0%",
                                  minWidth: "0px",
                                }}
                              >
                                <div
                                  className="slds-media slds-no-space"
                                  style={{
                                    boxSizing: "border-box",
                                    minWidth: "0px",
                                    display: "flex",
                                    alignItems: "flex-start",
                                  }}
                                >
                                  <slot
                                    name="icon"
                                    style={{ boxSizing: "border-box" }}
                                  >
                                    <records-highlights-icon
                                      style={{ boxSizing: "border-box" }}
                                    >
                                      <div
                                        className="highlights-icon-container slds-avatar slds-m-right_small icon"
                                        style={{
                                          boxSizing: "border-box",
                                          overflow: "hidden",
                                          borderRadius:
                                            "var(--slds-c-avatar-radius-border, var(--sds-c-avatar-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                          width:
                                            "var(--lwc-squareIconMediumBoundary,2rem)",
                                          height:
                                            "var(--lwc-squareIconMediumBoundary,2rem)",
                                          display: "inline-block",
                                          verticalAlign: "middle",
                                          lineHeight:
                                            "var(--lwc-lineHeightReset,1)",
                                          fontSize:
                                            "var(--lwc-fontSizeHeadingSmall,0.875rem)",
                                          color:
                                            "var(--slds-c-avatar-text-color, var(--sds-c-avatar-text-color, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))))",
                                          marginRight:
                                            "var(--lwc-spacingSmall,0.75rem)",
                                          backgroundColor: "rgb(255, 93, 45)",
                                        }}
                                      >
                                        <img
                                          src="/assets/icons/action/new_opportunity_120.png"
                                          title="Opportunity"
                                          style={{
                                            boxSizing: "border-box",
                                            border: "0px",
                                            verticalAlign: "middle",
                                            maxWidth: "100%",
                                            height: "auto",
                                          }}
                                        />
                                      </div>
                                    </records-highlights-icon>
                                  </slot>
                                </div>
                                <div
                                  className="slds-media__body"
                                  style={{
                                    boxSizing: "border-box",
                                    flex: "1 1 0%",
                                    minWidth: "0px",
                                    marginBottom: "0px",
                                  }}
                                >
                                  <h1
                                    style={{
                                      boxSizing: "border-box",
                                      margin: "0px",
                                      padding: "0px",
                                      fontWeight: "inherit",
                                      fontSize: "1em",
                                      marginBottom: "0px",
                                    }}
                                  >
                                    <div
                                      className="entityNameTitle slds-line-height--reset"
                                      style={{
                                        boxSizing: "border-box",
                                        lineHeight:
                                          "var(--lwc-lineHeightReset,1)",
                                        fontSize: "0.8125rem",
                                        color:
                                          "var(--slds-g-color-neutral-base-30, rgb(68, 68, 68))",
                                      }}
                                    >
                                      <slot
                                        name="entityLabel"
                                        style={{ boxSizing: "border-box" }}
                                      >
                                        <records-entity-label
                                          style={{ boxSizing: "border-box" }}
                                        >
                                          Opportunity
                                        </records-entity-label>
                                      </slot>
                                    </div>
                                    <slot
                                      className="slds-page-header__title slds-m-right--small slds-align-middle slds-line-clamp clip-text"
                                      name="primaryField"
                                      style={{
                                        boxSizing: "border-box",
                                        fontSize:
                                          "var(--lwc-pageHeaderTitleFontSize,1.125rem)",
                                        fontWeight:
                                          "var(--lwc-pageHeaderTitleFontWeight,700)",
                                        lineHeight:
                                          "var(--lwc-lineHeightHeading,1.25)",
                                        verticalAlign: "middle",
                                        alignSelf: "center",
                                        marginRight:
                                          "var(--lwc-spacingSmall,0.75rem)",
                                        whiteSpace: "pre-line",
                                        display: "-webkit-box",
                                        WebkitBoxOrient: "vertical",
                                        textOverflow: "ellipsis",
                                        overflow: "hidden",
                                        hyphens: "auto",
                                        overflowWrap: "break-word",
                                        wordBreak: "break-word",
                                        position: "relative",
                                        maxHeight: "2.8125rem",
                                        WebkitLineClamp: "2",
                                      }}
                                    >
                                      <lightning-formatted-text
                                        style={{ boxSizing: "border-box" }}
                                      >
                                        {allVariableData?.opportunityList
                                          ?.length
                                          ? allVariableData.opportunityList.slice(
                                              -1
                                            )[0].Name
                                          : "Opportunity Name"}
                                      </lightning-formatted-text>
                                    </slot>
                                  </h1>
                                </div>
                                <div
                                  className="header-right-content"
                                  style={{
                                    boxSizing: "border-box",
                                    display: "flex",
                                    alignItems: "center",
                                  }}
                                >
                                  <slot
                                    name="headerRightContent"
                                    style={{ boxSizing: "border-box" }}
                                  >
                                    <records-highlights-header-right-content
                                      style={{ boxSizing: "border-box" }}
                                    />
                                  </slot>
                                </div>
                              </div>
                              <div
                                className="slds-col slds-no-flex slds-grid slds-grid_vertical-align-center horizontal slds-m-right--xx-small chatterActionContainer"
                                style={{
                                  boxSizing: "border-box",
                                  display: "flex",
                                  flex: "0 0 auto",
                                  alignItems: "center",
                                  alignContent: "center",
                                  marginRight:
                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                }}
                              >
                                <div
                                  className="slds-grid forceActionsContainer"
                                  style={{
                                    boxSizing: "border-box",
                                    display: "flex",
                                  }}
                                >
                                  <div
                                    style={{
                                      boxSizing: "border-box",
                                      display: "block",
                                    }}
                                  >
                                    <LightningButton
                                      variant="neutral"
                                      className="uiButton"
                                      $borderColor="black"
                                    >
                                      <svg
                                        className="slds-icon slds-icon_xx-small"
                                        aria-hidden="true"
                                        focusable="false"
                                        viewBox="0 0 520 520"
                                        style={{
                                          boxSizing: "border-box",
                                          verticalAlign: "middle",
                                          lineHeight:
                                            "var(--lwc-lineHeightReset,1)",
                                          overflow: "hidden",
                                          width:
                                            "var(--lwc-squareIconXxSmallContent,.875rem)",
                                          height:
                                            "var(--lwc-squareIconXxSmallContent,.875rem)",
                                          fill: "currentcolor",
                                          marginRight:
                                            "var(--lwc-spacingXSmall,0.5rem)",
                                        }}
                                      >
                                        <g>
                                          <path d="M300 290h165c8 0 15-7 15-15v-30c0-8-7-15-15-15H300c-6 0-10-4-10-10V55c0-8-7-15-15-15h-30c-8 0-15 7-15 15v165c0 6-4 10-10 10H55c-8 0-15 7-15 15v30c0 8 7 15 15 15h165c6 0 10 4 10 10v165c0 8 7 15 15 15h30c8 0 15-7 15-15V300c0-6 4-10 10-10z" />
                                        </g>
                                      </svg>
                                      Follow
                                    </LightningButton>
                                  </div>
                                </div>
                              </div>
                              <div
                                className="slds-col slds-no-flex slds-grid slds-grid_vertical-align-center horizontal actionsContainer"
                                style={{
                                  boxSizing: "border-box",
                                  display: "flex",
                                  flex: "0 0 auto",
                                  alignItems: "center",
                                  alignContent: "center",
                                }}
                              >
                                <div style={{ boxSizing: "border-box" }}>
                                  <runtime_platform_actions-actions-ribbon
                                    style={{ boxSizing: "border-box" }}
                                  >
                                    <slot
                                      name="actionsProvider"
                                      style={{ boxSizing: "border-box" }}
                                    >
                                      <runtime_platform_actions-provider-record-detail-ple
                                        style={{ boxSizing: "border-box" }}
                                      />
                                    </slot>
                                    <ul
                                      className="slds-button-group-list"
                                      role="presentation"
                                      style={{
                                        boxSizing: "border-box",
                                        margin: "0px",
                                        padding: "0px",
                                        listStyle: "none",
                                        display: "inline-flex",
                                      }}
                                    >
                                      <li
                                        className="visible"
                                        role="presentation"
                                        style={{
                                          boxSizing: "border-box",
                                          display: "block",
                                        }}
                                      >
                                        <runtime_platform_actions-action-renderer
                                          title="New Case"
                                          style={{ boxSizing: "border-box" }}
                                        >
                                          <runtime_platform_actions-executor-page-reference
                                            style={{ boxSizing: "border-box" }}
                                          >
                                            <slot
                                              style={{
                                                boxSizing: "border-box",
                                              }}
                                            >
                                              <slot
                                                style={{
                                                  boxSizing: "border-box",
                                                }}
                                              >
                                                <LightningButton
                                                  variant="neutral"
                                                  label="New Case"
                                                  name="Global.NewCase"
                                                  $borderRadius="var(--lwc-buttonBorderRadius,.25rem) 0 0 var(--lwc-buttonBorderRadius,.25rem)"
                                                  $borderColor="black"
                                                />
                                              </slot>
                                            </slot>
                                          </runtime_platform_actions-executor-page-reference>
                                        </runtime_platform_actions-action-renderer>
                                      </li>
                                      <li
                                        className="visible"
                                        role="presentation"
                                        style={{
                                          boxSizing: "border-box",
                                          display: "block",
                                        }}
                                      >
                                        <runtime_platform_actions-action-renderer
                                          title="New Note"
                                          style={{ boxSizing: "border-box" }}
                                        >
                                          <runtime_platform_actions-executor-page-reference
                                            style={{ boxSizing: "border-box" }}
                                          >
                                            <slot
                                              style={{
                                                boxSizing: "border-box",
                                              }}
                                            >
                                              <slot
                                                style={{
                                                  boxSizing: "border-box",
                                                }}
                                              >
                                                <LightningButton
                                                  variant="neutral"
                                                  label="New Note"
                                                  name="Global.NewNote"
                                                  $borderRadius="0"
                                                  $marginLeft="-1px"
                                                  $borderColor="black"
                                                />
                                              </slot>
                                            </slot>
                                          </runtime_platform_actions-executor-page-reference>
                                        </runtime_platform_actions-action-renderer>
                                      </li>
                                      <li
                                        className="visible"
                                        role="presentation"
                                        style={{
                                          boxSizing: "border-box",
                                          display: "block",
                                        }}
                                      >
                                        <runtime_platform_actions-action-renderer
                                          title="Clone"
                                          style={{ boxSizing: "border-box" }}
                                        >
                                          <runtime_platform_actions-executor-page-reference
                                            style={{ boxSizing: "border-box" }}
                                          >
                                            <slot
                                              style={{
                                                boxSizing: "border-box",
                                              }}
                                            >
                                              <slot
                                                style={{
                                                  boxSizing: "border-box",
                                                }}
                                              >
                                                <LightningButton
                                                  variant="neutral"
                                                  label="Clone"
                                                  name="Clone"
                                                  $borderRadius="0"
                                                  $marginLeft="-1px"
                                                  $borderColor="black"
                                                />
                                              </slot>
                                            </slot>
                                          </runtime_platform_actions-executor-page-reference>
                                        </runtime_platform_actions-action-renderer>
                                      </li>
                                      <li
                                        className="slds-dropdown-trigger slds-dropdown-trigger_click slds-button_last overflow"
                                        role="presentation"
                                        style={{
                                          boxSizing: "border-box",
                                          position: "relative",
                                          display: "inline-block",
                                        }}
                                      >
                                        <LightningButtonMenu
                                          className="menu-button-item slds-dropdown_actions slds-dropdown-trigger slds-dropdown-trigger_click"
                                          style={{
                                            boxSizing: "border-box",
                                            position: "relative",
                                            display: "inline-block",
                                          }}
                                        >
                                          <div
                                            className="slds-dropdown slds-dropdown_right"
                                            style={{
                                              boxSizing: "border-box",
                                              border:
                                                "var(--lwc-borderWidthThin,1px) solid var(--slds-g-color-border-base-4, var(--lwc-colorBorder,rgb(229, 229, 229)))",
                                              borderRadius:
                                                "var(--lwc-borderRadiusMedium,0.25rem)",
                                              padding:
                                                "var(--lwc-spacingXxSmall,0.25rem) 0",
                                              background:
                                                "var(--slds-g-color-neutral-base-100, var(--lwc-colorBackgroundAlt,rgb(255, 255, 255)))",
                                              position: "absolute",
                                              zIndex:
                                                "var(--lwc-zIndexDropdown,7000)",
                                              cssFloat: "left",
                                              minWidth:
                                                "var(--lwc-sizeXxSmall,6rem)",
                                              maxWidth:
                                                "var(--lwc-sizeMedium,20rem)",
                                              marginTop:
                                                "var(--lwc-spacingXxxSmall,0.125rem)",
                                              marginBottom:
                                                "var(--lwc-spacingXxxSmall,0.125rem)",
                                              fontSize:
                                                "var(--lwc-fontSize2,0.75rem)",
                                              boxShadow:
                                                "var(--lwc-shadowDropDown,0 2px 3px 0 rgba(0, 0, 0, 0.16))",
                                              color:
                                                "var(--slds-g-color-neutral-base-10, var(--lwc-colorTextDefault,rgb(24, 24, 24)))",
                                              left: "auto",
                                              right: "0px",
                                              transform: "translateX(0px)",
                                              transition:
                                                "opacity var(--lwc-durationQuickly,0.1s) linear,visibility var(--lwc-durationQuickly,0.1s) linear",
                                              visibility: "hidden",
                                              opacity: 0,
                                              top: "100%",
                                              display: "none",
                                              width: "max-content",
                                            }}
                                          >
                                            <div
                                              className="slds-dropdown__list slds-dropdown_length-with-icon-10"
                                              aria-labelledby="button-label-495"
                                              role="menu"
                                              style={{
                                                boxSizing: "border-box",
                                                maxHeight: "calc(25rem)",
                                                overflowY: "auto",
                                              }}
                                            >
                                              <slot
                                                style={{
                                                  boxSizing: "border-box",
                                                }}
                                              >
                                                <runtime_platform_actions-action-renderer
                                                  title="Submit for Approval"
                                                  style={{
                                                    boxSizing: "border-box",
                                                  }}
                                                >
                                                  <runtime_platform_actions-executor-aura-legacy
                                                    style={{
                                                      boxSizing: "border-box",
                                                    }}
                                                  >
                                                    <slot
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <slot
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <lightning-menu-item
                                                          role="presentation"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="slds-dropdown__item"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              lineHeight:
                                                                "var(--lwc-lineHeightText,1.5)",
                                                              fontWeight:
                                                                "var(--lwc-fontWeightRegular,400)",
                                                            }}
                                                          >
                                                            <a
                                                              aria-disabled="false"
                                                              href="#"
                                                              role="menuitem"
                                                              tabIndex="-1"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                backgroundColor:
                                                                  "transparent",
                                                                textDecoration:
                                                                  "none",
                                                                transition:
                                                                  "color 0.1s linear",
                                                                padding:
                                                                  "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingSmall,0.75rem)",
                                                                whiteSpace:
                                                                  "nowrap",
                                                                position:
                                                                  "relative",
                                                                display: "flex",
                                                                justifyContent:
                                                                  "space-between",
                                                                alignItems:
                                                                  "center",
                                                                cursor:
                                                                  "pointer",
                                                                color:
                                                                  "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                                                              }}
                                                            >
                                                              <span
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                Submit for
                                                                Approval
                                                              </span>
                                                            </a>
                                                          </div>
                                                        </lightning-menu-item>
                                                      </slot>
                                                    </slot>
                                                  </runtime_platform_actions-executor-aura-legacy>
                                                </runtime_platform_actions-action-renderer>
                                                <runtime_platform_actions-action-renderer
                                                  title="Delete"
                                                  style={{
                                                    boxSizing: "border-box",
                                                  }}
                                                >
                                                  <runtime_platform_actions-executor-aura-legacy
                                                    style={{
                                                      boxSizing: "border-box",
                                                    }}
                                                  >
                                                    <slot
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <slot
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <lightning-menu-item
                                                          role="presentation"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="slds-dropdown__item"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              lineHeight:
                                                                "var(--lwc-lineHeightText,1.5)",
                                                              fontWeight:
                                                                "var(--lwc-fontWeightRegular,400)",
                                                            }}
                                                          >
                                                            <a
                                                              aria-disabled="false"
                                                              href="#"
                                                              role="menuitem"
                                                              tabIndex="-1"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                backgroundColor:
                                                                  "transparent",
                                                                textDecoration:
                                                                  "none",
                                                                transition:
                                                                  "color 0.1s linear",
                                                                padding:
                                                                  "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingSmall,0.75rem)",
                                                                whiteSpace:
                                                                  "nowrap",
                                                                position:
                                                                  "relative",
                                                                display: "flex",
                                                                justifyContent:
                                                                  "space-between",
                                                                alignItems:
                                                                  "center",
                                                                cursor:
                                                                  "pointer",
                                                                color:
                                                                  "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                                                              }}
                                                            >
                                                              <span
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                Delete
                                                              </span>
                                                            </a>
                                                          </div>
                                                        </lightning-menu-item>
                                                      </slot>
                                                    </slot>
                                                  </runtime_platform_actions-executor-aura-legacy>
                                                </runtime_platform_actions-action-renderer>
                                                <runtime_platform_actions-action-renderer
                                                  title="Edit"
                                                  style={{
                                                    boxSizing: "border-box",
                                                  }}
                                                >
                                                  <runtime_platform_actions-executor-page-reference
                                                    style={{
                                                      boxSizing: "border-box",
                                                    }}
                                                  >
                                                    <slot
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <slot
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <lightning-menu-item
                                                          role="presentation"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="slds-dropdown__item"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              lineHeight:
                                                                "var(--lwc-lineHeightText,1.5)",
                                                              fontWeight:
                                                                "var(--lwc-fontWeightRegular,400)",
                                                            }}
                                                          >
                                                            <a
                                                              aria-disabled="false"
                                                              href="#"
                                                              role="menuitem"
                                                              tabIndex="-1"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                backgroundColor:
                                                                  "transparent",
                                                                textDecoration:
                                                                  "none",
                                                                transition:
                                                                  "color 0.1s linear",
                                                                padding:
                                                                  "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingSmall,0.75rem)",
                                                                whiteSpace:
                                                                  "nowrap",
                                                                position:
                                                                  "relative",
                                                                display: "flex",
                                                                justifyContent:
                                                                  "space-between",
                                                                alignItems:
                                                                  "center",
                                                                cursor:
                                                                  "pointer",
                                                                color:
                                                                  "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                                                              }}
                                                            >
                                                              <span
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                Edit
                                                              </span>
                                                            </a>
                                                          </div>
                                                        </lightning-menu-item>
                                                      </slot>
                                                    </slot>
                                                  </runtime_platform_actions-executor-page-reference>
                                                </runtime_platform_actions-action-renderer>
                                                <runtime_platform_actions-action-renderer
                                                  title="Change Owner"
                                                  style={{
                                                    boxSizing: "border-box",
                                                  }}
                                                >
                                                  <runtime_platform_actions-executor-aura-legacy
                                                    style={{
                                                      boxSizing: "border-box",
                                                    }}
                                                  >
                                                    <slot
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <slot
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <lightning-menu-item
                                                          role="presentation"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="slds-dropdown__item"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              lineHeight:
                                                                "var(--lwc-lineHeightText,1.5)",
                                                              fontWeight:
                                                                "var(--lwc-fontWeightRegular,400)",
                                                            }}
                                                          >
                                                            <a
                                                              aria-disabled="false"
                                                              href="#"
                                                              role="menuitem"
                                                              tabIndex="-1"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                backgroundColor:
                                                                  "transparent",
                                                                textDecoration:
                                                                  "none",
                                                                transition:
                                                                  "color 0.1s linear",
                                                                padding:
                                                                  "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingSmall,0.75rem)",
                                                                whiteSpace:
                                                                  "nowrap",
                                                                position:
                                                                  "relative",
                                                                display: "flex",
                                                                justifyContent:
                                                                  "space-between",
                                                                alignItems:
                                                                  "center",
                                                                cursor:
                                                                  "pointer",
                                                                color:
                                                                  "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                                                              }}
                                                            >
                                                              <span
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                Change Owner
                                                              </span>
                                                            </a>
                                                          </div>
                                                        </lightning-menu-item>
                                                      </slot>
                                                    </slot>
                                                  </runtime_platform_actions-executor-aura-legacy>
                                                </runtime_platform_actions-action-renderer>
                                              </slot>
                                            </div>
                                          </div>
                                        </LightningButtonMenu>
                                      </li>
                                    </ul>
                                  </runtime_platform_actions-actions-ribbon>
                                </div>
                              </div>
                            </div>
                            <div
                              className="secondaryFields"
                              role="presentation"
                              style={{
                                boxSizing: "border-box",
                                visibility: "visible",
                                transform: "translate3d(0px, 0px, 0px)",
                                opacity: 1,
                              }}
                            >
                              <slot
                                className="slds-grid slds-page-header__detail-row"
                                name="secondaryFields"
                                style={{
                                  boxSizing: "border-box",
                                  margin:
                                    "var(--lwc-varSpacingVerticalSmall,0.75rem) calc(-1 * var(--lwc-varSpacingHorizontalMedium,1rem)) calc(-1 * var(--lwc-varSpacingVerticalMedium,1rem))",
                                  borderRadius:
                                    "0 0 var(--lwc-pageHeaderBorderRadius,0.25rem) var(--lwc-pageHeaderBorderRadius,0.25rem)",
                                  backgroundColor:
                                    "var(--slds-g-color-neutral-base-100, var(--lwc-pageHeaderColorBackgroundAlt,rgb(255, 255, 255)))",
                                  position: "relative",
                                  zIndex: 2,
                                  display: "flex",
                                  padding: "1rem 1rem",
                                  fill: "var(--slds-g-color-neutral-base-80, rgb(243, 243, 243))",
                                  marginTop: "1rem",
                                  marginRight: "calc(-1 * 1rem)",
                                  marginLeft: "calc(-1 * 1rem)",
                                  marginBottom: "calc(-1 * 1rem)",
                                }}
                              >
                                <records-highlights-details-item
                                  className="slds-page-header__detail-block"
                                  role="listitem"
                                  style={{
                                    boxSizing: "border-box",
                                    paddingRight:
                                      "var(--lwc-spacingXLarge,2rem)",
                                    overflow: "hidden",
                                    paddingLeft: "0px",
                                    maxWidth: "115px",
                                  }}
                                >
                                  <div style={{ boxSizing: "border-box" }}>
                                    <p
                                      className="slds-text-title slds-truncate"
                                      title="Account Name"
                                      style={{
                                        boxSizing: "border-box",
                                        margin: "0px",
                                        padding: "0px",
                                        overflow: "hidden",
                                        whiteSpace: "nowrap",
                                        maxWidth: "100%",
                                        textOverflow: "ellipsis",
                                        fontSize:
                                          "var(--lwc-fontSize2,0.75rem)",
                                        lineHeight:
                                          "var(--lwc-lineHeightHeading,1.25)",
                                        color:
                                          "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                                      }}
                                    >
                                      Account Name
                                    </p>
                                    <div
                                      className="fieldComponent slds-text-body--regular slds-show_inline-block slds-truncate"
                                      style={{
                                        boxSizing: "border-box",
                                        margin: "0px",
                                        padding: "0px",
                                        overflow: "hidden",
                                        whiteSpace: "nowrap",
                                        textOverflow: "ellipsis",
                                        display: "inline-block",
                                        fontSize: "0.875rem",
                                        verticalAlign: "middle",
                                        maxWidth: "100%",
                                      }}
                                    >
                                      <slot style={{ boxSizing: "border-box" }}>
                                        <force-lookup
                                          style={{
                                            boxSizing: "border-box",
                                            display: "inline-block",
                                            maxWidth: "100%",
                                            verticalAlign: "middle",
                                          }}
                                        >
                                          <div
                                            className="slds-grid"
                                            style={{
                                              boxSizing: "border-box",
                                              display: "flex",
                                            }}
                                          >
                                            <span
                                              className="slds-truncate not-navigable"
                                              style={{
                                                boxSizing: "border-box",
                                                overflow: "hidden",
                                                whiteSpace: "nowrap",
                                                maxWidth: "100%",
                                                textOverflow: "ellipsis",
                                              }}
                                            >
                                              <slot
                                                style={{
                                                  boxSizing: "border-box",
                                                }}
                                              >
                                                <span
                                                  style={{
                                                    boxSizing: "border-box",
                                                  }}
                                                >
                                                  {allVariableData
                                                    ?.opportunityList?.length
                                                    ? allVariableData.opportunityList.slice(
                                                        -1
                                                      )[0].AccountName
                                                    : ""}
                                                </span>
                                              </slot>
                                            </span>
                                          </div>
                                        </force-lookup>
                                      </slot>
                                    </div>
                                  </div>
                                </records-highlights-details-item>
                                <records-highlights-details-item
                                  className="slds-page-header__detail-block"
                                  role="listitem"
                                  style={{
                                    boxSizing: "border-box",
                                    paddingRight:
                                      "var(--lwc-spacingXLarge,2rem)",
                                    paddingLeft:
                                      "var(--lwc-spacingXLarge,2rem)",
                                    overflow: "hidden",
                                    maxWidth: "129px",
                                  }}
                                >
                                  <div style={{ boxSizing: "border-box" }}>
                                    <p
                                      className="slds-text-title slds-truncate"
                                      title="Close Date"
                                      style={{
                                        boxSizing: "border-box",
                                        margin: "0px",
                                        padding: "0px",
                                        overflow: "hidden",
                                        whiteSpace: "nowrap",
                                        maxWidth: "100%",
                                        textOverflow: "ellipsis",
                                        fontSize:
                                          "var(--lwc-fontSize2,0.75rem)",
                                        lineHeight:
                                          "var(--lwc-lineHeightHeading,1.25)",
                                        color:
                                          "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                                      }}
                                    >
                                      Close Date
                                    </p>
                                    <div
                                      className="fieldComponent slds-text-body--regular slds-show_inline-block slds-truncate"
                                      style={{
                                        boxSizing: "border-box",
                                        margin: "0px",
                                        padding: "0px",
                                        overflow: "hidden",
                                        whiteSpace: "nowrap",
                                        textOverflow: "ellipsis",
                                        display: "inline-block",
                                        fontSize: "0.875rem",
                                        verticalAlign: "middle",
                                        maxWidth: "100%",
                                      }}
                                    >
                                      <slot style={{ boxSizing: "border-box" }}>
                                        <lightning-formatted-text
                                          style={{ boxSizing: "border-box" }}
                                        >
                                          {allVariableData?.opportunityList
                                            ?.length
                                            ? allVariableData.opportunityList.slice(
                                                -1
                                              )[0].CloseDate
                                            : ""}
                                        </lightning-formatted-text>
                                      </slot>
                                    </div>
                                  </div>
                                </records-highlights-details-item>
                                <records-highlights-details-item
                                  className="slds-page-header__detail-block"
                                  role="listitem"
                                  style={{
                                    boxSizing: "border-box",
                                    paddingRight:
                                      "var(--lwc-spacingXLarge,2rem)",
                                    paddingLeft:
                                      "var(--lwc-spacingXLarge,2rem)",
                                    overflow: "hidden",
                                    maxWidth: "129px",
                                  }}
                                >
                                  <div style={{ boxSizing: "border-box" }}>
                                    <p
                                      className="slds-text-title slds-truncate"
                                      title="Amount"
                                      style={{
                                        boxSizing: "border-box",
                                        margin: "0px",
                                        padding: "0px",
                                        overflow: "hidden",
                                        whiteSpace: "nowrap",
                                        maxWidth: "100%",
                                        textOverflow: "ellipsis",
                                        fontSize:
                                          "var(--lwc-fontSize2,0.75rem)",
                                        lineHeight:
                                          "var(--lwc-lineHeightHeading,1.25)",
                                        color:
                                          "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                                      }}
                                    >
                                      Amount
                                    </p>
                                    <div
                                      className="fieldComponent slds-text-body--regular slds-show_inline-block slds-truncate"
                                      style={{
                                        boxSizing: "border-box",
                                        margin: "0px",
                                        padding: "0px",
                                        overflow: "hidden",
                                        whiteSpace: "nowrap",
                                        textOverflow: "ellipsis",
                                        display: "inline-block",
                                        fontSize: "0.875rem",
                                        verticalAlign: "middle",
                                        maxWidth: "100%",
                                      }}
                                    >
                                      <slot style={{ boxSizing: "border-box" }}>
                                        <lightning-formatted-text
                                          style={{ boxSizing: "border-box" }}
                                        >
                                          {allVariableData?.opportunityList
                                            ?.length &&
                                          allVariableData.opportunityList.slice(
                                            -1
                                          )[0].Amount
                                            ? `$${parseFloat(
                                                allVariableData.opportunityList.slice(
                                                  -1
                                                )[0].Amount
                                              ).toLocaleString(undefined, {
                                                minimumFractionDigits: 2,
                                                maximumFractionDigits: 2,
                                              })}`
                                            : ""}
                                        </lightning-formatted-text>
                                      </slot>
                                    </div>
                                  </div>
                                </records-highlights-details-item>
                                <records-highlights-details-item
                                  className="slds-page-header__detail-block"
                                  role="listitem"
                                  style={{
                                    boxSizing: "border-box",
                                    paddingLeft:
                                      "var(--lwc-spacingXLarge,2rem)",
                                    overflow: "hidden",
                                    paddingRight: "0px",
                                    maxWidth: "161px",
                                  }}
                                >
                                  <div style={{ boxSizing: "border-box" }}>
                                    <p
                                      className="slds-text-title slds-truncate"
                                      title="Opportunity Owner"
                                      style={{
                                        boxSizing: "border-box",
                                        margin: "0px",
                                        padding: "0px",
                                        overflow: "hidden",
                                        whiteSpace: "nowrap",
                                        maxWidth: "100%",
                                        textOverflow: "ellipsis",
                                        fontSize:
                                          "var(--lwc-fontSize2,0.75rem)",
                                        lineHeight:
                                          "var(--lwc-lineHeightHeading,1.25)",
                                        color:
                                          "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                                      }}
                                    >
                                      Opportunity Owner
                                    </p>
                                    <div
                                      className="fieldComponent slds-text-body--regular slds-show_inline-block slds-truncate"
                                      style={{
                                        boxSizing: "border-box",
                                        margin: "0px",
                                        padding: "0px",
                                        overflow: "hidden",
                                        whiteSpace: "nowrap",
                                        textOverflow: "ellipsis",
                                        display: "inline-block",
                                        fontSize: "0.875rem",
                                        verticalAlign: "middle",
                                        maxWidth: "100%",
                                      }}
                                    >
                                      <slot style={{ boxSizing: "border-box" }}>
                                        <force-owner-lookup
                                          style={{
                                            boxSizing: "border-box",
                                            display: "block",
                                          }}
                                        >
                                          <div
                                            className="slds-grid"
                                            style={{
                                              boxSizing: "border-box",
                                              display: "flex",
                                            }}
                                          >
                                            <span
                                              className="owner-name slds-truncate slds-grow"
                                              style={{
                                                boxSizing: "border-box",
                                                flexGrow: 1,
                                                overflow: "hidden",
                                                whiteSpace: "nowrap",
                                                maxWidth: "100%",
                                                textOverflow: "ellipsis",
                                                wordBreak: "break-all",
                                                textAlign: "start",
                                              }}
                                            >
                                              <force-lookup
                                                style={{
                                                  boxSizing: "border-box",
                                                  display: "inline-block",
                                                  maxWidth: "100%",
                                                  verticalAlign: "middle",
                                                }}
                                              >
                                                <div
                                                  className="slds-grid"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    display: "flex",
                                                  }}
                                                >
                                                  <force-record-avatar
                                                    className="slds-shrink-none"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      flexShrink: 0,
                                                    }}
                                                  >
                                                    <span
                                                      className="record-avatar-container slds-avatar slds-avatar_x-small slds-avatar--circle icon slds-m-right_xx-small slds-avatar_profile-image-small"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        display: "inline-block",
                                                        verticalAlign: "middle",
                                                        lineHeight:
                                                          "var(--lwc-lineHeightReset,1)",
                                                        color:
                                                          "var(--slds-c-avatar-text-color, var(--sds-c-avatar-text-color, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))))",
                                                        width:
                                                          "var(--lwc-squareIconXSmallBoundary,1.25rem)",
                                                        height:
                                                          "var(--lwc-squareIconXSmallBoundary,1.25rem)",
                                                        fontSize:
                                                          "var(--lwc-fontSize1,0.625rem)",
                                                        marginRight:
                                                          "var(--lwc-spacingXxSmall,0.25rem)",
                                                        overflow: "hidden",
                                                        background:
                                                          "var(--lwc-userDefaultAvatarSmall,url(/assets/downloaded/lightning_blue_profile_avatar_96.png)) top left/cover no-repeat",
                                                        borderRadius:
                                                          "var(--slds-s-icon-radius-border, 0.25rem)",
                                                        backgroundColor:
                                                          "rgb(27, 150, 255)",
                                                      }}
                                                    />
                                                  </force-record-avatar>
                                                  <records-hoverable-link
                                                    className="slds-grow has-avatar"
                                                    tabIndex="-1"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      flexGrow: 1,
                                                      display: "block",
                                                      width: "100%",
                                                      overflow: "hidden",
                                                      padding:
                                                        "var(--slds-g-sizing-border-2, var(--lwc-borderWidthThick, 2px))",
                                                    }}
                                                  >
                                                    <div
                                                      className="slds-grid"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        display: "flex",
                                                      }}
                                                    >
                                                      <a
                                                        className="slds-truncate"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          backgroundColor:
                                                            "transparent",
                                                          textDecoration:
                                                            "none",
                                                          transition:
                                                            "color 0.1s linear",
                                                          color:
                                                            "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                                                          cursor: "pointer",
                                                          overflow: "hidden",
                                                          whiteSpace: "nowrap",
                                                          maxWidth: "100%",
                                                          textOverflow:
                                                            "ellipsis",
                                                        }}
                                                      >
                                                        <span
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            textDecorationLine:
                                                              "underline",
                                                            textDecorationThickness:
                                                              "var(--slds-g-sizing-border-1, var(--lwc-borderWidthThin, 1px))",
                                                            textDecorationStyle:
                                                              "dotted",
                                                            textUnderlineOffset:
                                                              "2px",
                                                          }}
                                                        >
                                                          <slot
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                            }}
                                                          >
                                                            <span
                                                              className="slds-truncate"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                overflow:
                                                                  "hidden",
                                                                whiteSpace:
                                                                  "nowrap",
                                                                maxWidth:
                                                                  "100%",
                                                                textOverflow:
                                                                  "ellipsis",
                                                              }}
                                                            >
                                                              <slot
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <span
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  seven steven
                                                                </span>
                                                              </slot>
                                                            </span>
                                                          </slot>
                                                        </span>
                                                      </a>
                                                      <lightning-button-icon
                                                        className="slds-button_icon hover-button-icon-element hide-hover-icon"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          lineHeight:
                                                            "var(--lwc-lineHeightReset,1)",
                                                          verticalAlign:
                                                            "middle",
                                                          justifyContent:
                                                            "center",
                                                          color:
                                                            "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                          flexShrink: 0,
                                                          position: "absolute",
                                                          clip: "rect(1px, 1px, 1px, 1px)",
                                                          clipPath:
                                                            "polygon(0px 0px, 0px 0px, 0px 0px, 0px 0px)",
                                                        }}
                                                      >
                                                        <button
                                                          className="slds-button slds-button_icon slds-button_icon-bare"
                                                          type="button"
                                                          title="Open seven steven Preview"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            font: "inherit",
                                                            margin: "0px",
                                                            overflow: "visible",
                                                            textTransform:
                                                              "none",
                                                            cursor: "pointer",
                                                            backgroundPosition:
                                                              "initial",
                                                            borderColor:
                                                              "var(--slds-c-button-color-border, var(--sds-c-button-color-border, transparent))",
                                                            borderStyle:
                                                              "solid",
                                                            borderWidth:
                                                              "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                            borderRadius:
                                                              "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                            textDecoration:
                                                              "none",
                                                            whiteSpace:
                                                              "normal",
                                                            position:
                                                              "relative",
                                                            display:
                                                              "inline-flex",
                                                            alignItems:
                                                              "center",
                                                            paddingTop:
                                                              "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                            paddingRight:
                                                              "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                            paddingBottom:
                                                              "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                            paddingLeft:
                                                              "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                            backgroundImage:
                                                              "none",
                                                            backgroundSize:
                                                              "initial",
                                                            backgroundRepeat:
                                                              "initial",
                                                            backgroundAttachment:
                                                              "initial",
                                                            backgroundOrigin:
                                                              "initial",
                                                            backgroundColor:
                                                              "var(--slds-c-button-color-background, var(--sds-c-button-color-background, transparent))",
                                                            backgroundClip:
                                                              "border-box",
                                                            boxShadow:
                                                              "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                            appearance: "none",
                                                            userSelect: "none",
                                                            lineHeight:
                                                              "var(--lwc-lineHeightReset,1)",
                                                            verticalAlign:
                                                              "middle",
                                                            justifyContent:
                                                              "center",
                                                            color:
                                                              "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                            flexShrink: 0,
                                                          }}
                                                        >
                                                          <lightning-primitive-icon
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                            }}
                                                          >
                                                            <svg
                                                              className="slds-button__icon"
                                                              aria-hidden="true"
                                                              focusable="false"
                                                              viewBox="0 0 520 520"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                verticalAlign:
                                                                  "middle",
                                                                width:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                height:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                                                overflow:
                                                                  "hidden",
                                                              }}
                                                            >
                                                              <g
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <path
                                                                  d="M518 251a288 288 0 00-516 0 20 20 0 000 18 288 288 0 00516 0 20 20 0 000-18zM260 370c-61 0-110-49-110-110s49-110 110-110 110 49 110 110-49 110-110 110zm0-180c-39 0-70 31-70 70s31 70 70 70 70-31 70-70-31-70-70-70z"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                />
                                                              </g>
                                                            </svg>
                                                          </lightning-primitive-icon>
                                                          <span
                                                            className="slds-assistive-text"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              margin: "-1px",
                                                              border: "0px",
                                                              padding: "0px",
                                                              overflow:
                                                                "hidden",
                                                              whiteSpace:
                                                                "nowrap",
                                                              position:
                                                                "absolute",
                                                              width: "1px",
                                                              height: "1px",
                                                              clip: "rect(0px, 0px, 0px, 0px)",
                                                              textTransform:
                                                                "none",
                                                            }}
                                                          >
                                                            Open seven steven
                                                            Preview
                                                          </span>
                                                          <span
                                                            className="slds-assistive-text"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              margin: "-1px",
                                                              border: "0px",
                                                              padding: "0px",
                                                              overflow:
                                                                "hidden",
                                                              whiteSpace:
                                                                "nowrap",
                                                              position:
                                                                "absolute",
                                                              width: "1px",
                                                              height: "1px",
                                                              clip: "rect(0px, 0px, 0px, 0px)",
                                                              textTransform:
                                                                "none",
                                                            }}
                                                          >
                                                            Open seven steven
                                                            Preview
                                                          </span>
                                                        </button>
                                                      </lightning-button-icon>
                                                    </div>
                                                  </records-hoverable-link>
                                                </div>
                                              </force-lookup>
                                            </span>
                                            <lightning-button-icon
                                              className="slds-shrink-none change-owner-trigger"
                                              size="medium"
                                              style={{
                                                boxSizing: "border-box",
                                                flexShrink: 0,
                                              }}
                                            >
                                              <button
                                                className="slds-button slds-button_icon slds-button_icon-bare"
                                                type="button"
                                                title="Change Owner"
                                                style={{
                                                  boxSizing: "border-box",
                                                  font: "inherit",
                                                  margin: "0px",
                                                  overflow: "visible",
                                                  textTransform: "none",
                                                  cursor: "pointer",
                                                  backgroundPosition: "initial",
                                                  borderColor:
                                                    "var(--slds-c-button-color-border, var(--sds-c-button-color-border, transparent))",
                                                  borderStyle: "solid",
                                                  borderWidth:
                                                    "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                  borderRadius:
                                                    "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                  textDecoration: "none",
                                                  whiteSpace: "normal",
                                                  position: "relative",
                                                  display: "inline-flex",
                                                  alignItems: "center",
                                                  paddingTop:
                                                    "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                  paddingRight:
                                                    "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                  paddingBottom:
                                                    "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                  paddingLeft:
                                                    "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                  backgroundImage: "none",
                                                  backgroundSize: "initial",
                                                  backgroundRepeat: "initial",
                                                  backgroundAttachment:
                                                    "initial",
                                                  backgroundOrigin: "initial",
                                                  backgroundColor:
                                                    "var(--slds-c-button-color-background, var(--sds-c-button-color-background, transparent))",
                                                  backgroundClip: "border-box",
                                                  boxShadow:
                                                    "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                  appearance: "none",
                                                  userSelect: "none",
                                                  lineHeight:
                                                    "var(--lwc-lineHeightReset,1)",
                                                  verticalAlign: "middle",
                                                  justifyContent: "center",
                                                  color:
                                                    "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                  flexShrink: 0,
                                                }}
                                              >
                                                <lightning-primitive-icon
                                                  style={{
                                                    boxSizing: "border-box",
                                                  }}
                                                >
                                                  <svg
                                                    className="slds-button__icon slds-button__icon_hint"
                                                    aria-hidden="true"
                                                    focusable="false"
                                                    viewBox="0 0 520 520"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      verticalAlign: "middle",
                                                      width:
                                                        "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                      height:
                                                        "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                      fill: "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefaultHint,rgb(174, 174, 174)))",
                                                      overflow: "hidden",
                                                    }}
                                                  >
                                                    <g
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <path
                                                        d="M273 376c-30-12-35-23-35-35s8-23 18-32a82 82 0 0026-64c0-47-29-85-83-85s-83 38-83 85c0 25 8 49 26 64 10 9 18 20 18 32s-5 23-35 35c-44 18-86 38-87 76 2 26 22 48 47 48h230c25 0 45-22 45-47-1-38-43-59-87-77zm172-186c0-74-61-135-135-135V20l-68 55c-3 3-2 8 1 11l67 54v-35c47 0 85 38 85 85h-35l55 68c3 3 8 3 11 0l54-68z"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      />
                                                    </g>
                                                  </svg>
                                                </lightning-primitive-icon>
                                                <span
                                                  className="slds-assistive-text"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    margin: "-1px",
                                                    border: "0px",
                                                    padding: "0px",
                                                    overflow: "hidden",
                                                    whiteSpace: "nowrap",
                                                    position: "absolute",
                                                    width: "1px",
                                                    height: "1px",
                                                    clip: "rect(0px, 0px, 0px, 0px)",
                                                    textTransform: "none",
                                                  }}
                                                >
                                                  Change Owner
                                                </span>
                                              </button>
                                            </lightning-button-icon>
                                          </div>
                                        </force-owner-lookup>
                                      </slot>
                                    </div>
                                  </div>
                                </records-highlights-details-item>
                              </slot>
                            </div>
                          </div>
                          <div
                            className="proxy"
                            style={{
                              boxSizing: "border-box",
                              height: "139.5px",
                            }}
                          />
                        </records-highlights2>
                      </forcegenerated-highlightspanel_opportunity___012000000000000aaa___compact___view___recordlayout2>
                    </records-lwc-record-layout>
                  </records-lwc-highlights-panel>
                </slot>
              </flexipage-component2>
            </slot>
          </div>
          <div
            className="slds-col slds-size_1-of-1 row region-subheader"
            style={{
              boxSizing: "border-box",
              width: "100%",
              flex: "0 0 auto",
              margin: "0px auto",
              marginTop: "0.75rem",
            }}
          >
            <slot name="subheader" style={{ boxSizing: "border-box" }}>
              <flexipage-component2
                style={{
                  boxSizing: "border-box",
                  display: "block",
                  gridColumnStart:
                    "var(--flexipage-fieldsection-column-index, auto)",
                }}
              >
                <slot style={{ boxSizing: "border-box" }}>
                  <flexipage-aura-wrapper style={{ boxSizing: "border-box" }}>
                    <div style={{ boxSizing: "border-box" }}>
                      <div
                        className="pathOriginal runtime_sales_pathassistantPathAssistant"
                        style={{ boxSizing: "border-box" }}
                      >
                        <article
                          className="slds-card"
                          style={{
                            boxSizing: "border-box",
                            display: "block",
                            borderWidth:
                              "var(--slds-c-card-sizing-border, var(--sds-c-card-sizing-border, var(--lwc-borderWidthThin,1px)))",
                            borderStyle: "solid",
                            borderColor:
                              "var(--slds-c-card-color-border, var(--sds-c-card-color-border, var(--slds-g-color-border-base-1, var(--lwc-cardColorBorder,rgb(201, 201, 201)))))",
                            borderRadius:
                              "var(--slds-c-card-radius-border, var(--sds-c-card-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                            position: "relative",
                            paddingTop:
                              "var(--slds-c-card-spacing-blockstart, var(--slds-c-card-spacing-block-start, var(--sds-c-card-spacing-block-start, var(--sds-c-card-spacing-block, 0))))",
                            paddingRight: "10px",
                            paddingBottom:
                              "var(--slds-c-card-spacing-blockend, var(--slds-c-card-spacing-block-end, var(--sds-c-card-spacing-block-end, var(--sds-c-card-spacing-block, 0))))",
                            paddingLeft: "10px",
                            backgroundImage: "",
                            backgroundPositionX: "",
                            backgroundPositionY: "",
                            backgroundSize: "",
                            backgroundRepeat: "",
                            backgroundAttachment: "",
                            backgroundOrigin: "",
                            backgroundColor: "",
                            backgroundClip: "padding-box",
                            boxShadow:
                              "var(--slds-c-card-shadow, var(--sds-c-card-shadow, var(--lwc-cardShadow,0 2px 2px 0 rgba(0, 0, 0, 0.10))))",
                            color:
                              "var(--slds-c-card-text-color, var(--sds-c-card-text-color))",
                          }}
                        >
                          <div
                            className="slds-card__body slds-card__body_inner"
                            style={{
                              boxSizing: "border-box",
                              marginTop:
                                "var(--slds-c-card-body-spacing-blockstart, var(--slds-c-card-body-spacing-block-start, var(--sds-c-card-body-spacing-block-start, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                              marginBottom:
                                "var(--slds-c-card-body-spacing-blockend, var(--slds-c-card-body-spacing-block-end, var(--sds-c-card-body-spacing-block-end, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                              paddingRight:
                                "var(--slds-c-card-body-spacing-inlineend, var(--slds-c-card-body-spacing-inline-end, var(--sds-c-card-body-spacing-inline-end, var(--slds-c-card-body-spacing-inline, var(--sds-c-card-body-spacing-inline)))))",
                              paddingLeft:
                                "var(--slds-c-card-body-spacing-inlinestart, var(--slds-c-card-body-spacing-inline-start, var(--sds-c-card-body-spacing-inline-start, var(--slds-c-card-body-spacing-inline, var(--sds-c-card-body-spacing-inline)))))",
                              paddingTop: "0px",
                              paddingBottom: "0px",
                            }}
                          >
                            <h2
                              className="slds-assistive-text"
                              style={{
                                boxSizing: "border-box",
                                fontWeight: "inherit",
                                fontSize: "1em",
                                margin: "-1px",
                                border: "0px",
                                padding: "0px",
                                overflow: "hidden",
                                whiteSpace: "nowrap",
                                position: "absolute",
                                width: "1px",
                                height: "1px",
                                clip: "rect(0px, 0px, 0px, 0px)",
                                textTransform: "none",
                              }}
                            >
                              Path
                            </h2>
                            <div
                              className="slds-path"
                              style={{ boxSizing: "border-box" }}
                            >
                              <div
                                id="1935:0"
                                className="runtime_sales_pathassistantCollapsibleDrawer"
                                style={{ boxSizing: "border-box" }}
                              >
                                <div
                                  className="slds-grid slds-path__track"
                                  style={{
                                    boxSizing: "border-box",
                                    flexWrap: "nowrap",
                                    alignItems: "stretch",
                                    display: "flex",
                                  }}
                                >
                                  <div
                                    className="slds-grid slds-path__scroller-container runtime_sales_pathassistantPathAssistantTabSet"
                                    style={{
                                      boxSizing: "border-box",
                                      flex: "1 1 100%",
                                      overflow: "hidden",
                                      display: "flex",
                                      minWidth: "0px",
                                      flexBasis: "1px",
                                    }}
                                  >
                                    <div
                                      className="slds-path__scroller uiScroller scroller-wrapper scroll-horizontal"
                                      tabIndex="-1"
                                      style={{
                                        boxSizing: "border-box",
                                        position: "relative",
                                        width: "100%",
                                        transform: "translateZ(0px)",
                                        flex: "1 1 0%",
                                        overflow: "hidden",
                                        maxWidth: "100%",
                                        height: "auto",
                                      }}
                                    >
                                      <div
                                        className="slds-path__scroller_inner scroller"
                                        style={{
                                          boxSizing: "border-box",
                                          WebkitTapHighlightColor:
                                            "var(--slds-g-color-neutral-base-10, rgba(0,0,0,0))",
                                          transform: "translateZ(0px)",
                                          userSelect: "none",
                                          textSizeAdjust: "none",
                                          height: "100%",
                                          position: "static",
                                          minWidth: "100%",
                                        }}
                                      >
                                        <div
                                          style={{ boxSizing: "border-box" }}
                                        >
                                          <ul
                                            className="slds-path__nav"
                                            aria-label="Path Options"
                                            aria-orientation="horizontal"
                                            role="listbox"
                                            style={{
                                              boxSizing: "border-box",
                                              margin: "0px",
                                              padding: "0px",
                                              listStyle: "none",
                                              overflow: "hidden",
                                              display: "flex",
                                              alignItems: "flex-start",
                                            }}
                                          >
                                            <li
                                              className="slds-is-complete slds-path__item runtime_sales_pathassistantPathAssistantTab"
                                              role="presentation"
                                              style={{
                                                boxSizing: "border-box",
                                                flex: "1 1 0%",
                                                position: "relative",
                                                marginRight: "0.4375rem",
                                                minWidth: "5rem",
                                                textAlign: "center",
                                                background:
                                                  "var(--lwc-colorBackgroundPathComplete,rgb(59, 167, 85))",
                                                marginLeft: "0px",
                                                borderTopLeftRadius:
                                                  "var(--lwc-heightSalesPath,2rem)",
                                                borderBottomLeftRadius:
                                                  "var(--lwc-heightSalesPath,2rem)",
                                                paddingLeft: "0.625rem",
                                              }}
                                            >
                                              <a
                                                className="tabHeader slds-path__link"
                                                aria-current="false"
                                                aria-selected="false"
                                                href="#"
                                                role="option"
                                                tabIndex="-1"
                                                title="Prospecting"
                                                style={{
                                                  boxSizing: "border-box",
                                                  backgroundColor:
                                                    "transparent",
                                                  transition:
                                                    "color 0.1s linear",
                                                  padding:
                                                    "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXxSmall,0.25rem) var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXSmall,0.5rem)",
                                                  textDecoration: "none",
                                                  position: "relative",
                                                  display: "flex",
                                                  justifyContent: "center",
                                                  alignItems: "center",
                                                  height:
                                                    "var(--lwc-heightSalesPath,2rem)",
                                                  zIndex: 5,
                                                  cursor: "pointer",
                                                  color:
                                                    "var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))",
                                                }}
                                              >
                                                <span
                                                  className="complete slds-path__stage"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    position: "absolute",
                                                    top: "50%",
                                                    left: "50%",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                    transform:
                                                      "translate(-50%, -50%) rotateX(0deg)",
                                                  }}
                                                >
                                                  <lightning-icon
                                                    className="checkmark slds-icon-utility-check slds-icon_container"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      borderRadius:
                                                        "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                      display: "inline-block",
                                                      lineHeight:
                                                        "var(--lwc-lineHeightReset,1)",
                                                      backgroundColor:
                                                        "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                                                    }}
                                                  >
                                                    <span
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <lightning-primitive-icon
                                                        size="xx-small"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <svg
                                                          className="slds-icon slds-icon_xx-small"
                                                          aria-hidden="true"
                                                          focusable="false"
                                                          viewBox="0 0 520 520"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            verticalAlign:
                                                              "middle",
                                                            fill: "var(--slds-c-icon-color-foreground, var(--sds-c-icon-color-foreground, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextIconInverse,rgb(255, 255, 255)))))",
                                                            width:
                                                              "var(--lwc-squareIconXxSmallContent,.875rem)",
                                                            height:
                                                              "var(--lwc-squareIconXxSmallContent,.875rem)",
                                                            lineHeight:
                                                              "var(--lwc-lineHeightReset,1)",
                                                            overflow: "hidden",
                                                          }}
                                                        >
                                                          <g
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                            }}
                                                          >
                                                            <path
                                                              d="M191 425L26 259c-6-6-6-16 0-22l22-22c6-6 16-6 22 0l124 125a10 10 0 0015 0L452 95c6-6 16-6 22 0l22 22c6 6 6 16 0 22L213 425c-6 7-16 7-22 0z"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                              }}
                                                            />
                                                          </g>
                                                        </svg>
                                                      </lightning-primitive-icon>
                                                      <span
                                                        className="slds-assistive-text"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          margin: "-1px",
                                                          border: "0px",
                                                          padding: "0px",
                                                          overflow: "hidden",
                                                          whiteSpace: "nowrap",
                                                          position: "absolute",
                                                          width: "1px",
                                                          height: "1px",
                                                          clip: "rect(0px, 0px, 0px, 0px)",
                                                          textTransform: "none",
                                                        }}
                                                      >
                                                        stage complete
                                                      </span>
                                                    </span>
                                                  </lightning-icon>
                                                </span>
                                                <span
                                                  className="title slds-path__title"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    overflow: "hidden",
                                                    whiteSpace: "nowrap",
                                                    maxWidth: "100%",
                                                    textOverflow: "ellipsis",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                    transform:
                                                      "rotateX(180deg)",
                                                  }}
                                                >
                                                  Prospecting
                                                </span>
                                              </a>
                                            </li>
                                            <li
                                              className="slds-is-current slds-is-active slds-path__item runtime_sales_pathassistantPathAssistantTab"
                                              role="presentation"
                                              style={{
                                                boxSizing: "border-box",
                                                flex: "1 1 0%",
                                                position: "relative",
                                                marginLeft: "0.375rem",
                                                marginRight: "0.4375rem",
                                                minWidth: "5rem",
                                                textAlign: "center",
                                                background:
                                                  "var(--lwc-colorBackgroundPathActive,rgb(1, 68, 134))",
                                                backgroundColor: "",
                                              }}
                                            >
                                              <a
                                                className="tabHeader slds-path__link"
                                                aria-current="true"
                                                aria-selected="true"
                                                href="#"
                                                role="option"
                                                tabIndex="0"
                                                title="Qualification"
                                                style={{
                                                  boxSizing: "border-box",
                                                  backgroundColor:
                                                    "transparent",
                                                  transition:
                                                    "color 0.1s linear",
                                                  padding:
                                                    "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXxSmall,0.25rem) var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXSmall,0.5rem)",
                                                  textDecoration: "none",
                                                  position: "relative",
                                                  display: "flex",
                                                  justifyContent: "center",
                                                  alignItems: "center",
                                                  height:
                                                    "var(--lwc-heightSalesPath,2rem)",
                                                  zIndex: 5,
                                                  cursor: "pointer",
                                                  color:
                                                    "var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))",
                                                }}
                                              >
                                                <span
                                                  className="current slds-path__stage"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    position: "absolute",
                                                    top: "50%",
                                                    left: "50%",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                    transform:
                                                      "translateY(-50%) rotateX(-180deg)",
                                                  }}
                                                />
                                                <span
                                                  className="title slds-path__title"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    overflow: "hidden",
                                                    whiteSpace: "nowrap",
                                                    maxWidth: "100%",
                                                    textOverflow: "ellipsis",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                    transform: "rotateX(0deg)",
                                                  }}
                                                >
                                                  Qualification
                                                </span>
                                              </a>
                                            </li>
                                            <li
                                              className="slds-is-incomplete slds-path__item runtime_sales_pathassistantPathAssistantTab"
                                              role="presentation"
                                              style={{
                                                boxSizing: "border-box",
                                                flex: "1 1 0%",
                                                position: "relative",
                                                marginLeft: "0.375rem",
                                                marginRight: "0.4375rem",
                                                minWidth: "5rem",
                                                textAlign: "center",
                                                background:
                                                  "var(--slds-g-color-neutral-base-95, var(--lwc-colorBackgroundPathIncomplete,rgb(243, 243, 243)))",
                                              }}
                                            >
                                              <a
                                                className="tabHeader slds-path__link"
                                                aria-current="false"
                                                aria-selected="false"
                                                href="#"
                                                role="option"
                                                tabIndex="-1"
                                                title="Needs Analysis"
                                                style={{
                                                  boxSizing: "border-box",
                                                  backgroundColor:
                                                    "transparent",
                                                  transition:
                                                    "color 0.1s linear",
                                                  padding:
                                                    "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXxSmall,0.25rem) var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXSmall,0.5rem)",
                                                  textDecoration: "none",
                                                  position: "relative",
                                                  display: "flex",
                                                  justifyContent: "center",
                                                  alignItems: "center",
                                                  height:
                                                    "var(--lwc-heightSalesPath,2rem)",
                                                  zIndex: 5,
                                                  cursor: "pointer",
                                                  color:
                                                    "var(--slds-g-color-neutral-base-10, var(--lwc-colorTextDefault,rgb(24, 24, 24)))",
                                                }}
                                              >
                                                <span
                                                  className="ahead slds-path__stage"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    position: "absolute",
                                                    top: "50%",
                                                    left: "50%",
                                                    transform:
                                                      "translate(-50%, -50%) rotateX(-180deg)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                />
                                                <span
                                                  className="title slds-path__title"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    overflow: "hidden",
                                                    whiteSpace: "nowrap",
                                                    maxWidth: "100%",
                                                    textOverflow: "ellipsis",
                                                    transform:
                                                      "translate3d(0px, 0px, 0px)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                >
                                                  Needs Analysis
                                                </span>
                                              </a>
                                            </li>
                                            <li
                                              className="slds-is-incomplete slds-path__item runtime_sales_pathassistantPathAssistantTab"
                                              role="presentation"
                                              style={{
                                                boxSizing: "border-box",
                                                flex: "1 1 0%",
                                                position: "relative",
                                                marginLeft: "0.375rem",
                                                marginRight: "0.4375rem",
                                                minWidth: "5rem",
                                                textAlign: "center",
                                                background:
                                                  "var(--slds-g-color-neutral-base-95, var(--lwc-colorBackgroundPathIncomplete,rgb(243, 243, 243)))",
                                              }}
                                            >
                                              <a
                                                className="tabHeader slds-path__link"
                                                aria-current="false"
                                                aria-selected="false"
                                                href="#"
                                                role="option"
                                                tabIndex="-1"
                                                title="Value Proposition"
                                                style={{
                                                  boxSizing: "border-box",
                                                  backgroundColor:
                                                    "transparent",
                                                  transition:
                                                    "color 0.1s linear",
                                                  padding:
                                                    "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXxSmall,0.25rem) var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXSmall,0.5rem)",
                                                  textDecoration: "none",
                                                  position: "relative",
                                                  display: "flex",
                                                  justifyContent: "center",
                                                  alignItems: "center",
                                                  height:
                                                    "var(--lwc-heightSalesPath,2rem)",
                                                  zIndex: 5,
                                                  cursor: "pointer",
                                                  color:
                                                    "var(--slds-g-color-neutral-base-10, var(--lwc-colorTextDefault,rgb(24, 24, 24)))",
                                                }}
                                              >
                                                <span
                                                  className="ahead slds-path__stage"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    position: "absolute",
                                                    top: "50%",
                                                    left: "50%",
                                                    transform:
                                                      "translate(-50%, -50%) rotateX(-180deg)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                />
                                                <span
                                                  className="title slds-path__title"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    overflow: "hidden",
                                                    whiteSpace: "nowrap",
                                                    maxWidth: "100%",
                                                    textOverflow: "ellipsis",
                                                    transform:
                                                      "translate3d(0px, 0px, 0px)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                >
                                                  Value Proposition
                                                </span>
                                              </a>
                                            </li>
                                            <li
                                              className="slds-is-incomplete slds-path__item runtime_sales_pathassistantPathAssistantTab"
                                              role="presentation"
                                              style={{
                                                boxSizing: "border-box",
                                                flex: "1 1 0%",
                                                position: "relative",
                                                marginLeft: "0.375rem",
                                                marginRight: "0.4375rem",
                                                minWidth: "5rem",
                                                textAlign: "center",
                                                background:
                                                  "var(--slds-g-color-neutral-base-95, var(--lwc-colorBackgroundPathIncomplete,rgb(243, 243, 243)))",
                                              }}
                                            >
                                              <a
                                                className="tabHeader slds-path__link"
                                                aria-current="false"
                                                aria-selected="false"
                                                href="#"
                                                role="option"
                                                tabIndex="-1"
                                                title="Id. Decision Makers"
                                                style={{
                                                  boxSizing: "border-box",
                                                  backgroundColor:
                                                    "transparent",
                                                  transition:
                                                    "color 0.1s linear",
                                                  padding:
                                                    "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXxSmall,0.25rem) var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXSmall,0.5rem)",
                                                  textDecoration: "none",
                                                  position: "relative",
                                                  display: "flex",
                                                  justifyContent: "center",
                                                  alignItems: "center",
                                                  height:
                                                    "var(--lwc-heightSalesPath,2rem)",
                                                  zIndex: 5,
                                                  cursor: "pointer",
                                                  color:
                                                    "var(--slds-g-color-neutral-base-10, var(--lwc-colorTextDefault,rgb(24, 24, 24)))",
                                                }}
                                              >
                                                <span
                                                  className="ahead slds-path__stage"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    position: "absolute",
                                                    top: "50%",
                                                    left: "50%",
                                                    transform:
                                                      "translate(-50%, -50%) rotateX(-180deg)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                />
                                                <span
                                                  className="title slds-path__title"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    overflow: "hidden",
                                                    whiteSpace: "nowrap",
                                                    maxWidth: "100%",
                                                    textOverflow: "ellipsis",
                                                    transform:
                                                      "translate3d(0px, 0px, 0px)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                >
                                                  Id. Decision Makers
                                                </span>
                                              </a>
                                            </li>
                                            <li
                                              className="slds-is-incomplete slds-path__item runtime_sales_pathassistantPathAssistantTab"
                                              role="presentation"
                                              style={{
                                                boxSizing: "border-box",
                                                flex: "1 1 0%",
                                                position: "relative",
                                                marginLeft: "0.375rem",
                                                marginRight: "0.4375rem",
                                                minWidth: "5rem",
                                                textAlign: "center",
                                                background:
                                                  "var(--slds-g-color-neutral-base-95, var(--lwc-colorBackgroundPathIncomplete,rgb(243, 243, 243)))",
                                              }}
                                            >
                                              <a
                                                className="tabHeader slds-path__link"
                                                aria-current="false"
                                                aria-selected="false"
                                                href="#"
                                                role="option"
                                                tabIndex="-1"
                                                title="Perception Analysis"
                                                style={{
                                                  boxSizing: "border-box",
                                                  backgroundColor:
                                                    "transparent",
                                                  transition:
                                                    "color 0.1s linear",
                                                  padding:
                                                    "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXxSmall,0.25rem) var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXSmall,0.5rem)",
                                                  textDecoration: "none",
                                                  position: "relative",
                                                  display: "flex",
                                                  justifyContent: "center",
                                                  alignItems: "center",
                                                  height:
                                                    "var(--lwc-heightSalesPath,2rem)",
                                                  zIndex: 5,
                                                  cursor: "pointer",
                                                  color:
                                                    "var(--slds-g-color-neutral-base-10, var(--lwc-colorTextDefault,rgb(24, 24, 24)))",
                                                }}
                                              >
                                                <span
                                                  className="ahead slds-path__stage"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    position: "absolute",
                                                    top: "50%",
                                                    left: "50%",
                                                    transform:
                                                      "translate(-50%, -50%) rotateX(-180deg)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                />
                                                <span
                                                  className="title slds-path__title"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    overflow: "hidden",
                                                    whiteSpace: "nowrap",
                                                    maxWidth: "100%",
                                                    textOverflow: "ellipsis",
                                                    transform:
                                                      "translate3d(0px, 0px, 0px)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                >
                                                  Perception Analysis
                                                </span>
                                              </a>
                                            </li>
                                            <li
                                              className="slds-is-incomplete slds-path__item runtime_sales_pathassistantPathAssistantTab"
                                              role="presentation"
                                              style={{
                                                boxSizing: "border-box",
                                                flex: "1 1 0%",
                                                position: "relative",
                                                marginLeft: "0.375rem",
                                                marginRight: "0.4375rem",
                                                minWidth: "5rem",
                                                textAlign: "center",
                                                background:
                                                  "var(--slds-g-color-neutral-base-95, var(--lwc-colorBackgroundPathIncomplete,rgb(243, 243, 243)))",
                                              }}
                                            >
                                              <a
                                                className="tabHeader slds-path__link"
                                                aria-current="false"
                                                aria-selected="false"
                                                href="#"
                                                role="option"
                                                tabIndex="-1"
                                                title="Proposal/Price Quote"
                                                style={{
                                                  boxSizing: "border-box",
                                                  backgroundColor:
                                                    "transparent",
                                                  transition:
                                                    "color 0.1s linear",
                                                  padding:
                                                    "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXxSmall,0.25rem) var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXSmall,0.5rem)",
                                                  textDecoration: "none",
                                                  position: "relative",
                                                  display: "flex",
                                                  justifyContent: "center",
                                                  alignItems: "center",
                                                  height:
                                                    "var(--lwc-heightSalesPath,2rem)",
                                                  zIndex: 5,
                                                  cursor: "pointer",
                                                  color:
                                                    "var(--slds-g-color-neutral-base-10, var(--lwc-colorTextDefault,rgb(24, 24, 24)))",
                                                }}
                                              >
                                                <span
                                                  className="ahead slds-path__stage"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    position: "absolute",
                                                    top: "50%",
                                                    left: "50%",
                                                    transform:
                                                      "translate(-50%, -50%) rotateX(-180deg)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                />
                                                <span
                                                  className="title slds-path__title"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    overflow: "hidden",
                                                    whiteSpace: "nowrap",
                                                    maxWidth: "100%",
                                                    textOverflow: "ellipsis",
                                                    transform:
                                                      "translate3d(0px, 0px, 0px)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                >
                                                  Proposal/Price Quote
                                                </span>
                                              </a>
                                            </li>
                                            <li
                                              className="slds-is-incomplete slds-path__item runtime_sales_pathassistantPathAssistantTab"
                                              role="presentation"
                                              style={{
                                                boxSizing: "border-box",
                                                flex: "1 1 0%",
                                                position: "relative",
                                                marginLeft: "0.375rem",
                                                marginRight: "0.4375rem",
                                                minWidth: "5rem",
                                                textAlign: "center",
                                                background:
                                                  "var(--slds-g-color-neutral-base-95, var(--lwc-colorBackgroundPathIncomplete,rgb(243, 243, 243)))",
                                              }}
                                            >
                                              <a
                                                className="tabHeader slds-path__link"
                                                aria-current="false"
                                                aria-selected="false"
                                                href="#"
                                                role="option"
                                                tabIndex="-1"
                                                title="Negotiation/Review"
                                                style={{
                                                  boxSizing: "border-box",
                                                  backgroundColor:
                                                    "transparent",
                                                  transition:
                                                    "color 0.1s linear",
                                                  padding:
                                                    "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXxSmall,0.25rem) var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXSmall,0.5rem)",
                                                  textDecoration: "none",
                                                  position: "relative",
                                                  display: "flex",
                                                  justifyContent: "center",
                                                  alignItems: "center",
                                                  height:
                                                    "var(--lwc-heightSalesPath,2rem)",
                                                  zIndex: 5,
                                                  cursor: "pointer",
                                                  color:
                                                    "var(--slds-g-color-neutral-base-10, var(--lwc-colorTextDefault,rgb(24, 24, 24)))",
                                                }}
                                              >
                                                <span
                                                  className="ahead slds-path__stage"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    position: "absolute",
                                                    top: "50%",
                                                    left: "50%",
                                                    transform:
                                                      "translate(-50%, -50%) rotateX(-180deg)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                />
                                                <span
                                                  className="title slds-path__title"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    overflow: "hidden",
                                                    whiteSpace: "nowrap",
                                                    maxWidth: "100%",
                                                    textOverflow: "ellipsis",
                                                    transform:
                                                      "translate3d(0px, 0px, 0px)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                >
                                                  Negotiation/Review
                                                </span>
                                              </a>
                                            </li>
                                            <li
                                              className="slds-is-incomplete slds-path__item runtime_sales_pathassistantPathAssistantTab"
                                              role="presentation"
                                              style={{
                                                boxSizing: "border-box",
                                                flex: "1 1 0%",
                                                position: "relative",
                                                marginLeft: "0.375rem",
                                                minWidth: "5rem",
                                                textAlign: "center",
                                                background:
                                                  "var(--slds-g-color-neutral-base-95, var(--lwc-colorBackgroundPathIncomplete,rgb(243, 243, 243)))",
                                                marginRight: "0px",
                                                borderTopRightRadius:
                                                  "var(--lwc-heightSalesPath,2rem)",
                                                borderBottomRightRadius:
                                                  "var(--lwc-heightSalesPath,2rem)",
                                                paddingRight: "0.625rem",
                                              }}
                                            >
                                              <a
                                                className="tabHeader slds-path__link"
                                                aria-current="false"
                                                aria-selected="false"
                                                href="#"
                                                role="option"
                                                tabIndex="-1"
                                                title="Closed"
                                                style={{
                                                  boxSizing: "border-box",
                                                  backgroundColor:
                                                    "transparent",
                                                  transition:
                                                    "color 0.1s linear",
                                                  padding:
                                                    "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXxSmall,0.25rem) var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingXSmall,0.5rem)",
                                                  textDecoration: "none",
                                                  position: "relative",
                                                  display: "flex",
                                                  justifyContent: "center",
                                                  alignItems: "center",
                                                  height:
                                                    "var(--lwc-heightSalesPath,2rem)",
                                                  zIndex: 5,
                                                  cursor: "pointer",
                                                  color:
                                                    "var(--slds-g-color-neutral-base-10, var(--lwc-colorTextDefault,rgb(24, 24, 24)))",
                                                  paddingLeft:
                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                }}
                                              >
                                                <span
                                                  className="ahead slds-path__stage"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    position: "absolute",
                                                    top: "50%",
                                                    left: "50%",
                                                    transform:
                                                      "translate(-50%, -50%) rotateX(-180deg)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                />
                                                <span
                                                  className="title slds-path__title"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    overflow: "hidden",
                                                    whiteSpace: "nowrap",
                                                    maxWidth: "100%",
                                                    textOverflow: "ellipsis",
                                                    transform:
                                                      "translate3d(0px, 0px, 0px)",
                                                    transition:
                                                      "transform 0.2s linear",
                                                    display: "block",
                                                    backfaceVisibility:
                                                      "hidden",
                                                  }}
                                                >
                                                  Closed
                                                </span>
                                              </a>
                                            </li>
                                          </ul>
                                        </div>
                                      </div>
                                    </div>
                                    <div
                                      className="slds-path__scroll-controls"
                                      style={{
                                        boxSizing: "border-box",
                                        display: "none",
                                      }}
                                    />
                                    <div
                                      id="pa-tooltip"
                                      className="tooltip tooltip-hidden"
                                      aria-hidden="true"
                                      role="tooltip"
                                      style={{
                                        boxSizing: "border-box",
                                        transition: "opacity ease-out 0.25s",
                                        whiteSpace: "nowrap",
                                        position: "absolute",
                                        transform: "translate(-50%, 2.5rem)",
                                        zIndex: 3,
                                        WebkitFontSmoothing:
                                          "subpixel-antialiased",
                                        visibility: "hidden",
                                        opacity: 0,
                                        left: "1032px",
                                      }}
                                    >
                                      <div
                                        className="wrapper"
                                        style={{
                                          boxSizing: "border-box",
                                          borderRadius:
                                            "var(--lwc-borderRadiusMedium,0.25rem)",
                                          position: "absolute",
                                          transform: "translateX(-50%)",
                                          zIndex: 3,
                                          backgroundColor:
                                            "var(--slds-g-color-palette-blue-20, var(--lwc-colorBackgroundAltInverse,rgb(3, 45, 96)))",
                                          color:
                                            "var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))",
                                        }}
                                      >
                                        <span
                                          className="daysInStageString"
                                          style={{
                                            boxSizing: "border-box",
                                            margin:
                                              "var(--lwc-spacingXSmall,0.5rem) var(--lwc-spacingMedium,1rem)",
                                            display: "inline-block",
                                          }}
                                        >
                                          Negotiation/Review
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    className="slds-grid slds-path__action runtime_sales_pathassistantPathAssistantHeader"
                                    style={{
                                      boxSizing: "border-box",
                                      flex: "1 0 100%",
                                      maxWidth: "100%",
                                      justifyContent: "space-between",
                                      flexDirection: "row",
                                      flexBasis: "auto",
                                      flexGrow: 0,
                                      alignItems: "normal",
                                      marginTop: "0px",
                                      marginLeft:
                                        "var(--lwc-spacingLarge,1.5rem)",
                                      display: "flex",
                                    }}
                                  >
                                    <CompleteButton>
                                      <svg
                                        style={{
                                          width: "16px",
                                          height: "16px",
                                          fill: "white",
                                          marginRight: "8px",
                                        }}
                                        viewBox="0 0 24 24"
                                      >
                                        <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z" />
                                      </svg>
                                      Mark Stage as Complete
                                    </CompleteButton>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <lightning-message-context-consumer
                              style={{
                                boxSizing: "border-box",
                                width: "unset",
                                height: "unset",
                              }}
                            />
                          </div>
                        </article>
                      </div>
                    </div>
                  </flexipage-aura-wrapper>
                </slot>
              </flexipage-component2>
            </slot>
          </div>
          <div
            className="slds-grid slds-wrap slds-col slds-size_1-of-1 row row-main"
            style={{
              boxSizing: "border-box",
              display: "flex",
              flexWrap: "wrap",
              alignItems: "flex-start",
              width: "100%",
              flex: "0 0 auto",
              margin: "0.75rem auto",
            }}
          >
            <div
              className="slds-col slds-large-size_8-of-12 slds-medium-size_8-of-12 slds-size_1-of-1 column region-main"
              style={{
                boxSizing: "border-box",
                flex: "0 0 auto",
                width: "67%",
              }}
            >
              <slot name="main" style={{ boxSizing: "border-box" }}>
                <flexipage-component2
                  style={{
                    boxSizing: "border-box",
                    display: "block",
                    gridColumnStart:
                      "var(--flexipage-fieldsection-column-index, auto)",
                  }}
                >
                  <slot style={{ boxSizing: "border-box" }}>
                    <flexipage-tabset2 style={{ boxSizing: "border-box" }}>
                      <div
                        className="slds-tabs_card"
                        style={{
                          boxSizing: "border-box",
                          padding:
                            "var(--lwc-varSpacingVerticalSmall,0.75rem) var(--lwc-varSpacingHorizontalMedium,1rem)",
                          background:
                            "var(--slds-c-card-color-background, var(--sds-c-card-color-background, var(--slds-g-color-neutral-base-100, var(--lwc-cardColorBackground,rgb(255, 255, 255)))))",
                          borderWidth:
                            "var(--slds-c-card-sizing-border, var(--sds-c-card-sizing-border, var(--lwc-borderWidthThin,1px)))",
                          borderStyle: "solid",
                          borderColor:
                            "var(--slds-c-card-color-border, var(--sds-c-card-color-border, var(--slds-g-color-border-base-1, var(--lwc-cardColorBorder,rgb(201, 201, 201)))))",
                          borderRadius:
                            "var(--slds-c-card-radius-border, var(--sds-c-card-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                          boxShadow:
                            "var(--slds-c-card-shadow, var(--sds-c-card-shadow, var(--lwc-cardShadow,0 2px 2px 0 rgba(0, 0, 0, 0.10))))",
                        }}
                      >
                        <h2
                          className="slds-assistive-text"
                          style={{
                            boxSizing: "border-box",
                            fontWeight: "inherit",
                            fontSize: "1em",
                            margin: "-1px",
                            border: "0px",
                            padding: "0px",
                            overflow: "hidden",
                            whiteSpace: "nowrap",
                            position: "absolute",
                            width: "1px",
                            height: "1px",
                            clip: "rect(0px, 0px, 0px, 0px)",
                            textTransform: "none",
                          }}
                        >
                          Tabs
                        </h2>
                        <lightning-tabset
                          className="flexipage-tabset"
                          style={{ boxSizing: "border-box" }}
                        >
                          <div
                            className="slds-tabs_default"
                            style={{
                              boxSizing: "border-box",
                              display: "block",
                              width: "100%",
                            }}
                          >
                            <lightning-tab-bar
                              style={{ boxSizing: "border-box" }}
                            >
                              <ul
                                className="slds-tabs_default__nav"
                                aria-label="Tabs"
                                role="tablist"
                                style={{
                                  boxSizing: "border-box",
                                  margin: "0px",
                                  padding: "0px",
                                  listStyle: "none",
                                  display: "flex",
                                  alignItems: "flex-start",
                                  borderBottomWidth:
                                    "var(--slds-c-tabs-list-sizing-border, var(--sds-c-tabs-list-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                  borderBottomStyle: "solid",
                                  borderBottomColor:
                                    "var(--slds-c-tabs-list-color-border, var(--sds-c-tabs-list-color-border, var(--slds-g-color-border-base-4, var(--lwc-colorBorder,rgb(229, 229, 229)))))",
                                }}
                              >
                                <li
                                  className="slds-tabs_default__item slds-is-active"
                                  role="presentation"
                                  title="Activity"
                                  style={{
                                    boxSizing: "border-box",
                                    color:
                                      "var(--slds-c-tabs-item-text-color, var(--sds-c-tabs-item-text-color, var(--slds-g-color-neutral-base-30, var(--lwc-colorTextActionLabel,rgb(68, 68, 68)))))",
                                    position: "relative",
                                    paddingTop:
                                      "var(--slds-c-tabs-item-spacing-blockstart, var(--slds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block, 0))))",
                                    paddingRight:
                                      "var(--slds-c-tabs-item-spacing-inlineend, var(--slds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                                    paddingBottom:
                                      "var(--slds-c-tabs-item-spacing-blockend, var(--slds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block, 0))))",
                                    paddingLeft:
                                      "var(--slds-c-tabs-item-spacing-inlinestart, var(--slds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                                    marginBottom:
                                      "calc(var(--slds-c-tabs-list-sizing-border, var(--sds-c-tabs-list-sizing-border, var(--lwc-borderWidthThin,1px))) * -1)",
                                    fontSize: "var(--lwc-fontSize5,1rem)",
                                    textTransform: "capitalize",
                                    letterSpacing: "normal",
                                    maxWidth: "240px",
                                  }}
                                >
                                  <a
                                    id="activityTab__item"
                                    className="slds-tabs_default__link"
                                    aria-controls="tab-7"
                                    aria-selected="true"
                                    href="#"
                                    role="tab"
                                    tabIndex="0"
                                    style={{
                                      boxSizing: "border-box",
                                      backgroundColor: "transparent",
                                      transition: "color 0.1s linear",
                                      whiteSpace: "nowrap",
                                      textDecoration: "none",
                                      border: "0px",
                                      maxWidth: "100%",
                                      cursor: "pointer",
                                      height:
                                        "var(--slds-c-tabs-item-sizing-height, var(--sds-c-tabs-item-sizing-height, var(--lwc-lineHeightTab,2.5rem)))",
                                      lineHeight:
                                        "var(--slds-c-tabs-item-line-height, var(--sds-c-tabs-item-line-height, var(--lwc-lineHeightTab,2.5rem)))",
                                      color: "currentcolor",
                                      textTransform: "inherit",
                                      zIndex: 1,
                                      fontWeight:
                                        "var(--lwc-tabsFontWeight,700)",
                                      overflow: "hidden",
                                      paddingBottom:
                                        "var(--lwc-varSpacingVerticalXSmall,0.5rem)",
                                      display: "block",
                                      textOverflow: "ellipsis",
                                      textAlign: "center",
                                    }}
                                  >
                                    Activity
                                  </a>
                                </li>
                                <li
                                  className="slds-tabs_default__item"
                                  role="presentation"
                                  title="Details"
                                  style={{
                                    boxSizing: "border-box",
                                    color:
                                      "var(--slds-c-tabs-item-text-color, var(--sds-c-tabs-item-text-color, var(--slds-g-color-neutral-base-30, var(--lwc-colorTextActionLabel,rgb(68, 68, 68)))))",
                                    position: "relative",
                                    paddingTop:
                                      "var(--slds-c-tabs-item-spacing-blockstart, var(--slds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block, 0))))",
                                    paddingRight:
                                      "var(--slds-c-tabs-item-spacing-inlineend, var(--slds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                                    paddingBottom:
                                      "var(--slds-c-tabs-item-spacing-blockend, var(--slds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block, 0))))",
                                    paddingLeft:
                                      "var(--slds-c-tabs-item-spacing-inlinestart, var(--slds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                                    marginBottom:
                                      "calc(var(--slds-c-tabs-list-sizing-border, var(--sds-c-tabs-list-sizing-border, var(--lwc-borderWidthThin,1px))) * -1)",
                                    fontSize: "var(--lwc-fontSize5,1rem)",
                                    textTransform: "capitalize",
                                    letterSpacing: "normal",
                                    maxWidth: "240px",
                                    marginLeft:
                                      "var(--lwc-varSpacingHorizontalLarge,1.5rem)",
                                  }}
                                >
                                  <a
                                    id="detailTab__item"
                                    className="slds-tabs_default__link"
                                    aria-controls="tab-8"
                                    aria-selected="false"
                                    href="#"
                                    role="tab"
                                    tabIndex="-1"
                                    style={{
                                      boxSizing: "border-box",
                                      backgroundColor: "transparent",
                                      transition: "color 0.1s linear",
                                      whiteSpace: "nowrap",
                                      textDecoration: "none",
                                      border: "0px",
                                      maxWidth: "100%",
                                      cursor: "pointer",
                                      height:
                                        "var(--slds-c-tabs-item-sizing-height, var(--sds-c-tabs-item-sizing-height, var(--lwc-lineHeightTab,2.5rem)))",
                                      lineHeight:
                                        "var(--slds-c-tabs-item-line-height, var(--sds-c-tabs-item-line-height, var(--lwc-lineHeightTab,2.5rem)))",
                                      color: "currentcolor",
                                      textTransform: "inherit",
                                      zIndex: 1,
                                      overflow: "hidden",
                                      paddingBottom:
                                        "var(--lwc-varSpacingVerticalXSmall,0.5rem)",
                                      display: "block",
                                      textOverflow: "ellipsis",
                                      textAlign: "center",
                                    }}
                                  >
                                    Details
                                  </a>
                                </li>
                                <li
                                  className="slds-tabs_default__item"
                                  role="presentation"
                                  title="Chatter"
                                  style={{
                                    boxSizing: "border-box",
                                    color:
                                      "var(--slds-c-tabs-item-text-color, var(--sds-c-tabs-item-text-color, var(--slds-g-color-neutral-base-30, var(--lwc-colorTextActionLabel,rgb(68, 68, 68)))))",
                                    position: "relative",
                                    paddingTop:
                                      "var(--slds-c-tabs-item-spacing-blockstart, var(--slds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block, 0))))",
                                    paddingRight:
                                      "var(--slds-c-tabs-item-spacing-inlineend, var(--slds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                                    paddingBottom:
                                      "var(--slds-c-tabs-item-spacing-blockend, var(--slds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block, 0))))",
                                    paddingLeft:
                                      "var(--slds-c-tabs-item-spacing-inlinestart, var(--slds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                                    marginBottom:
                                      "calc(var(--slds-c-tabs-list-sizing-border, var(--sds-c-tabs-list-sizing-border, var(--lwc-borderWidthThin,1px))) * -1)",
                                    fontSize: "var(--lwc-fontSize5,1rem)",
                                    textTransform: "capitalize",
                                    letterSpacing: "normal",
                                    maxWidth: "240px",
                                    marginLeft:
                                      "var(--lwc-varSpacingHorizontalLarge,1.5rem)",
                                  }}
                                >
                                  <a
                                    id="collaborateTab__item"
                                    className="slds-tabs_default__link"
                                    aria-controls="tab-9"
                                    aria-selected="false"
                                    href="#"
                                    role="tab"
                                    tabIndex="-1"
                                    style={{
                                      boxSizing: "border-box",
                                      backgroundColor: "transparent",
                                      transition: "color 0.1s linear",
                                      whiteSpace: "nowrap",
                                      textDecoration: "none",
                                      border: "0px",
                                      maxWidth: "100%",
                                      cursor: "pointer",
                                      height:
                                        "var(--slds-c-tabs-item-sizing-height, var(--sds-c-tabs-item-sizing-height, var(--lwc-lineHeightTab,2.5rem)))",
                                      lineHeight:
                                        "var(--slds-c-tabs-item-line-height, var(--sds-c-tabs-item-line-height, var(--lwc-lineHeightTab,2.5rem)))",
                                      color: "currentcolor",
                                      textTransform: "inherit",
                                      zIndex: 1,
                                      overflow: "hidden",
                                      paddingBottom:
                                        "var(--lwc-varSpacingVerticalXSmall,0.5rem)",
                                      display: "block",
                                      textOverflow: "ellipsis",
                                      textAlign: "center",
                                    }}
                                  >
                                    Chatter
                                  </a>
                                </li>
                                <li
                                  className="slds-tabs_default__item slds-tabs_default__overflow-button"
                                  style={{
                                    boxSizing: "border-box",
                                    color:
                                      "var(--slds-c-tabs-item-text-color, var(--sds-c-tabs-item-text-color, var(--slds-g-color-neutral-base-30, var(--lwc-colorTextActionLabel,rgb(68, 68, 68)))))",
                                    position: "relative",
                                    paddingTop:
                                      "var(--slds-c-tabs-item-spacing-blockstart, var(--slds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block, 0))))",
                                    paddingRight:
                                      "var(--slds-c-tabs-item-spacing-inlineend, var(--slds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                                    paddingBottom:
                                      "var(--slds-c-tabs-item-spacing-blockend, var(--slds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block, 0))))",
                                    paddingLeft:
                                      "var(--slds-c-tabs-item-spacing-inlinestart, var(--slds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                                    marginBottom:
                                      "calc(var(--slds-c-tabs-list-sizing-border, var(--sds-c-tabs-list-sizing-border, var(--lwc-borderWidthThin,1px))) * -1)",
                                    display: "inline-flex",
                                    height:
                                      "var(--slds-c-tabs-item-sizing-height, var(--sds-c-tabs-item-sizing-height, var(--lwc-lineHeightTab,2.5rem)))",
                                    lineHeight:
                                      "var(--slds-c-tabs-item-line-height, var(--sds-c-tabs-item-line-height, var(--lwc-lineHeightTab,2.5rem)))",
                                    fontSize: "var(--lwc-fontSize5,1rem)",
                                    textTransform: "capitalize",
                                    letterSpacing: "normal",
                                    maxWidth: "240px",
                                    marginLeft:
                                      "var(--lwc-varSpacingHorizontalLarge,1.5rem)",
                                    visibility: "hidden",
                                  }}
                                >
                                  {/* Removed duplicate lightning-button-menu */}
                                  <button
                                    className="slds-button"
                                    type="button"
                                    aria-expanded="false"
                                    aria-haspopup="true"
                                    title="More Tabs"
                                    style={{
                                      boxSizing: "border-box",
                                      font: "inherit",
                                      margin: "0px",
                                      textTransform: "none",
                                      cursor: "pointer",
                                      backgroundPosition: "initial",
                                      borderColor:
                                        "var(--slds-c-button-color-border, var(--sds-c-button-color-border, transparent))",
                                      borderStyle: "solid",
                                      borderWidth:
                                        "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                      borderRadius:
                                        "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                      textDecoration: "none",
                                      whiteSpace: "normal",
                                      position: "relative",
                                      alignItems: "center",
                                      paddingTop:
                                        "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                      paddingRight:
                                        "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                      paddingLeft:
                                        "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                      backgroundImage: "none",
                                      backgroundSize: "initial",
                                      backgroundRepeat: "initial",
                                      backgroundAttachment: "initial",
                                      backgroundOrigin: "initial",
                                      backgroundColor:
                                        "var(--slds-c-button-color-background, var(--sds-c-button-color-background, transparent))",
                                      backgroundClip: "border-box",
                                      boxShadow:
                                        "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                      appearance: "none",
                                      userSelect: "none",
                                      lineHeight: "inherit",
                                      color:
                                        "var(--slds-c-tabs-item-text-color, var(--sds-c-tabs-item-text-color, var(--slds-g-color-neutral-base-30, var(--lwc-colorTextActionLabel,rgb(68, 68, 68)))))",
                                      overflow: "hidden",
                                      paddingBottom:
                                        "var(--lwc-varSpacingVerticalXSmall,0.5rem)",
                                      display: "block",
                                      textOverflow: "ellipsis",
                                      textAlign: "center",
                                    }}
                                  >
                                    More
                                    <lightning-primitive-icon
                                      style={{ boxSizing: "border-box" }}
                                    >
                                      <svg
                                        className="slds-button__icon slds-button__icon_right"
                                        aria-hidden="true"
                                        focusable="false"
                                        viewBox="0 0 520 520"
                                        style={{
                                          boxSizing: "border-box",
                                          verticalAlign: "middle",
                                          width:
                                            "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                          height:
                                            "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                          fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                          marginLeft:
                                            "var(--lwc-spacingXSmall,0.5rem)",
                                          overflow: "hidden",
                                        }}
                                      >
                                        <g style={{ boxSizing: "border-box" }}>
                                          <path
                                            d="M476 178L271 385c-6 6-16 6-22 0L44 178c-6-6-6-16 0-22l22-22c6-6 16-6 22 0l161 163c6 6 16 6 22 0l161-162c6-6 16-6 22 0l22 22c5 6 5 15 0 21z"
                                            style={{
                                              boxSizing: "border-box",
                                            }}
                                          />
                                        </g>
                                      </svg>
                                    </lightning-primitive-icon>
                                    <span
                                      id="button-label-374"
                                      className="slds-assistive-text"
                                      style={{
                                        boxSizing: "border-box",
                                        margin: "-1px",
                                        border: "0px",
                                        padding: "0px",
                                        overflow: "hidden",
                                        whiteSpace: "nowrap",
                                        position: "absolute",
                                        width: "1px",
                                        height: "1px",
                                        clip: "rect(0px, 0px, 0px, 0px)",
                                        textTransform: "none",
                                      }}
                                    >
                                      Tabs
                                    </span>
                                  </button>
                                  {/* End of removed lightning-button-menu */}
                                </li>
                              </ul>
                            </lightning-tab-bar>
                            <slot style={{ boxSizing: "border-box" }}>
                              <slot
                                name="tabs"
                                style={{ boxSizing: "border-box" }}
                              >
                                <flexipage-tab2
                                  id="tab-7"
                                  className="slds-tabs_default__content slds-show"
                                  aria-labelledby="activityTab__item"
                                  role="tabpanel"
                                  tabIndex="0"
                                  style={{
                                    boxSizing: "border-box",
                                    position: "relative",
                                    paddingRight:
                                      "var(--slds-c-tabs-panel-spacing-inlineend, var(--slds-c-tabs-panel-spacing-inline-end, var(--sds-c-tabs-panel-spacing-inline-end, var(--slds-c-tabs-panel-spacing-inline, var(--sds-c-tabs-panel-spacing-inline, 0)))))",
                                    paddingLeft:
                                      "var(--slds-c-tabs-panel-spacing-inlinestart, var(--slds-c-tabs-panel-spacing-inline-start, var(--sds-c-tabs-panel-spacing-inline-start, var(--slds-c-tabs-panel-spacing-inline, var(--sds-c-tabs-panel-spacing-inline, 0)))))",
                                    display: "block",
                                    paddingTop: "0.75rem",
                                    paddingBottom: "0px",
                                  }}
                                >
                                  <slot style={{ boxSizing: "border-box" }}>
                                    <flexipage-component2
                                      style={{
                                        boxSizing: "border-box",
                                        display: "block",
                                        gridColumnStart:
                                          "var(--flexipage-fieldsection-column-index, auto)",
                                      }}
                                    >
                                      <slot style={{ boxSizing: "border-box" }}>
                                        <flexipage-aura-wrapper
                                          style={{ boxSizing: "border-box" }}
                                        >
                                          <div
                                            style={{ boxSizing: "border-box" }}
                                          >
                                            <div
                                              className="slds-card-wrapper activityPanel"
                                              style={{
                                                boxSizing: "border-box",
                                                padding:
                                                  "var(--lwc-varSpacingMedium,1rem)",
                                                borderWidth:
                                                  "var(--slds-c-card-sizing-border, var(--sds-c-card-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                borderStyle: "solid",
                                                borderColor:
                                                  "var(--slds-c-card-color-border, var(--sds-c-card-color-border, var(--slds-g-color-border-base-1, var(--lwc-cardColorBorder,rgb(201, 201, 201)))))",
                                                borderRadius:
                                                  "var(--slds-c-card-radius-border, var(--sds-c-card-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                backgroundImage: "",
                                                backgroundPositionX: "",
                                                backgroundPositionY: "",
                                                backgroundSize: "",
                                                backgroundRepeat: "",
                                                backgroundAttachment: "",
                                                backgroundOrigin: "",
                                                backgroundColor: "",
                                                backgroundClip: "padding-box",
                                                border: "0px",
                                                boxShadow: "none",
                                                paddingLeft: "0px",
                                                paddingRight: "0px",
                                                paddingTop: "0px",
                                              }}
                                            >
                                              <h2
                                                className="slds-assistive-text"
                                                style={{
                                                  boxSizing: "border-box",
                                                  fontWeight: "inherit",
                                                  fontSize: "1em",
                                                  margin: "-1px",
                                                  border: "0px",
                                                  padding: "0px",
                                                  overflow: "hidden",
                                                  whiteSpace: "nowrap",
                                                  position: "absolute",
                                                  width: "1px",
                                                  height: "1px",
                                                  clip: "rect(0px, 0px, 0px, 0px)",
                                                  textTransform: "none",
                                                }}
                                              >
                                                Activity Publisher
                                              </h2>
                                              <runtime_sales_activities-activity-panel-composer
                                                className="slds-m-horizontal_xx-small"
                                                style={{
                                                  boxSizing: "border-box",
                                                  marginLeft:
                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                  marginRight:
                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                }}
                                              >
                                                <lightning-button-group
                                                  className="slds-m-vertical_xxx-small slds-button-group"
                                                  role="group"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    display: "inline-flex",
                                                    marginTop:
                                                      "var(--lwc-spacingXxxSmall,0.125rem)",
                                                    marginBottom:
                                                      "var(--lwc-spacingXxxSmall,0.125rem)",
                                                  }}
                                                >
                                                  <div
                                                    className="fix_button-group-flexbox"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      display: "inline-flex",
                                                    }}
                                                  >
                                                    <slot
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <div
                                                        style={{
                                                          display: "flex",
                                                        }}
                                                      >
                                                        <LightningButton
                                                          variant="neutral"
                                                          value="NewTask"
                                                          aria-label="New Task"
                                                          title="New Task"
                                                          borderColor="rgb(201, 201, 201)"
                                                          style={{
                                                            display:
                                                              "inline-flex",
                                                            alignItems:
                                                              "center",
                                                            borderTopRightRadius: 0,
                                                            borderBottomRightRadius: 0,
                                                            borderRadius:
                                                              "0.25rem 0 0 0.25rem",
                                                          }}
                                                        >
                                                          <div
                                                            className="slds-icon-standard-task slds-icon_container"
                                                            style={{
                                                              borderRadius:
                                                                "0.25rem",
                                                              display:
                                                                "inline-block",
                                                              lineHeight: "1",
                                                              backgroundColor:
                                                                "#3ba755",
                                                              marginRight:
                                                                "0.5rem",
                                                            }}
                                                          >
                                                            <svg
                                                              className="slds-icon slds-icon_small"
                                                              aria-hidden="true"
                                                              focusable="false"
                                                              viewBox="0 0 1000 1000"
                                                              style={{
                                                                verticalAlign:
                                                                  "middle",
                                                                fill: "white",
                                                                width: "1.5rem",
                                                                height:
                                                                  "1.5rem",
                                                                lineHeight: "1",
                                                                overflow:
                                                                  "hidden",
                                                              }}
                                                            >
                                                              <g>
                                                                <path d="M466 237l-21-21c-6-6-15-6-21 0L292 348l-53-53c-6-6-15-6-21 0l-21 21c-6 6-6 15 0 21l74 74a29 29 0 0042 0l153-153c5-5 5-15 0-21zm304 143H510c-11 0-20-9-20-20v-40c0-11 9-20 20-20h260c11 0 20 9 20 20v40c0 11-9 20-20 20zm0 180H450c-11 0-20-9-20-20v-40c0-11 9-20 20-20h320c11 0 20 9 20 20v40c0 11-9 20-20 20zm-440 0h-40c-11 0-20-9-20-20v-40c0-11 9-20 20-20h40c11 0 20 9 20 20v40c0 11-9 20-20 20zm0 180h-40c-11 0-20-9-20-20v-40c0-11 9-20 20-20h40c11 0 20 9 20 20v40c0 11-9 20-20 20zm440 0H450c-11 0-20-9-20-20v-40c0-11 9-20 20-20h320c11 0 20 9 20 20v40c0 11-9 20-20 20z" />
                                                              </g>
                                                            </svg>
                                                            <span className="slds-assistive-text">
                                                              New Task
                                                            </span>
                                                          </div>
                                                          New Task
                                                        </LightningButton>
                                                      </div>
                                                      <lightning-button-menu
                                                        className="slds-dropdown-trigger slds-dropdown-trigger_click"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          position: "relative",
                                                          display:
                                                            "inline-block",
                                                        }}
                                                      >
                                                        <button
                                                          className="slds-button slds-button_icon-border-filled fix-slds-button_icon-border-filled slds-button_last"
                                                          type="button"
                                                          aria-expanded="false"
                                                          aria-haspopup="true"
                                                          disabled
                                                          title="No Additional New Task Actions"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            font: "inherit",
                                                            margin: "0px",
                                                            overflow: "visible",
                                                            textTransform:
                                                              "none",
                                                            backgroundPosition:
                                                              "initial",
                                                            borderStyle:
                                                              "solid",
                                                            textDecoration:
                                                              "none",
                                                            whiteSpace:
                                                              "normal",
                                                            position:
                                                              "relative",
                                                            display:
                                                              "inline-flex",
                                                            alignItems:
                                                              "center",
                                                            paddingTop:
                                                              "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                            paddingRight:
                                                              "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                            paddingBottom:
                                                              "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                            paddingLeft:
                                                              "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                            backgroundImage:
                                                              "none",
                                                            backgroundSize:
                                                              "initial",
                                                            backgroundRepeat:
                                                              "initial",
                                                            backgroundAttachment:
                                                              "initial",
                                                            backgroundOrigin:
                                                              "initial",
                                                            backgroundClip:
                                                              "border-box",
                                                            boxShadow:
                                                              "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                            appearance: "none",
                                                            userSelect: "none",
                                                            justifyContent:
                                                              "center",
                                                            flexShrink: 0,
                                                            width:
                                                              "var(--lwc-squareIconMediumBoundary,2rem)",
                                                            transition:
                                                              "border 0.15s linear",
                                                            verticalAlign:
                                                              "middle",
                                                            borderTopStyle: "",
                                                            borderRightStyle:
                                                              "",
                                                            borderBottomStyle:
                                                              "",
                                                            borderLeftStyle: "",
                                                            borderImageSource:
                                                              "",
                                                            borderImageSlice:
                                                              "",
                                                            borderImageWidth:
                                                              "",
                                                            borderImageOutset:
                                                              "",
                                                            borderImageRepeat:
                                                              "",
                                                            borderWidth:
                                                              "var(--lwc-borderWidthThin,1px)",
                                                            borderTopWidth: "",
                                                            borderRightWidth:
                                                              "",
                                                            borderBottomWidth:
                                                              "",
                                                            borderLeftWidth: "",
                                                            cursor: "default",
                                                            color:
                                                              "var(--slds-g-color-neutral-base-80, var(--lwc-colorTextButtonDefaultDisabled,rgb(201, 201, 201)))",
                                                            borderColor:
                                                              "var(--slds-g-color-border-base-1, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                                            backgroundColor:
                                                              "var(--slds-g-color-neutral-base-100, var(--lwc-colorBackgroundButtonDefaultDisabled,rgb(255, 255, 255)))",
                                                            height: "unset",
                                                            lineHeight:
                                                              "var(--slds-c-button-line-height, var(--sds-c-button-line-height, 1.875rem))",
                                                            borderRadius:
                                                              "0 var(--lwc-buttonBorderRadius,.25rem) var(--lwc-buttonBorderRadius,.25rem) 0",
                                                            marginLeft:
                                                              "calc(-1 * var(--lwc-borderWidthThin,1px))",
                                                          }}
                                                        >
                                                          <lightning-primitive-icon
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              pointerEvents:
                                                                "none",
                                                            }}
                                                          >
                                                            <svg
                                                              className="slds-button__icon"
                                                              aria-hidden="true"
                                                              focusable="false"
                                                              viewBox="0 0 520 520"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                verticalAlign:
                                                                  "middle",
                                                                width:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                height:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                overflow:
                                                                  "hidden",
                                                                fill: "currentcolor",
                                                                pointerEvents:
                                                                  "none",
                                                              }}
                                                            >
                                                              <g
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  pointerEvents:
                                                                    "none",
                                                                }}
                                                              >
                                                                <path
                                                                  d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                    pointerEvents:
                                                                      "none",
                                                                  }}
                                                                />
                                                              </g>
                                                            </svg>
                                                          </lightning-primitive-icon>
                                                          <span
                                                            id="button-label-463"
                                                            className="slds-assistive-text"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              pointerEvents:
                                                                "none",
                                                              margin: "-1px",
                                                              border: "0px",
                                                              padding: "0px",
                                                              overflow:
                                                                "hidden",
                                                              whiteSpace:
                                                                "nowrap",
                                                              position:
                                                                "absolute",
                                                              width: "1px",
                                                              height: "1px",
                                                              clip: "rect(0px, 0px, 0px, 0px)",
                                                              textTransform:
                                                                "none",
                                                            }}
                                                          >
                                                            No Additional New
                                                            Task Actions
                                                          </span>
                                                        </button>
                                                      </lightning-button-menu>
                                                    </slot>
                                                  </div>
                                                </lightning-button-group>
                                                <lightning-button-group
                                                  className="slds-m-vertical_xxx-small slds-button-group"
                                                  role="group"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    display: "inline-flex",
                                                    marginTop:
                                                      "var(--lwc-spacingXxxSmall,0.125rem)",
                                                    marginBottom:
                                                      "var(--lwc-spacingXxxSmall,0.125rem)",
                                                    marginLeft:
                                                      "var(--lwc-spacingXxSmall,0.25rem)",
                                                  }}
                                                >
                                                  <div
                                                    className="fix_button-group-flexbox"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      display: "inline-flex",
                                                    }}
                                                  >
                                                    <slot
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <LightningButton
                                                        variant="neutral"
                                                        value="LogACall"
                                                        aria-label="Log a Call"
                                                        title="Log a Call"
                                                        borderColor="rgb(201, 201, 201)"
                                                        style={{
                                                          display:
                                                            "inline-flex",
                                                          alignItems: "center",
                                                          borderTopRightRadius: 0,
                                                          borderBottomRightRadius: 0,
                                                          borderRadius:
                                                            "0.25rem 0 0 0.25rem",
                                                        }}
                                                      >
                                                        <div
                                                          className="slds-icon-standard-log-a-call slds-icon_container"
                                                          style={{
                                                            borderRadius:
                                                              "0.25rem",
                                                            display:
                                                              "inline-block",
                                                            lineHeight: "1",
                                                            backgroundColor:
                                                              "#06a59a",
                                                            marginRight:
                                                              "0.5rem",
                                                          }}
                                                        >
                                                          <svg
                                                            className="slds-icon slds-icon_small"
                                                            aria-hidden="true"
                                                            focusable="false"
                                                            viewBox="0 0 1000 1000"
                                                            style={{
                                                              verticalAlign:
                                                                "middle",
                                                              fill: "white",
                                                              width: "1.5rem",
                                                              height: "1.5rem",
                                                              lineHeight: "1",
                                                              overflow:
                                                                "hidden",
                                                            }}
                                                          >
                                                            <g>
                                                              <path d="M697 200H321c-33 0-61 30-61 60v20h-20c-22 0-40 18-40 40s18 40 40 40h20v100h-20c-22 0-40 18-40 40s18 40 40 40h20v100h-20c-22 0-40 18-40 40s18 40 40 40h20v20c0 30 28 60 61 60h376c33 0 63-30 63-63V257c0-33-30-57-63-57zm-36 403l-28 28c-6 6-15 10-23 9a264 264 0 01-249-249c0-9 3-17 9-23l28-28c13-13 35-12 46 3l26 32c9 11 9 26 1 38l-22 31c-3 4-3 10 1 13l46 51 51 46c4 4 9 4 13 1l31-22c11-8 27-8 38 1l32 26c12 8 13 30 0 43z" />
                                                            </g>
                                                          </svg>
                                                          <span className="slds-assistive-text">
                                                            Log a Call
                                                          </span>
                                                        </div>
                                                        Log a Call
                                                      </LightningButton>
                                                      <lightning-button-menu
                                                        className="slds-dropdown-trigger slds-dropdown-trigger_click"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          position: "relative",
                                                          display:
                                                            "inline-block",
                                                        }}
                                                      >
                                                        <button
                                                          className="slds-button slds-button_icon-border-filled fix-slds-button_icon-border-filled slds-button_last"
                                                          type="button"
                                                          aria-expanded="false"
                                                          aria-haspopup="true"
                                                          title="More Log a Call Actions"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            font: "inherit",
                                                            margin: "0px",
                                                            overflow: "visible",
                                                            textTransform:
                                                              "none",
                                                            cursor: "pointer",
                                                            backgroundPosition:
                                                              "initial",
                                                            borderStyle:
                                                              "solid",
                                                            textDecoration:
                                                              "none",
                                                            whiteSpace:
                                                              "normal",
                                                            position:
                                                              "relative",
                                                            display:
                                                              "inline-flex",
                                                            alignItems:
                                                              "center",
                                                            paddingTop:
                                                              "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                            paddingRight:
                                                              "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                            paddingBottom:
                                                              "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                            paddingLeft:
                                                              "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                            backgroundImage:
                                                              "none",
                                                            backgroundSize:
                                                              "initial",
                                                            backgroundRepeat:
                                                              "initial",
                                                            backgroundAttachment:
                                                              "initial",
                                                            backgroundOrigin:
                                                              "initial",
                                                            backgroundClip:
                                                              "border-box",
                                                            boxShadow:
                                                              "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                            appearance: "none",
                                                            userSelect: "none",
                                                            justifyContent:
                                                              "center",
                                                            flexShrink: 0,
                                                            width:
                                                              "var(--lwc-squareIconMediumBoundary,2rem)",
                                                            transition:
                                                              "border 0.15s linear",
                                                            borderColor:
                                                              "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                                            verticalAlign:
                                                              "middle",
                                                            color:
                                                              "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                            backgroundColor:
                                                              "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                                                            borderWidth:
                                                              "var(--lwc-borderWidthThin,1px)",
                                                            height: "unset",
                                                            lineHeight:
                                                              "var(--slds-c-button-line-height, var(--sds-c-button-line-height, 1.875rem))",
                                                            borderRadius:
                                                              "0 var(--lwc-buttonBorderRadius,.25rem) var(--lwc-buttonBorderRadius,.25rem) 0",
                                                            marginLeft:
                                                              "calc(-1 * var(--lwc-borderWidthThin,1px))",
                                                          }}
                                                        >
                                                          <lightning-primitive-icon
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                            }}
                                                          >
                                                            <svg
                                                              className="slds-button__icon"
                                                              aria-hidden="true"
                                                              focusable="false"
                                                              viewBox="0 0 520 520"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                verticalAlign:
                                                                  "middle",
                                                                width:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                height:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                                                overflow:
                                                                  "hidden",
                                                              }}
                                                            >
                                                              <g
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <path
                                                                  d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                />
                                                              </g>
                                                            </svg>
                                                          </lightning-primitive-icon>
                                                          <span
                                                            className="slds-assistive-text"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              margin: "-1px",
                                                              border: "0px",
                                                              padding: "0px",
                                                              overflow:
                                                                "hidden",
                                                              whiteSpace:
                                                                "nowrap",
                                                              position:
                                                                "absolute",
                                                              width: "1px",
                                                              height: "1px",
                                                              clip: "rect(0px, 0px, 0px, 0px)",
                                                              textTransform:
                                                                "none",
                                                            }}
                                                          >
                                                            More Log a Call
                                                            Actions
                                                          </span>
                                                        </button>
                                                      </lightning-button-menu>
                                                    </slot>
                                                  </div>
                                                </lightning-button-group>
                                                <lightning-button-group
                                                  className="slds-m-vertical_xxx-small slds-button-group"
                                                  role="group"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    display: "inline-flex",
                                                    marginTop:
                                                      "var(--lwc-spacingXxxSmall,0.125rem)",
                                                    marginBottom:
                                                      "var(--lwc-spacingXxxSmall,0.125rem)",
                                                    marginLeft:
                                                      "var(--lwc-spacingXxSmall,0.25rem)",
                                                  }}
                                                >
                                                  <div
                                                    className="fix_button-group-flexbox"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      display: "inline-flex",
                                                    }}
                                                  >
                                                    <slot
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <LightningButton
                                                        variant="neutral"
                                                        value="NewEvent"
                                                        aria-label="New Event"
                                                        title="New Event"
                                                        borderColor="rgb(201, 201, 201)"
                                                        style={{
                                                          display:
                                                            "inline-flex",
                                                          alignItems: "center",
                                                          borderTopRightRadius: 0,
                                                          borderBottomRightRadius: 0,
                                                          borderRadius:
                                                            "0.25rem 0 0 0.25rem",
                                                        }}
                                                      >
                                                        <div
                                                          className="slds-icon-standard-event slds-icon_container"
                                                          style={{
                                                            borderRadius:
                                                              "0.25rem",
                                                            display:
                                                              "inline-block",
                                                            lineHeight: "1",
                                                            backgroundColor:
                                                              "#cb65ff",
                                                            marginRight:
                                                              "0.5rem",
                                                          }}
                                                        >
                                                          <svg
                                                            className="slds-icon slds-icon_small"
                                                            aria-hidden="true"
                                                            focusable="false"
                                                            viewBox="0 0 100 100"
                                                            style={{
                                                              verticalAlign:
                                                                "middle",
                                                              fill: "white",
                                                              width: "1.5rem",
                                                              height: "1.5rem",
                                                              lineHeight: "1",
                                                              overflow:
                                                                "hidden",
                                                            }}
                                                          >
                                                            <g>
                                                              <path d="M76 42H24a2 2 0 00-2 2v30a6 6 0 006 6h44a6 6 0 006-6V44a2 2 0 00-2-2zM40 70a2 2 0 01-2 2h-4a2 2 0 01-2-2v-4c0-1.1.9-2 2-2h4a2 2 0 012 2zm0-14a2 2 0 01-2 2h-4a2 2 0 01-2-2v-4c0-1.1.9-2 2-2h4a2 2 0 012 2zm14 14a2 2 0 01-2 2h-4a2 2 0 01-2-2v-4c0-1.1.9-2 2-2h4a2 2 0 012 2zm0-14a2 2 0 01-2 2h-4a2 2 0 01-2-2v-4c0-1.1.9-2 2-2h4a2 2 0 012 2zm14 14a2 2 0 01-2 2h-4a2 2 0 01-2-2v-4c0-1.1.9-2 2-2h4a2 2 0 012 2zm0-14a2 2 0 01-2 2h-4a2 2 0 01-2-2v-4c0-1.1.9-2 2-2h4a2 2 0 012 2zm4-30h-5v-2c0-2.2-1.8-4-4-4s-4 1.8-4 4v2H41v-2c0-2.2-1.8-4-4-4s-4 1.8-4 4v2h-5a6 6 0 00-6 6v2c0 1.1.9 2 2 2h52a2 2 0 002-2v-2a6 6 0 00-6-6z" />
                                                            </g>
                                                          </svg>
                                                          <span className="slds-assistive-text">
                                                            New Event
                                                          </span>
                                                        </div>
                                                        New Event
                                                      </LightningButton>
                                                      <lightning-button-menu
                                                        className="slds-dropdown-trigger slds-dropdown-trigger_click"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          position: "relative",
                                                          display:
                                                            "inline-block",
                                                        }}
                                                      >
                                                        <button
                                                          className="slds-button slds-button_icon-border-filled fix-slds-button_icon-border-filled slds-button_last"
                                                          type="button"
                                                          aria-expanded="false"
                                                          aria-haspopup="true"
                                                          title="More New Event Actions"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            font: "inherit",
                                                            margin: "0px",
                                                            overflow: "visible",
                                                            textTransform:
                                                              "none",
                                                            cursor: "pointer",
                                                            backgroundPosition:
                                                              "initial",
                                                            borderStyle:
                                                              "solid",
                                                            textDecoration:
                                                              "none",
                                                            whiteSpace:
                                                              "normal",
                                                            position:
                                                              "relative",
                                                            display:
                                                              "inline-flex",
                                                            alignItems:
                                                              "center",
                                                            paddingTop:
                                                              "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                            paddingRight:
                                                              "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                            paddingBottom:
                                                              "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                            paddingLeft:
                                                              "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                            backgroundImage:
                                                              "none",
                                                            backgroundSize:
                                                              "initial",
                                                            backgroundRepeat:
                                                              "initial",
                                                            backgroundAttachment:
                                                              "initial",
                                                            backgroundOrigin:
                                                              "initial",
                                                            backgroundClip:
                                                              "border-box",
                                                            boxShadow:
                                                              "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                            appearance: "none",
                                                            userSelect: "none",
                                                            justifyContent:
                                                              "center",
                                                            flexShrink: 0,
                                                            width:
                                                              "var(--lwc-squareIconMediumBoundary,2rem)",
                                                            transition:
                                                              "border 0.15s linear",
                                                            borderColor:
                                                              "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                                            verticalAlign:
                                                              "middle",
                                                            color:
                                                              "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                            borderTopStyle: "",
                                                            borderRightStyle:
                                                              "",
                                                            borderBottomStyle:
                                                              "",
                                                            borderLeftStyle: "",
                                                            borderImageSource:
                                                              "",
                                                            borderImageSlice:
                                                              "",
                                                            borderImageWidth:
                                                              "",
                                                            borderImageOutset:
                                                              "",
                                                            borderImageRepeat:
                                                              "",
                                                            backgroundColor:
                                                              "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                                                            borderWidth:
                                                              "var(--lwc-borderWidthThin,1px)",
                                                            borderTopWidth: "",
                                                            borderRightWidth:
                                                              "",
                                                            borderBottomWidth:
                                                              "",
                                                            borderLeftWidth: "",
                                                            height: "unset",
                                                            lineHeight:
                                                              "var(--slds-c-button-line-height, var(--sds-c-button-line-height, 1.875rem))",
                                                            borderRadius:
                                                              "0 var(--lwc-buttonBorderRadius,.25rem) var(--lwc-buttonBorderRadius,.25rem) 0",
                                                            marginLeft:
                                                              "calc(-1 * var(--lwc-borderWidthThin,1px))",
                                                          }}
                                                        >
                                                          <lightning-primitive-icon
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                            }}
                                                          >
                                                            <svg
                                                              className="slds-button__icon"
                                                              aria-hidden="true"
                                                              focusable="false"
                                                              viewBox="0 0 520 520"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                verticalAlign:
                                                                  "middle",
                                                                width:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                height:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                                                overflow:
                                                                  "hidden",
                                                              }}
                                                            >
                                                              <g
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <path
                                                                  d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                />
                                                              </g>
                                                            </svg>
                                                          </lightning-primitive-icon>
                                                          <span
                                                            id="button-label-473"
                                                            className="slds-assistive-text"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              margin: "-1px",
                                                              border: "0px",
                                                              padding: "0px",
                                                              overflow:
                                                                "hidden",
                                                              whiteSpace:
                                                                "nowrap",
                                                              position:
                                                                "absolute",
                                                              width: "1px",
                                                              height: "1px",
                                                              clip: "rect(0px, 0px, 0px, 0px)",
                                                              textTransform:
                                                                "none",
                                                            }}
                                                          >
                                                            More New Event
                                                            Actions
                                                          </span>
                                                        </button>
                                                      </lightning-button-menu>
                                                    </slot>
                                                  </div>
                                                </lightning-button-group>
                                                <lightning-button-group
                                                  className="slds-m-vertical_xxx-small slds-button-group"
                                                  role="group"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    display: "inline-flex",
                                                    marginTop:
                                                      "var(--lwc-spacingXxxSmall,0.125rem)",
                                                    marginBottom:
                                                      "var(--lwc-spacingXxxSmall,0.125rem)",
                                                    marginLeft:
                                                      "var(--lwc-spacingXxSmall,0.25rem)",
                                                  }}
                                                >
                                                  <div
                                                    className="fix_button-group-flexbox"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      display: "inline-flex",
                                                    }}
                                                  >
                                                    <slot
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <LightningButton
                                                        variant="neutral"
                                                        value="SendEmail"
                                                        aria-label="Email"
                                                        title="Email"
                                                        borderColor="rgb(201, 201, 201)"
                                                        style={{
                                                          display:
                                                            "inline-flex",
                                                          alignItems: "center",
                                                          borderTopRightRadius: 0,
                                                          borderBottomRightRadius: 0,
                                                          borderRadius:
                                                            "0.25rem 0 0 0.25rem",
                                                        }}
                                                      >
                                                        <div
                                                          className="slds-icon-standard-email slds-icon_container"
                                                          style={{
                                                            borderRadius:
                                                              "0.25rem",
                                                            display:
                                                              "inline-block",
                                                            lineHeight: "1",
                                                            backgroundColor:
                                                              "#939393",
                                                            marginRight:
                                                              "0.5rem",
                                                          }}
                                                        >
                                                          <svg
                                                            className="slds-icon slds-icon_small"
                                                            aria-hidden="true"
                                                            focusable="false"
                                                            viewBox="0 0 1000 1000"
                                                            style={{
                                                              verticalAlign:
                                                                "middle",
                                                              fill: "white",
                                                              width: "1.5rem",
                                                              height: "1.5rem",
                                                              lineHeight: "1",
                                                              overflow:
                                                                "hidden",
                                                            }}
                                                          >
                                                            <g>
                                                              <path d="M487 550c8 7 19 7 27 0l283-262c5-10 4-26-16-26l-560 1c-15 0-27 14-16 26zm313-150c0-13-16-20-25-11L555 593c-15 14-34 21-54 21s-39-7-54-21L226 389a15 15 0 00-25 11v260c0 33 27 60 60 60h480c33 0 60-27 60-60z" />
                                                            </g>
                                                          </svg>
                                                          <span className="slds-assistive-text">
                                                            Email
                                                          </span>
                                                        </div>
                                                        Email
                                                      </LightningButton>
                                                      <lightning-button-menu
                                                        className="slds-dropdown-trigger slds-dropdown-trigger_click"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          position: "relative",
                                                          display:
                                                            "inline-block",
                                                        }}
                                                      >
                                                        <button
                                                          className="slds-button slds-button_icon-border-filled fix-slds-button_icon-border-filled slds-button_last"
                                                          type="button"
                                                          aria-expanded="false"
                                                          aria-haspopup="true"
                                                          title="More Email Actions"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            font: "inherit",
                                                            margin: "0px",
                                                            overflow: "visible",
                                                            textTransform:
                                                              "none",
                                                            cursor: "pointer",
                                                            backgroundPosition:
                                                              "initial",
                                                            borderStyle:
                                                              "solid",
                                                            textDecoration:
                                                              "none",
                                                            whiteSpace:
                                                              "normal",
                                                            position:
                                                              "relative",
                                                            display:
                                                              "inline-flex",
                                                            alignItems:
                                                              "center",
                                                            paddingTop:
                                                              "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                            paddingRight:
                                                              "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                            paddingBottom:
                                                              "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                            paddingLeft:
                                                              "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                            backgroundImage:
                                                              "none",
                                                            backgroundSize:
                                                              "initial",
                                                            backgroundRepeat:
                                                              "initial",
                                                            backgroundAttachment:
                                                              "initial",
                                                            backgroundOrigin:
                                                              "initial",
                                                            backgroundClip:
                                                              "border-box",
                                                            boxShadow:
                                                              "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                            appearance: "none",
                                                            userSelect: "none",
                                                            justifyContent:
                                                              "center",
                                                            flexShrink: 0,
                                                            width:
                                                              "var(--lwc-squareIconMediumBoundary,2rem)",
                                                            transition:
                                                              "border 0.15s linear",
                                                            borderColor:
                                                              "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                                            verticalAlign:
                                                              "middle",
                                                            color:
                                                              "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                            borderTopStyle: "",
                                                            borderRightStyle:
                                                              "",
                                                            borderBottomStyle:
                                                              "",
                                                            borderLeftStyle: "",
                                                            borderImageSource:
                                                              "",
                                                            borderImageSlice:
                                                              "",
                                                            borderImageWidth:
                                                              "",
                                                            borderImageOutset:
                                                              "",
                                                            borderImageRepeat:
                                                              "",
                                                            backgroundColor:
                                                              "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                                                            borderWidth:
                                                              "var(--lwc-borderWidthThin,1px)",
                                                            borderTopWidth: "",
                                                            borderRightWidth:
                                                              "",
                                                            borderBottomWidth:
                                                              "",
                                                            borderLeftWidth: "",
                                                            height: "unset",
                                                            lineHeight:
                                                              "var(--slds-c-button-line-height, var(--sds-c-button-line-height, 1.875rem))",
                                                            borderRadius:
                                                              "0 var(--lwc-buttonBorderRadius,.25rem) var(--lwc-buttonBorderRadius,.25rem) 0",
                                                            marginLeft:
                                                              "calc(-1 * var(--lwc-borderWidthThin,1px))",
                                                          }}
                                                        >
                                                          <lightning-primitive-icon
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                            }}
                                                          >
                                                            <svg
                                                              className="slds-button__icon"
                                                              aria-hidden="true"
                                                              focusable="false"
                                                              viewBox="0 0 520 520"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                verticalAlign:
                                                                  "middle",
                                                                width:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                height:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                                                overflow:
                                                                  "hidden",
                                                              }}
                                                            >
                                                              <g
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <path
                                                                  d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                />
                                                              </g>
                                                            </svg>
                                                          </lightning-primitive-icon>
                                                          <span
                                                            id="button-label-478"
                                                            className="slds-assistive-text"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              margin: "-1px",
                                                              border: "0px",
                                                              padding: "0px",
                                                              overflow:
                                                                "hidden",
                                                              whiteSpace:
                                                                "nowrap",
                                                              position:
                                                                "absolute",
                                                              width: "1px",
                                                              height: "1px",
                                                              clip: "rect(0px, 0px, 0px, 0px)",
                                                              textTransform:
                                                                "none",
                                                            }}
                                                          >
                                                            More Email Actions
                                                          </span>
                                                        </button>
                                                      </lightning-button-menu>
                                                    </slot>
                                                  </div>
                                                </lightning-button-group>
                                              </runtime_sales_activities-activity-panel-composer>
                                              <h2
                                                className="slds-assistive-text"
                                                style={{
                                                  boxSizing: "border-box",
                                                  fontWeight: "inherit",
                                                  fontSize: "1em",
                                                  margin: "-1px",
                                                  border: "0px",
                                                  padding: "0px",
                                                  overflow: "hidden",
                                                  whiteSpace: "nowrap",
                                                  position: "absolute",
                                                  width: "1px",
                                                  height: "1px",
                                                  clip: "rect(0px, 0px, 0px, 0px)",
                                                  textTransform: "none",
                                                }}
                                              >
                                                Activity Timeline
                                              </h2>
                                              <div
                                                className="MEDIUM grouped runtime_sales_activitiesActivityTimelineFixedLayout"
                                                style={{
                                                  boxSizing: "border-box",
                                                  padding:
                                                    "var(--lwc-varSpacingVerticalMedium,1rem) 0 var(--lwc-varSpacingVerticalLarge,1.5rem)",
                                                  position: "relative",
                                                }}
                                              >
                                                <div
                                                  className="skip-timeline-wrapper slds-p-top_none slds-is-relative"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    position: "relative",
                                                    paddingTop: "0px",
                                                  }}
                                                >
                                                  <a
                                                    className="slds-assistive-text slds-assistive-text_focus skipTimeline forceSkipLink"
                                                    href="#"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      textDecoration: "none",
                                                      transition:
                                                        "color 0.1s linear",
                                                      cursor: "pointer",
                                                      background:
                                                        "rgba(255, 255, 255, 0.98)",
                                                      left: "0px",
                                                      color:
                                                        "var(--slds-g-color-on-surface-2, var(--lwc-colorGray11, #3c3d3d))",
                                                      backgroundColor:
                                                        "rgba(255, 255, 255, 0.98)",
                                                      top: "-1rem",
                                                      margin: "-1px",
                                                      border: "0px",
                                                      padding: "0px",
                                                      overflow: "hidden",
                                                      whiteSpace: "nowrap",
                                                      position: "absolute",
                                                      width: "1px",
                                                      height: "1px",
                                                      clip: "rect(0px, 0px, 0px, 0px)",
                                                      textTransform: "none",
                                                    }}
                                                  >
                                                    Skip to the bottom of the
                                                    activity timeline
                                                  </a>
                                                  <div
                                                    className="timelineError"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      color:
                                                        "var(--slds-g-color-palette-red-50, var(--lwc-paletteRed50, #ea001e))",
                                                    }}
                                                  />
                                                  <div
                                                    className="slds-grid slds-wrap slds-grid_align-spread slds-grid_vertical-align-center slds-m-bottom_small"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      display: "flex",
                                                      flexWrap: "wrap",
                                                      justifyContent:
                                                        "space-between",
                                                      alignItems: "center",
                                                      alignContent: "center",
                                                      marginBottom:
                                                        "var(--lwc-spacingSmall,0.75rem)",
                                                    }}
                                                  >
                                                    <div
                                                      className="slds-col columnWidth"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        flex: "1 1 auto",
                                                        flexGrow: 0,
                                                      }}
                                                    />
                                                    <div
                                                      className="slds-col columnWidth slds-grid slds-grid_align-end"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        display: "flex",
                                                        flex: "1 1 auto",
                                                        justifyContent:
                                                          "flex-end",
                                                        flexGrow: 0,
                                                      }}
                                                    >
                                                      <span
                                                        className="slds-truncate"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          overflow: "hidden",
                                                          whiteSpace: "nowrap",
                                                          maxWidth: "100%",
                                                          textOverflow:
                                                            "ellipsis",
                                                        }}
                                                      >
                                                        <lightning-formatted-text
                                                          className="selectedFiltersText slds-show_inline-block"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            display:
                                                              "inline-block",
                                                            whiteSpace:
                                                              "normal",
                                                            fontSize:
                                                              "var(--slds-g-font-size-base, var(--lwc-fontSize3,0.8125rem))",
                                                            color:
                                                              "var(--slds-g-color-palette-neutral-10, var(--lwc-paletteNeutral10, #181818))",
                                                            marginTop:
                                                              "var(--slds-g-spacing-2, var(--lwc-spacingXSmall,0.5rem))",
                                                          }}
                                                        >
                                                          Filters: All time •
                                                          All activities • All
                                                          types
                                                        </lightning-formatted-text>
                                                      </span>
                                                      <button
                                                        className="slds-button slds-button_icon filterMenuLink slds-m-left_small slds-button_icon-border"
                                                        type="button"
                                                        aria-haspopup="true"
                                                        title="Timeline Settings"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          font: "inherit",
                                                          margin: "0px",
                                                          overflow: "visible",
                                                          textTransform: "none",
                                                          cursor: "pointer",
                                                          backgroundPosition:
                                                            "initial",
                                                          borderStyle: "solid",
                                                          borderWidth:
                                                            "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                          borderRadius:
                                                            "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                          textDecoration:
                                                            "none",
                                                          whiteSpace: "normal",
                                                          position: "relative",
                                                          display:
                                                            "inline-flex",
                                                          alignItems: "center",
                                                          paddingTop:
                                                            "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                          paddingRight:
                                                            "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                          paddingBottom:
                                                            "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                          paddingLeft:
                                                            "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                          backgroundImage:
                                                            "none",
                                                          backgroundSize:
                                                            "initial",
                                                          backgroundRepeat:
                                                            "initial",
                                                          backgroundAttachment:
                                                            "initial",
                                                          backgroundOrigin:
                                                            "initial",
                                                          backgroundColor:
                                                            "var(--slds-c-button-color-background, var(--sds-c-button-color-background, transparent))",
                                                          backgroundClip:
                                                            "border-box",
                                                          boxShadow:
                                                            "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                          appearance: "none",
                                                          userSelect: "none",
                                                          justifyContent:
                                                            "center",
                                                          flexShrink: 0,
                                                          width:
                                                            "var(--lwc-squareIconMediumBoundary,2rem)",
                                                          height:
                                                            "var(--lwc-squareIconMediumBoundary,2rem)",
                                                          transition:
                                                            "border 0.15s linear",
                                                          borderColor:
                                                            "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                                          lineHeight:
                                                            "var(--lwc-lineHeightReset,1)",
                                                          verticalAlign:
                                                            "middle",
                                                          color:
                                                            "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                          borderTopStyle: "",
                                                          borderTopWidth: "",
                                                          borderRightStyle: "",
                                                          borderRightWidth: "",
                                                          borderBottomStyle: "",
                                                          borderBottomWidth: "",
                                                          borderLeftStyle: "",
                                                          borderLeftWidth: "",
                                                          borderImageSource: "",
                                                          borderImageSlice: "",
                                                          borderImageWidth: "",
                                                          borderImageOutset: "",
                                                          borderImageRepeat: "",
                                                          marginLeft:
                                                            "var(--lwc-spacingSmall,0.75rem)",
                                                        }}
                                                      >
                                                        <lightning-primitive-icon
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <svg
                                                            className="slds-button__icon"
                                                            aria-hidden="true"
                                                            focusable="false"
                                                            viewBox="0 0 520 520"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              verticalAlign:
                                                                "middle",
                                                              width:
                                                                "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                              height:
                                                                "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                              fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                                              overflow:
                                                                "hidden",
                                                            }}
                                                          >
                                                            <g
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                              }}
                                                            >
                                                              <path
                                                                d="M261 191c-39 0-70 31-70 70s31 70 70 70 70-31 70-70-31-70-70-70zm210 133l-37-31a195 195 0 000-68l37-31c12-10 16-28 8-42l-16-28a34 34 0 00-40-14l-46 17a168 168 0 00-59-34l-8-47c-3-16-17-25-33-25h-32c-16 0-30 9-33 25l-8 46a180 180 0 00-60 34l-46-17-11-2c-12 0-23 6-29 16l-16 28c-8 14-5 32 8 42l37 31a195 195 0 000 68l-37 31a34 34 0 00-8 42l16 28a34 34 0 0040 14l46-17c18 16 38 27 59 34l8 48a33 33 0 0033 27h32c16 0 30-12 33-28l8-48a170 170 0 0062-37l43 17 12 2c12 0 23-6 29-16l15-26c9-11 5-29-7-39zm-210 47c-61 0-110-49-110-110s49-110 110-110 110 49 110 110-49 110-110 110z"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              />
                                                            </g>
                                                          </svg>
                                                        </lightning-primitive-icon>
                                                        <span
                                                          className="slds-assistive-text"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            margin: "-1px",
                                                            border: "0px",
                                                            padding: "0px",
                                                            overflow: "hidden",
                                                            whiteSpace:
                                                              "nowrap",
                                                            position:
                                                              "absolute",
                                                            width: "1px",
                                                            height: "1px",
                                                            clip: "rect(0px, 0px, 0px, 0px)",
                                                            textTransform:
                                                              "none",
                                                          }}
                                                        >
                                                          Timeline Settings
                                                        </span>
                                                      </button>
                                                    </div>
                                                  </div>
                                                  <div
                                                    className="slds-section slds-is-open slds-var-p-top_xx-small slds-var-p-bottom_x-small"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      marginTop:
                                                        "var(--lwc-spacingXSmall,0.5rem)",
                                                      marginBottom:
                                                        "var(--lwc-spacingXSmall,0.5rem)",
                                                      paddingTop:
                                                        "var(--lwc-varSpacingVerticalXxSmall,0.25rem)",
                                                      paddingBottom:
                                                        "var(--lwc-varSpacingVerticalXSmall,0.5rem)",
                                                    }}
                                                  >
                                                    <div
                                                      className="slds-section__title slds-button-section__title slds-m-bottom_x-small"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        border:
                                                          "var(--lwc-borderWidthThin,1px) solid transparent",
                                                        borderRadius:
                                                          "var(--lwc-borderRadiusMedium,0.25rem)",
                                                        display: "flex",
                                                        alignItems: "center",
                                                        lineHeight:
                                                          "var(--lwc-lineHeightButton,1.875rem)",
                                                        marginBottom:
                                                          "var(--lwc-spacingXSmall,0.5rem)",
                                                        fontSize:
                                                          "var(--slds-g-font-size-base, var(--lwc-fontSize3,0.8125rem))",
                                                        fontWeight:
                                                          "var(--slds-g-font-weight-7, var(--lwc-fontWeightBold,700))",
                                                      }}
                                                    >
                                                      <div
                                                        className="slds-section__title-action slds-p-around_none"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          background:
                                                            "var(--slds-g-color-neutral-base-95, var(--lwc-colorBackground,rgb(243, 243, 243)))",
                                                          display: "flex",
                                                          alignItems: "center",
                                                          cursor: "pointer",
                                                          width: "100%",
                                                          textAlign: "left",
                                                          color: "currentcolor",
                                                          fontSize: "inherit",
                                                          padding: "0px",
                                                        }}
                                                      />
                                                    </div>
                                                    <div
                                                      className="slds-button-section__buttons slds-text-align_center slds-var-m-bottom_x-small slds-is-relative slds-float_right"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        marginBottom:
                                                          "var(--lwc-varSpacingVerticalXSmall,0.5rem)",
                                                        cssFloat: "right",
                                                        textAlign: "center",
                                                        position: "relative",
                                                        background:
                                                          "var(--slds-g-color-palette-neutral-100, var(--lwc-paletteNeutral100, #ffffff))",
                                                        marginTop:
                                                          "calc(-1 * var(--lwc-spacingLarge,1.5rem))",
                                                      }}
                                                    >
                                                      <button
                                                        className="slds-button testonly-refreshTimeline slds-p-horizontal_xx-small"
                                                        type="button"
                                                        title="Refresh"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          font: "inherit",
                                                          margin: "0px",
                                                          overflow: "visible",
                                                          textTransform: "none",
                                                          cursor: "pointer",
                                                          backgroundPosition:
                                                            "initial",
                                                          borderColor:
                                                            "var(--slds-c-button-color-border, var(--sds-c-button-color-border, transparent))",
                                                          borderStyle: "solid",
                                                          borderWidth:
                                                            "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                          borderRadius:
                                                            "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                          textDecoration:
                                                            "none",
                                                          whiteSpace: "normal",
                                                          position: "relative",
                                                          display:
                                                            "inline-flex",
                                                          alignItems: "center",
                                                          paddingTop:
                                                            "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                          paddingBottom:
                                                            "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                          backgroundImage:
                                                            "none",
                                                          backgroundSize:
                                                            "initial",
                                                          backgroundRepeat:
                                                            "initial",
                                                          backgroundAttachment:
                                                            "initial",
                                                          backgroundOrigin:
                                                            "initial",
                                                          backgroundColor:
                                                            "var(--slds-c-button-color-background, var(--sds-c-button-color-background, transparent))",
                                                          backgroundClip:
                                                            "border-box",
                                                          boxShadow:
                                                            "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                          lineHeight:
                                                            "var(--slds-c-button-line-height, var(--sds-c-button-line-height, var(--lwc-lineHeightButton,1.875rem)))",
                                                          color:
                                                            "var(--slds-c-button-text-color, var(--sds-c-button-text-color, var(--lwc-brandAccessible,rgba(1, 118, 211, 1))))",
                                                          appearance: "none",
                                                          userSelect: "none",
                                                          paddingLeft:
                                                            "var(--lwc-spacingXxSmall,0.25rem)",
                                                          paddingRight:
                                                            "var(--lwc-spacingXxSmall,0.25rem)",
                                                        }}
                                                      >
                                                        Refresh
                                                      </button>
                                                      <span
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        {" "}
                                                        •{" "}
                                                      </span>
                                                      <button
                                                        className="slds-button testonly-expandAll slds-p-horizontal_xx-small"
                                                        type="button"
                                                        title="Expand All"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          font: "inherit",
                                                          margin: "0px",
                                                          overflow: "visible",
                                                          textTransform: "none",
                                                          cursor: "pointer",
                                                          backgroundPosition:
                                                            "initial",
                                                          borderColor:
                                                            "var(--slds-c-button-color-border, var(--sds-c-button-color-border, transparent))",
                                                          borderStyle: "solid",
                                                          borderWidth:
                                                            "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                          borderRadius:
                                                            "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                          textDecoration:
                                                            "none",
                                                          whiteSpace: "normal",
                                                          position: "relative",
                                                          display:
                                                            "inline-flex",
                                                          alignItems: "center",
                                                          paddingTop:
                                                            "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                          paddingBottom:
                                                            "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                          backgroundImage:
                                                            "none",
                                                          backgroundSize:
                                                            "initial",
                                                          backgroundRepeat:
                                                            "initial",
                                                          backgroundAttachment:
                                                            "initial",
                                                          backgroundOrigin:
                                                            "initial",
                                                          backgroundColor:
                                                            "var(--slds-c-button-color-background, var(--sds-c-button-color-background, transparent))",
                                                          backgroundClip:
                                                            "border-box",
                                                          boxShadow:
                                                            "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                          lineHeight:
                                                            "var(--slds-c-button-line-height, var(--sds-c-button-line-height, var(--lwc-lineHeightButton,1.875rem)))",
                                                          color:
                                                            "var(--slds-c-button-text-color, var(--sds-c-button-text-color, var(--lwc-brandAccessible,rgba(1, 118, 211, 1))))",
                                                          appearance: "none",
                                                          userSelect: "none",
                                                          paddingLeft:
                                                            "var(--lwc-spacingXxSmall,0.25rem)",
                                                          paddingRight:
                                                            "var(--lwc-spacingXxSmall,0.25rem)",
                                                        }}
                                                      >
                                                        Expand All
                                                      </button>
                                                      <span
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        {" "}
                                                        •{" "}
                                                      </span>
                                                      <a
                                                        className="slds-button slds-p-horizontal_xx-small"
                                                        role="link"
                                                        target="_blank"
                                                        title="Show all past activities in a new tab"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          transition:
                                                            "color 0.1s linear",
                                                          cursor: "pointer",
                                                          backgroundPosition:
                                                            "initial",
                                                          borderColor:
                                                            "var(--slds-c-button-color-border, var(--sds-c-button-color-border, transparent))",
                                                          borderStyle: "solid",
                                                          borderWidth:
                                                            "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                          borderRadius:
                                                            "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                          textDecoration:
                                                            "none",
                                                          whiteSpace: "normal",
                                                          position: "relative",
                                                          display:
                                                            "inline-flex",
                                                          alignItems: "center",
                                                          paddingTop:
                                                            "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                          paddingBottom:
                                                            "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                          backgroundImage:
                                                            "none",
                                                          backgroundSize:
                                                            "initial",
                                                          backgroundRepeat:
                                                            "initial",
                                                          backgroundAttachment:
                                                            "initial",
                                                          backgroundOrigin:
                                                            "initial",
                                                          backgroundColor:
                                                            "var(--slds-c-button-color-background, var(--sds-c-button-color-background, transparent))",
                                                          backgroundClip:
                                                            "border-box",
                                                          boxShadow:
                                                            "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                          lineHeight:
                                                            "var(--slds-c-button-line-height, var(--sds-c-button-line-height, var(--lwc-lineHeightButton,1.875rem)))",
                                                          color:
                                                            "var(--slds-c-button-text-color, var(--sds-c-button-text-color, var(--lwc-brandAccessible,rgba(1, 118, 211, 1))))",
                                                          appearance: "none",
                                                          userSelect: "none",
                                                          paddingLeft:
                                                            "var(--lwc-spacingXxSmall,0.25rem)",
                                                          paddingRight:
                                                            "var(--lwc-spacingXxSmall,0.25rem)",
                                                          textAlign: "center",
                                                        }}
                                                      >
                                                        View All
                                                      </a>
                                                    </div>
                                                  </div>
                                                  <div
                                                    className="standardTimelineUpcomingActivities"
                                                    style={{
                                                      boxSizing: "border-box",
                                                    }}
                                                  >
                                                    <div
                                                      className="slds-section open-activity-group openActivities slds-is-open"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        marginTop:
                                                          "var(--lwc-spacingXSmall,0.5rem)",
                                                        marginBottom:
                                                          "var(--lwc-spacingXSmall,0.5rem)",
                                                      }}
                                                    >
                                                      <h3
                                                        className="openActivitiesHeader slds-section__title"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          margin: "0px",
                                                          padding: "0px",
                                                          border:
                                                            "var(--lwc-borderWidthThin,1px) solid transparent",
                                                          borderRadius:
                                                            "var(--lwc-borderRadiusMedium,0.25rem)",
                                                          display: "flex",
                                                          alignItems: "center",
                                                          lineHeight:
                                                            "var(--lwc-lineHeightButton,1.875rem)",
                                                          fontSize:
                                                            "var(--slds-g-font-size-base, var(--lwc-fontSize3,0.8125rem))",
                                                          fontWeight:
                                                            "var(--slds-g-font-weight-7, var(--lwc-fontWeightBold,700))",
                                                        }}
                                                      >
                                                        <button
                                                          className="slds-button slds-section__title-action"
                                                          name="upcoming"
                                                          type="button"
                                                          aria-controls="upcoming-activities-section-006aj00000BXz4nAAD"
                                                          aria-expanded="true"
                                                          title="Upcoming & Overdue"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            font: "inherit",
                                                            margin: "0px",
                                                            overflow: "visible",
                                                            textTransform:
                                                              "none",
                                                            backgroundPosition:
                                                              "initial",
                                                            borderColor:
                                                              "var(--slds-c-button-color-border, var(--sds-c-button-color-border, transparent))",
                                                            borderStyle:
                                                              "solid",
                                                            borderWidth:
                                                              "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                            borderRadius:
                                                              "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                            textDecoration:
                                                              "none",
                                                            whiteSpace:
                                                              "normal",
                                                            position:
                                                              "relative",
                                                            boxShadow:
                                                              "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                            lineHeight:
                                                              "var(--slds-c-button-line-height, var(--sds-c-button-line-height, var(--lwc-lineHeightButton,1.875rem)))",
                                                            appearance: "none",
                                                            userSelect: "none",
                                                            background:
                                                              "var(--slds-g-color-neutral-base-95, var(--lwc-colorBackground,rgb(243, 243, 243)))",
                                                            padding:
                                                              "0 var(--lwc-spacingXSmall,0.5rem)",
                                                            display: "flex",
                                                            alignItems:
                                                              "center",
                                                            backgroundImage: "",
                                                            backgroundSize: "",
                                                            backgroundRepeat:
                                                              "",
                                                            backgroundAttachment:
                                                              "",
                                                            backgroundOrigin:
                                                              "",
                                                            backgroundClip: "",
                                                            backgroundColor: "",
                                                            cursor: "pointer",
                                                            width: "100%",
                                                            textAlign: "left",
                                                            color:
                                                              "currentcolor",
                                                            fontSize: "inherit",
                                                            paddingTop: "",
                                                            paddingRight: "",
                                                            paddingBottom: "",
                                                            paddingLeft: "",
                                                          }}
                                                        >
                                                          <lightning-primitive-icon
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                            }}
                                                          >
                                                            <svg
                                                              className="slds-button__icon slds-button__icon_left"
                                                              aria-hidden="true"
                                                              focusable="false"
                                                              viewBox="0 0 520 520"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                verticalAlign:
                                                                  "middle",
                                                                width:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                height:
                                                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                                                marginRight:
                                                                  "var(--lwc-spacingXSmall,0.5rem)",
                                                                overflow:
                                                                  "hidden",
                                                              }}
                                                            >
                                                              <g
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <path
                                                                  d="M476 178L271 385c-6 6-16 6-22 0L44 178c-6-6-6-16 0-22l22-22c6-6 16-6 22 0l161 163c6 6 16 6 22 0l161-162c6-6 16-6 22 0l22 22c5 6 5 15 0 21z"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                />
                                                              </g>
                                                            </svg>
                                                          </lightning-primitive-icon>
                                                          Upcoming & Overdue
                                                        </button>
                                                      </h3>
                                                      <div
                                                        id="upcoming-activities-section-006aj00000BXz4nAAD"
                                                        className="slds-section__content"
                                                        aria-hidden="false"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          overflow: "visible",
                                                          paddingTop:
                                                            "var(--lwc-spacingXSmall,0.5rem)",
                                                          visibility: "visible",
                                                          opacity: 1,
                                                          height: "auto",
                                                        }}
                                                      >
                                                        <div
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <ul
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              listStyle: "none",
                                                              margin:
                                                                "var(--lwc-spacingNone,0)",
                                                              padding:
                                                                "var(--lwc-spacingNone,0)",
                                                            }}
                                                          />
                                                        </div>
                                                        <div
                                                          className="slds-text-align_center emptyListContent"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            textAlign: "center",
                                                          }}
                                                        >
                                                          <lightning-formatted-rich-text
                                                            className="slds-rich-text-editor__output"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              lineHeight:
                                                                "var(--lwc-lineHeightText,1.5)",
                                                              overflowWrap:
                                                                "break-word",
                                                              hyphens: "manual",
                                                            }}
                                                          >
                                                            <span
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                              }}
                                                            >
                                                              No activities to
                                                              show.
                                                              <br
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              />{" "}
                                                              Get started by
                                                              sending an email,
                                                              scheduling a task,
                                                              and more.
                                                            </span>
                                                          </lightning-formatted-rich-text>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                  <div
                                                    className="past-activity-groups pastActivities activityHistories"
                                                    style={{
                                                      boxSizing: "border-box",
                                                    }}
                                                  >
                                                    <div
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <div
                                                        id="past-activities-section-006aj00000BXz4nAAD"
                                                        aria-hidden="false"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <div
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        />
                                                      </div>
                                                    </div>
                                                  </div>
                                                  <div
                                                    className="emptyPastActivities slds-border_top slds-p-top_medium slds-m-top_medium slds-text-align_center"
                                                    role="status"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      marginTop:
                                                        "var(--lwc-spacingMedium,1rem)",
                                                      paddingTop:
                                                        "var(--lwc-spacingMedium,1rem)",
                                                      borderTop:
                                                        "var(--lwc-borderWidthThin,1px) solid var(--slds-g-color-border-base-1, var(--lwc-colorBorder,rgb(229, 229, 229)))",
                                                      textAlign: "center",
                                                    }}
                                                  >
                                                    <lightning-formatted-rich-text
                                                      className="slds-rich-text-editor__output"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        lineHeight:
                                                          "var(--lwc-lineHeightText,1.5)",
                                                        overflowWrap:
                                                          "break-word",
                                                        hyphens: "manual",
                                                      }}
                                                    >
                                                      <span
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        No past activity. Past
                                                        meetings and tasks
                                                        marked as done show up
                                                        here.
                                                      </span>
                                                    </lightning-formatted-rich-text>
                                                  </div>
                                                  <div
                                                    className="inlineSpinnerWrapper"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      paddingLeft: "50%",
                                                    }}
                                                  >
                                                    <div
                                                      className="slds-is-absolute slds-var-p-left_large inlineSpinner indicator hideEl forceInlineSpinner"
                                                      aria-hidden="true"
                                                      role="alert"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        paddingLeft:
                                                          "var(--lwc-varSpacingHorizontalLarge,1.5rem)",
                                                        position: "absolute",
                                                        textAlign: "center",
                                                        display: "none",
                                                        bottom:
                                                          "var(--lwc-varSpacingVerticalXxSmall,0.25rem)",
                                                      }}
                                                    />
                                                  </div>
                                                  <a
                                                    className="slds-assistive-text slds-assistive-text_focus skip-timeline-endpoint forceSkipLink"
                                                    href="#"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      textDecoration: "none",
                                                      transition:
                                                        "color 0.1s linear",
                                                      cursor: "pointer",
                                                      background:
                                                        "rgba(255, 255, 255, 0.98)",
                                                      color:
                                                        "var(--slds-g-color-on-surface-2, var(--lwc-colorGray11, #3c3d3d))",
                                                      backgroundColor:
                                                        "rgba(255, 255, 255, 0.98)",
                                                      top: "-1rem",
                                                      left: "auto",
                                                      right: "0px",
                                                      margin: "-1px",
                                                      border: "0px",
                                                      padding: "0px",
                                                      overflow: "hidden",
                                                      whiteSpace: "nowrap",
                                                      position: "absolute",
                                                      width: "1px",
                                                      height: "1px",
                                                      clip: "rect(0px, 0px, 0px, 0px)",
                                                      textTransform: "none",
                                                    }}
                                                  >
                                                    Skip to the top of the
                                                    activity timeline
                                                  </a>
                                                </div>
                                                <div
                                                  className="timeline-end"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    height: "1px",
                                                    width: "1px",
                                                  }}
                                                />
                                              </div>
                                            </div>
                                          </div>
                                        </flexipage-aura-wrapper>
                                      </slot>
                                    </flexipage-component2>
                                  </slot>
                                </flexipage-tab2>
                                <flexipage-tab2
                                  id="tab-8"
                                  className="slds-tabs_default__content slds-hide"
                                  aria-labelledby="detailTab__item"
                                  role="tabpanel"
                                  tabIndex="0"
                                  style={{
                                    boxSizing: "border-box",
                                    position: "relative",
                                    paddingRight:
                                      "var(--slds-c-tabs-panel-spacing-inlineend, var(--slds-c-tabs-panel-spacing-inline-end, var(--sds-c-tabs-panel-spacing-inline-end, var(--slds-c-tabs-panel-spacing-inline, var(--sds-c-tabs-panel-spacing-inline, 0)))))",
                                    paddingLeft:
                                      "var(--slds-c-tabs-panel-spacing-inlinestart, var(--slds-c-tabs-panel-spacing-inline-start, var(--sds-c-tabs-panel-spacing-inline-start, var(--slds-c-tabs-panel-spacing-inline, var(--sds-c-tabs-panel-spacing-inline, 0)))))",
                                    paddingTop: "0.75rem",
                                    paddingBottom: "0px",
                                    display: "none",
                                  }}
                                />
                                <flexipage-tab2
                                  id="tab-9"
                                  className="slds-tabs_default__content slds-hide"
                                  aria-labelledby="collaborateTab__item"
                                  role="tabpanel"
                                  tabIndex="0"
                                  style={{
                                    boxSizing: "border-box",
                                    position: "relative",
                                    paddingRight:
                                      "var(--slds-c-tabs-panel-spacing-inlineend, var(--slds-c-tabs-panel-spacing-inline-end, var(--sds-c-tabs-panel-spacing-inline-end, var(--slds-c-tabs-panel-spacing-inline, var(--sds-c-tabs-panel-spacing-inline, 0)))))",
                                    paddingLeft:
                                      "var(--slds-c-tabs-panel-spacing-inlinestart, var(--slds-c-tabs-panel-spacing-inline-start, var(--sds-c-tabs-panel-spacing-inline-start, var(--slds-c-tabs-panel-spacing-inline, var(--sds-c-tabs-panel-spacing-inline, 0)))))",
                                    paddingTop: "0.75rem",
                                    paddingBottom: "0px",
                                    display: "none",
                                  }}
                                />
                              </slot>
                            </slot>
                          </div>
                        </lightning-tabset>
                      </div>
                    </flexipage-tabset2>
                  </slot>
                </flexipage-component2>
              </slot>
            </div>
            <Related />
          </div>
        </div>
      </flexipage-record-home-with-subheader-template-desktop2>
      <style
        dangerouslySetInnerHTML={{
          __html: `
html {
  box-sizing: border-box;
  text-size-adjust: 100%;
  background: var(--lwc-brandBackgroundPrimary,rgba(176, 196, 223, 1));
  font-family: var(--lwc-fontFamily,-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol');
  font-size: 100%;
  line-height: var(--lwc-varLineHeightText,1.5);
  color: var(--slds-g-color-neutral-base-10, var(--lwc-colorTextDefault,rgb(24, 24, 24)));
  -webkit-tap-highlight-color: transparent;
}

body {
  box-sizing: border-box;
  margin: 0px;
  background: transparent;
  font-size: var(--lwc-fontSize3,0.8125rem);
  overflow: visible;
}
`,
        }}
      />
    </>
  );
}
