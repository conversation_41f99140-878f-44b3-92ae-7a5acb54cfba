import React, { useContext } from "react";
import { GlobalContext } from "../../../context/GlobalContext";

export default function Related() {
  const { allVariableData } = useContext(GlobalContext);

  return (
    <div
      className="slds-col slds-large-size_4-of-12 slds-medium-size_4-of-12 slds-size_1-of-1 column region-sidebar-right"
      style={{
        boxSizing: "border-box",
        flex: "0 0 auto",
        paddingLeft: "0.75rem",
        width: "33%",
      }}
    >
      <slot name="sidebar" style={{ boxSizing: "border-box" }}>
        <flexipage-component2
          style={{
            boxSizing: "border-box",
            display: "block",
            gridColumnStart: "var(--flexipage-fieldsection-column-index, auto)",
          }}
        >
          <slot style={{ boxSizing: "border-box" }}>
            <flexipage-tabset2 style={{ boxSizing: "border-box" }}>
              <div
                className="slds-tabs_card"
                style={{
                  boxSizing: "border-box",
                  padding:
                    "var(--lwc-varSpacingVerticalSmall,0.75rem) var(--lwc-varSpacingHorizontalMedium,1rem)",
                  background:
                    "var(--slds-c-card-color-background, var(--sds-c-card-color-background, var(--slds-g-color-neutral-base-100, var(--lwc-cardColorBackground,rgb(255, 255, 255)))))",
                  borderWidth:
                    "var(--slds-c-card-sizing-border, var(--sds-c-card-sizing-border, var(--lwc-borderWidthThin,1px)))",
                  borderStyle: "solid",
                  borderColor:
                    "var(--slds-c-card-color-border, var(--sds-c-card-color-border, var(--slds-g-color-border-base-1, var(--lwc-cardColorBorder,rgb(201, 201, 201)))))",
                  borderRadius:
                    "var(--slds-c-card-radius-border, var(--sds-c-card-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                  boxShadow:
                    "var(--slds-c-card-shadow, var(--sds-c-card-shadow, var(--lwc-cardShadow,0 2px 2px 0 rgba(0, 0, 0, 0.10))))",
                }}
              >
                <h2
                  className="slds-assistive-text"
                  style={{
                    boxSizing: "border-box",
                    fontWeight: "inherit",
                    fontSize: "1em",
                    margin: "-1px",
                    border: "0px",
                    padding: "0px",
                    overflow: "hidden",
                    whiteSpace: "nowrap",
                    position: "absolute",
                    width: "1px",
                    height: "1px",
                    clip: "rect(0px, 0px, 0px, 0px)",
                    textTransform: "none",
                  }}
                >
                  Tabs
                </h2>
                <lightning-tabset
                  className="flexipage-tabset"
                  style={{ boxSizing: "border-box" }}
                >
                  <div
                    className="slds-tabs_default"
                    style={{
                      boxSizing: "border-box",
                      display: "block",
                      width: "100%",
                    }}
                  >
                    <lightning-tab-bar style={{ boxSizing: "border-box" }}>
                      <ul
                        className="slds-tabs_default__nav"
                        aria-label="Tabs"
                        role="tablist"
                        style={{
                          boxSizing: "border-box",
                          margin: "0px",
                          padding: "0px",
                          listStyle: "none",
                          display: "flex",
                          alignItems: "flex-start",
                          borderBottomWidth:
                            "var(--slds-c-tabs-list-sizing-border, var(--sds-c-tabs-list-sizing-border, var(--lwc-borderWidthThin,1px)))",
                          borderBottomStyle: "solid",
                          borderBottomColor:
                            "var(--slds-c-tabs-list-color-border, var(--sds-c-tabs-list-color-border, var(--slds-g-color-border-base-4, var(--lwc-colorBorder,rgb(229, 229, 229)))))",
                        }}
                      >
                        <li
                          className="slds-tabs_default__item slds-is-active"
                          role="presentation"
                          title="Related"
                          style={{
                            boxSizing: "border-box",
                            color:
                              "var(--slds-c-tabs-item-text-color, var(--sds-c-tabs-item-text-color, var(--slds-g-color-neutral-base-30, var(--lwc-colorTextActionLabel,rgb(68, 68, 68)))))",
                            position: "relative",
                            paddingTop:
                              "var(--slds-c-tabs-item-spacing-blockstart, var(--slds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block, 0))))",
                            paddingRight:
                              "var(--slds-c-tabs-item-spacing-inlineend, var(--slds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                            paddingBottom:
                              "var(--slds-c-tabs-item-spacing-blockend, var(--slds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block, 0))))",
                            paddingLeft:
                              "var(--slds-c-tabs-item-spacing-inlinestart, var(--slds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                            marginBottom:
                              "calc(var(--slds-c-tabs-list-sizing-border, var(--sds-c-tabs-list-sizing-border, var(--lwc-borderWidthThin,1px))) * -1)",
                            fontSize: "var(--lwc-fontSize5,1rem)",
                            textTransform: "capitalize",
                            letterSpacing: "normal",
                            maxWidth: "240px",
                          }}
                        >
                          <a
                            id="relatedListTab__item"
                            className="slds-tabs_default__link"
                            aria-controls="tab-10"
                            aria-selected="true"
                            href="#"
                            role="tab"
                            tabIndex="0"
                            style={{
                              boxSizing: "border-box",
                              backgroundColor: "transparent",
                              transition: "color 0.1s linear",
                              whiteSpace: "nowrap",
                              textDecoration: "none",
                              border: "0px",
                              maxWidth: "100%",
                              cursor: "pointer",
                              height:
                                "var(--slds-c-tabs-item-sizing-height, var(--sds-c-tabs-item-sizing-height, var(--lwc-lineHeightTab,2.5rem)))",
                              lineHeight:
                                "var(--slds-c-tabs-item-line-height, var(--sds-c-tabs-item-line-height, var(--lwc-lineHeightTab,2.5rem)))",
                              color: "currentcolor",
                              textTransform: "inherit",
                              zIndex: 1,
                              fontWeight: "var(--lwc-tabsFontWeight,700)",
                              overflow: "hidden",
                              paddingBottom:
                                "var(--lwc-varSpacingVerticalXSmall,0.5rem)",
                              display: "block",
                              textOverflow: "ellipsis",
                              textAlign: "center",
                            }}
                          >
                            Related
                          </a>
                        </li>
                        <li
                          className="slds-tabs_default__item slds-tabs_default__overflow-button"
                          style={{
                            boxSizing: "border-box",
                            color:
                              "var(--slds-c-tabs-item-text-color, var(--sds-c-tabs-item-text-color, var(--slds-g-color-neutral-base-30, var(--lwc-colorTextActionLabel,rgb(68, 68, 68)))))",
                            position: "relative",
                            paddingTop:
                              "var(--slds-c-tabs-item-spacing-blockstart, var(--slds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block-start, var(--sds-c-tabs-item-spacing-block, 0))))",
                            paddingRight:
                              "var(--slds-c-tabs-item-spacing-inlineend, var(--slds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline-end, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                            paddingBottom:
                              "var(--slds-c-tabs-item-spacing-blockend, var(--slds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block-end, var(--sds-c-tabs-item-spacing-block, 0))))",
                            paddingLeft:
                              "var(--slds-c-tabs-item-spacing-inlinestart, var(--slds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline-start, var(--sds-c-tabs-item-spacing-inline, var(--lwc-spacingSmall,0.75rem)))))",
                            marginBottom:
                              "calc(var(--slds-c-tabs-list-sizing-border, var(--sds-c-tabs-list-sizing-border, var(--lwc-borderWidthThin,1px))) * -1)",
                            display: "inline-flex",
                            height:
                              "var(--slds-c-tabs-item-sizing-height, var(--sds-c-tabs-item-sizing-height, var(--lwc-lineHeightTab,2.5rem)))",
                            lineHeight:
                              "var(--slds-c-tabs-item-line-height, var(--sds-c-tabs-item-line-height, var(--lwc-lineHeightTab,2.5rem)))",
                            fontSize: "var(--lwc-fontSize5,1rem)",
                            textTransform: "capitalize",
                            letterSpacing: "normal",
                            maxWidth: "240px",
                            marginLeft:
                              "var(--lwc-varSpacingHorizontalLarge,1.5rem)",
                            visibility: "hidden",
                          }}
                        >
                          <lightning-button-menu
                            className="slds-dropdown-trigger slds-dropdown-trigger_click"
                            label="More"
                            style={{
                              boxSizing: "border-box",
                              position: "relative",
                              display: "inline-block",
                            }}
                          >
                            <button
                              className="slds-button"
                              type="button"
                              aria-expanded="false"
                              aria-haspopup="true"
                              title="More Tabs"
                              style={{
                                boxSizing: "border-box",
                                font: "inherit",
                                margin: "0px",
                                textTransform: "none",
                                cursor: "pointer",
                                backgroundPosition: "initial",
                                borderColor:
                                  "var(--slds-c-button-color-border, var(--sds-c-button-color-border, transparent))",
                                borderStyle: "solid",
                                borderWidth:
                                  "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                borderRadius:
                                  "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                textDecoration: "none",
                                whiteSpace: "normal",
                                position: "relative",
                                alignItems: "center",
                                paddingTop:
                                  "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                paddingRight:
                                  "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                paddingLeft:
                                  "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                backgroundImage: "none",
                                backgroundSize: "initial",
                                backgroundRepeat: "initial",
                                backgroundAttachment: "initial",
                                backgroundOrigin: "initial",
                                backgroundColor:
                                  "var(--slds-c-button-color-background, var(--sds-c-button-color-background, transparent))",
                                backgroundClip: "border-box",
                                boxShadow:
                                  "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                appearance: "none",
                                userSelect: "none",
                                lineHeight: "inherit",
                                color:
                                  "var(--slds-c-tabs-item-text-color, var(--sds-c-tabs-item-text-color, var(--slds-g-color-neutral-base-30, var(--lwc-colorTextActionLabel,rgb(68, 68, 68)))))",
                                overflow: "hidden",
                                paddingBottom:
                                  "var(--lwc-varSpacingVerticalXSmall,0.5rem)",
                                display: "block",
                                textOverflow: "ellipsis",
                                textAlign: "center",
                              }}
                            >
                              More
                              <lightning-primitive-icon
                                style={{ boxSizing: "border-box" }}
                              >
                                <svg
                                  className="slds-button__icon slds-button__icon_right"
                                  aria-hidden="true"
                                  focusable="false"
                                  viewBox="0 0 520 520"
                                  style={{
                                    boxSizing: "border-box",
                                    verticalAlign: "middle",
                                    width:
                                      "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                    height:
                                      "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                    fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                    marginLeft:
                                      "var(--lwc-spacingXSmall,0.5rem)",
                                    overflow: "hidden",
                                  }}
                                >
                                  <g style={{ boxSizing: "border-box" }}>
                                    <path
                                      d="M476 178L271 385c-6 6-16 6-22 0L44 178c-6-6-6-16 0-22l22-22c6-6 16-6 22 0l161 163c6 6 16 6 22 0l161-162c6-6 16-6 22 0l22 22c5 6 5 15 0 21z"
                                      style={{
                                        boxSizing: "border-box",
                                      }}
                                    />
                                  </g>
                                </svg>
                              </lightning-primitive-icon>
                              <span
                                id="button-label-383"
                                className="slds-assistive-text"
                                style={{
                                  boxSizing: "border-box",
                                  margin: "-1px",
                                  border: "0px",
                                  padding: "0px",
                                  overflow: "hidden",
                                  whiteSpace: "nowrap",
                                  position: "absolute",
                                  width: "1px",
                                  height: "1px",
                                  clip: "rect(0px, 0px, 0px, 0px)",
                                  textTransform: "none",
                                }}
                              >
                                Tabs
                              </span>
                            </button>
                          </lightning-button-menu>
                        </li>
                      </ul>
                    </lightning-tab-bar>
                    <slot style={{ boxSizing: "border-box" }}>
                      <slot name="tabs" style={{ boxSizing: "border-box" }}>
                        <flexipage-tab2
                          id="tab-10"
                          className="slds-tabs_default__content slds-show"
                          aria-labelledby="relatedListTab__item"
                          role="tabpanel"
                          tabIndex="0"
                          style={{
                            boxSizing: "border-box",
                            position: "relative",
                            paddingRight:
                              "var(--slds-c-tabs-panel-spacing-inlineend, var(--slds-c-tabs-panel-spacing-inline-end, var(--sds-c-tabs-panel-spacing-inline-end, var(--slds-c-tabs-panel-spacing-inline, var(--sds-c-tabs-panel-spacing-inline, 0)))))",
                            paddingLeft:
                              "var(--slds-c-tabs-panel-spacing-inlinestart, var(--slds-c-tabs-panel-spacing-inline-start, var(--sds-c-tabs-panel-spacing-inline-start, var(--slds-c-tabs-panel-spacing-inline, var(--sds-c-tabs-panel-spacing-inline, 0)))))",
                            display: "block",
                            paddingTop: "0.75rem",
                            paddingBottom: "0px",
                          }}
                        >
                          <slot style={{ boxSizing: "border-box" }}>
                            <flexipage-component2
                              style={{
                                boxSizing: "border-box",
                                display: "block",
                                gridColumnStart:
                                  "var(--flexipage-fieldsection-column-index, auto)",
                              }}
                            >
                              <slot style={{ boxSizing: "border-box" }}>
                                <lst-related-list-container
                                  style={{ boxSizing: "border-box" }}
                                >
                                  <div
                                    className="container"
                                    style={{ boxSizing: "border-box" }}
                                  >
                                    <div
                                      className="listWrapper"
                                      style={{
                                        boxSizing: "border-box",
                                      }}
                                    >
                                      <lst-related-list-single-container
                                        style={{
                                          boxSizing: "border-box",
                                          display: "block",
                                        }}
                                      >
                                        <laf-progressive-container
                                          aria-busy="false"
                                          style={{
                                            boxSizing: "border-box",
                                            display: "block",
                                          }}
                                        >
                                          <slot
                                            style={{
                                              boxSizing: "border-box",
                                            }}
                                          >
                                            <lst-related-list-single-aura-wrapper
                                              style={{
                                                boxSizing: "border-box",
                                              }}
                                            >
                                              <div
                                                style={{
                                                  boxSizing: "border-box",
                                                }}
                                              >
                                                <div
                                                  className="container forceRelatedListSingleContainer"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    width: "100%",
                                                  }}
                                                >
                                                  <article
                                                    className="slds-card slds-card_boundary slds-card_related-list-fix forceRelatedListCardDesktop"
                                                    aria-label="Products"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      display: "block",
                                                      position: "relative",
                                                      paddingTop:
                                                        "var(--slds-c-card-spacing-blockstart, var(--slds-c-card-spacing-block-start, var(--sds-c-card-spacing-block-start, var(--sds-c-card-spacing-block, 0))))",
                                                      paddingBottom:
                                                        "var(--slds-c-card-spacing-blockend, var(--slds-c-card-spacing-block-end, var(--sds-c-card-spacing-block-end, var(--sds-c-card-spacing-block, 0))))",
                                                      backgroundImage: "",
                                                      backgroundPositionX: "",
                                                      backgroundPositionY: "",
                                                      backgroundSize: "",
                                                      backgroundRepeat: "",
                                                      backgroundAttachment: "",
                                                      backgroundOrigin: "",
                                                      backgroundColor: "",
                                                      backgroundClip:
                                                        "padding-box",
                                                      color:
                                                        "var(--slds-c-card-text-color, var(--sds-c-card-text-color))",
                                                      border: "0px",
                                                      boxShadow: "none",
                                                      paddingLeft: "0px",
                                                      paddingRight: "0px",
                                                      borderWidth:
                                                        "var(--slds-c-card-sizing-border, var(--sds-c-card-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                      borderStyle: "solid",
                                                      borderColor:
                                                        "var(--slds-c-card-color-border, var(--sds-c-card-color-border, var(--slds-g-color-border-base-1, var(--lwc-cardColorBorder,rgb(201, 201, 201)))))",
                                                      borderRadius:
                                                        "var(--slds-c-card-radius-border, var(--sds-c-card-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                    }}
                                                  >
                                                    <div
                                                      className="slds-grid slds-page-header forceRelatedListCardHeader"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        padding:
                                                          "var(--lwc-varSpacingVerticalMedium,1rem) var(--lwc-varSpacingHorizontalMedium,1rem)",
                                                        backgroundImage: "",
                                                        backgroundPositionX: "",
                                                        backgroundPositionY: "",
                                                        backgroundSize: "",
                                                        backgroundRepeat: "",
                                                        backgroundAttachment:
                                                          "",
                                                        backgroundOrigin: "",
                                                        backgroundColor: "",
                                                        backgroundClip:
                                                          "padding-box",
                                                        display: "flex",
                                                        border: "0px",
                                                        borderRadius:
                                                          "var(--lwc-borderRadiusMedium,0.25rem)",
                                                        boxShadow: "none",
                                                      }}
                                                    >
                                                      <header
                                                        className="slds-media slds-media--center slds-has-flexi-truncate"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "1 1 0%",
                                                          minWidth: "0px",
                                                          display: "flex",
                                                          alignItems: "center",
                                                          height:
                                                            "var(--lwc-squareIconMedium,2rem)",
                                                        }}
                                                      >
                                                        <div
                                                          className="slds-m-right_small stencil slds-avatar slds-avatar_small"
                                                          aria-hidden="true"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            overflow: "hidden",
                                                            borderRadius:
                                                              "var(--slds-c-avatar-radius-border, var(--sds-c-avatar-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                            display:
                                                              "inline-block",
                                                            verticalAlign:
                                                              "middle",
                                                            lineHeight:
                                                              "var(--lwc-lineHeightReset,1)",
                                                            color:
                                                              "var(--slds-c-avatar-text-color, var(--sds-c-avatar-text-color, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))))",
                                                            width:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            height:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            fontSize:
                                                              "var(--lwc-fontSize1,0.625rem)",
                                                            marginRight:
                                                              "var(--lwc-spacingSmall,0.75rem)",
                                                            backgroundColor:
                                                              "var(--slds-g-color-neutral-base-80, rgb(236, 235, 234))",
                                                          }}
                                                        >
                                                          <div
                                                            className="extraSmall forceEntityIcon"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              borderRadius:
                                                                "var(--slds-s-icon-radius-border, var(--lwc-borderRadiusSmall,0.125rem))",
                                                              display: "block",
                                                              width:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              height:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              backgroundColor:
                                                                "rgb(255, 93, 45)",
                                                            }}
                                                          >
                                                            <span
                                                              className="uiImage"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                display:
                                                                  "inherit",
                                                              }}
                                                            >
                                                              <img
                                                                className="icon"
                                                                src="/assets/icons/standard/product_120.png"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  border: "0px",
                                                                  verticalAlign:
                                                                    "middle",
                                                                  maxWidth:
                                                                    "100%",
                                                                  height:
                                                                    "auto",
                                                                  display:
                                                                    "inherit",
                                                                }}
                                                              />
                                                            </span>
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="slds-media__body"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            flex: "1 1 0%",
                                                            minWidth: "0px",
                                                            marginBottom: "0px",
                                                          }}
                                                        >
                                                          <h2
                                                            id="header_1544:0"
                                                            className="slds-card__header-title"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              margin: "0px",
                                                              padding: "0px",
                                                              display: "flex",
                                                              fontSize:
                                                                "var(--slds-c-card-heading-font-size, var(--sds-c-card-heading-font-size, var(--lwc-varFontSize5,1rem)))",
                                                              fontWeight:
                                                                "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                              lineHeight:
                                                                "var(--lwc-lineHeightHeading,1.25)",
                                                              marginBottom:
                                                                "0px",
                                                            }}
                                                          >
                                                            <a
                                                              className="slds-card__header-link baseCard__header-title-container"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                backgroundColor:
                                                                  "transparent",
                                                                textDecoration:
                                                                  "none",
                                                                transition:
                                                                  "color 0.1s linear",
                                                                cursor:
                                                                  "pointer",
                                                                color:
                                                                  "inherit",
                                                                fontWeight:
                                                                  "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                                display:
                                                                  "inline-flex",
                                                                maxWidth:
                                                                  "100%",
                                                                alignItems:
                                                                  "center",
                                                              }}
                                                            >
                                                              <span
                                                                className="slds-truncate slds-m-right--xx-small"
                                                                title="Products"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                  overflow:
                                                                    "hidden",
                                                                  whiteSpace:
                                                                    "nowrap",
                                                                  maxWidth:
                                                                    "100%",
                                                                  textOverflow:
                                                                    "ellipsis",
                                                                }}
                                                              >
                                                                Products
                                                              </span>
                                                              <span
                                                                className="slds-shrink-none slds-m-right--xx-small"
                                                                title="(0)"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  flexShrink: 0,
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                }}
                                                              >
                                                                (0)
                                                              </span>
                                                            </a>
                                                          </h2>
                                                        </div>
                                                      </header>
                                                      <div
                                                        className="slds-no-flex"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "0 0 auto",
                                                        }}
                                                      >
                                                        <div
                                                          className="actionsContainer"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="slds-float--right forceDeferredDropDownAction"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              cssFloat: "right",
                                                            }}
                                                          >
                                                            <a
                                                              className="slds-button slds-button_icon-small slds-button_icon-border-filled"
                                                              aria-haspopup="true"
                                                              href="#"
                                                              role="button"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                cursor:
                                                                  "pointer",
                                                                backgroundPosition:
                                                                  "initial",
                                                                borderStyle:
                                                                  "solid",
                                                                borderWidth:
                                                                  "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                                borderRadius:
                                                                  "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                                textDecoration:
                                                                  "none",
                                                                whiteSpace:
                                                                  "normal",
                                                                position:
                                                                  "relative",
                                                                display:
                                                                  "inline-flex",
                                                                alignItems:
                                                                  "center",
                                                                paddingTop:
                                                                  "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                                paddingRight:
                                                                  "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                                paddingBottom:
                                                                  "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                                paddingLeft:
                                                                  "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                                backgroundImage:
                                                                  "none",
                                                                backgroundSize:
                                                                  "initial",
                                                                backgroundRepeat:
                                                                  "initial",
                                                                backgroundAttachment:
                                                                  "initial",
                                                                backgroundOrigin:
                                                                  "initial",
                                                                backgroundClip:
                                                                  "border-box",
                                                                boxShadow:
                                                                  "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                                appearance:
                                                                  "none",
                                                                userSelect:
                                                                  "none",
                                                                justifyContent:
                                                                  "center",
                                                                flexShrink: 0,
                                                                transition:
                                                                  "border 0.15s linear",
                                                                borderColor:
                                                                  "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                                                lineHeight:
                                                                  "var(--lwc-lineHeightReset,1)",
                                                                verticalAlign:
                                                                  "middle",
                                                                color:
                                                                  "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                                borderTopStyle:
                                                                  "",
                                                                borderTopWidth:
                                                                  "",
                                                                borderRightStyle:
                                                                  "",
                                                                borderRightWidth:
                                                                  "",
                                                                borderBottomStyle:
                                                                  "",
                                                                borderBottomWidth:
                                                                  "",
                                                                borderLeftStyle:
                                                                  "",
                                                                borderLeftWidth:
                                                                  "",
                                                                borderImageSource:
                                                                  "",
                                                                borderImageSlice:
                                                                  "",
                                                                borderImageWidth:
                                                                  "",
                                                                borderImageOutset:
                                                                  "",
                                                                borderImageRepeat:
                                                                  "",
                                                                backgroundColor:
                                                                  "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                                                                width:
                                                                  "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                                height:
                                                                  "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                                textAlign:
                                                                  "center",
                                                              }}
                                                            >
                                                              <lightning-icon
                                                                className="slds-icon-utility-down slds-icon_container"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  borderRadius:
                                                                    "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                                  display:
                                                                    "inline-block",
                                                                  backgroundColor:
                                                                    "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                                                                  lineHeight:
                                                                    "initial",
                                                                }}
                                                              >
                                                                <span
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <lightning-primitive-icon
                                                                    size="xx-small"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                    }}
                                                                  >
                                                                    <svg
                                                                      className="slds-icon slds-icon-text-default slds-icon_xx-small"
                                                                      aria-hidden="true"
                                                                      focusable="false"
                                                                      viewBox="0 0 520 520"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        verticalAlign:
                                                                          "middle",
                                                                        lineHeight:
                                                                          "var(--lwc-lineHeightReset,1)",
                                                                        overflow:
                                                                          "hidden",
                                                                        width:
                                                                          "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                        height:
                                                                          "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                        fill: "currentcolor",
                                                                      }}
                                                                    >
                                                                      <g
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <path
                                                                          d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                          }}
                                                                        />
                                                                      </g>
                                                                    </svg>
                                                                  </lightning-primitive-icon>
                                                                  <span
                                                                    className="slds-assistive-text"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      margin:
                                                                        "-1px",
                                                                      border:
                                                                        "0px",
                                                                      padding:
                                                                        "0px",
                                                                      overflow:
                                                                        "hidden",
                                                                      whiteSpace:
                                                                        "nowrap",
                                                                      position:
                                                                        "absolute",
                                                                      width:
                                                                        "1px",
                                                                      height:
                                                                        "1px",
                                                                      clip: "rect(0px, 0px, 0px, 0px)",
                                                                      textTransform:
                                                                        "none",
                                                                    }}
                                                                  >
                                                                    Show actions
                                                                    for Products
                                                                  </span>
                                                                </span>
                                                              </lightning-icon>
                                                            </a>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                    <div
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <div
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <div
                                                          className="previewMode SMALL forceRelatedListPreview"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="hide"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              display: "none",
                                                            }}
                                                          >
                                                            <div
                                                              className="slds-card__body"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                marginTop:
                                                                  "var(--slds-c-card-body-spacing-blockstart, var(--slds-c-card-body-spacing-block-start, var(--sds-c-card-body-spacing-block-start, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                                marginBottom:
                                                                  "var(--slds-c-card-body-spacing-blockend, var(--slds-c-card-body-spacing-block-end, var(--sds-c-card-body-spacing-block-end, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                                paddingLeft:
                                                                  "0px",
                                                                paddingRight:
                                                                  "0px",
                                                                marginLeft:
                                                                  "0px",
                                                                marginRight:
                                                                  "0px",
                                                              }}
                                                            >
                                                              <div
                                                                className="slds-hide"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  display:
                                                                    "none",
                                                                }}
                                                              >
                                                                <force-placeholder2
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <div
                                                                    className="slds-p-around_xxx-small"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      padding:
                                                                        "var(--lwc-spacingXxxSmall,0.125rem)",
                                                                    }}
                                                                  >
                                                                    <div
                                                                      className="field"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        padding:
                                                                          "0 1rem",
                                                                      }}
                                                                    >
                                                                      <div
                                                                        className="text text-primary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          height:
                                                                            "0.25rem",
                                                                          maxWidth:
                                                                            "265px",
                                                                        }}
                                                                      />
                                                                      <div
                                                                        className="text text-secondary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          height:
                                                                            "0.25rem",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          maxWidth:
                                                                            "130px",
                                                                        }}
                                                                      />
                                                                    </div>
                                                                    <div
                                                                      className="field"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        padding:
                                                                          "0 1rem",
                                                                      }}
                                                                    >
                                                                      <div
                                                                        className="text text-primary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          height:
                                                                            "0.25rem",
                                                                          maxWidth:
                                                                            "180px",
                                                                        }}
                                                                      />
                                                                      <div
                                                                        className="text text-secondary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          height:
                                                                            "0.25rem",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          maxWidth:
                                                                            "210px",
                                                                        }}
                                                                      />
                                                                    </div>
                                                                  </div>
                                                                </force-placeholder2>
                                                              </div>
                                                              <ul
                                                                className="uiAbstractList"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  padding:
                                                                    "0px",
                                                                  listStyle:
                                                                    "none",
                                                                  margin: "0px",
                                                                }}
                                                              />
                                                              <div
                                                                className="emptyContent hidden"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <div
                                                                  className="emptyContentInner slds-text-align_center slds-text-align--center"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                    textAlign:
                                                                      "center",
                                                                  }}
                                                                />
                                                              </div>
                                                            </div>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </article>
                                                </div>
                                              </div>
                                            </lst-related-list-single-aura-wrapper>
                                          </slot>
                                        </laf-progressive-container>
                                      </lst-related-list-single-container>
                                    </div>
                                    <div
                                      className="listWrapper"
                                      style={{
                                        boxSizing: "border-box",
                                        marginTop: "0.75rem",
                                      }}
                                    >
                                      <lst-related-list-single-container
                                        style={{
                                          boxSizing: "border-box",
                                          display: "block",
                                        }}
                                      >
                                        <laf-progressive-container
                                          aria-busy="false"
                                          style={{
                                            boxSizing: "border-box",
                                            display: "block",
                                          }}
                                        >
                                          <slot
                                            style={{
                                              boxSizing: "border-box",
                                            }}
                                          >
                                            <lst-related-list-single-aura-wrapper
                                              style={{
                                                boxSizing: "border-box",
                                              }}
                                            >
                                              <div
                                                style={{
                                                  boxSizing: "border-box",
                                                }}
                                              >
                                                <div
                                                  className="container forceRelatedListSingleContainer"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    width: "100%",
                                                  }}
                                                >
                                                  <article
                                                    className="slds-card slds-card_boundary slds-card_related-list-fix forceRelatedListCardDesktop"
                                                    aria-label="Notes & Attachments"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      display: "block",
                                                      position: "relative",
                                                      paddingTop:
                                                        "var(--slds-c-card-spacing-blockstart, var(--slds-c-card-spacing-block-start, var(--sds-c-card-spacing-block-start, var(--sds-c-card-spacing-block, 0))))",
                                                      paddingBottom:
                                                        "var(--slds-c-card-spacing-blockend, var(--slds-c-card-spacing-block-end, var(--sds-c-card-spacing-block-end, var(--sds-c-card-spacing-block, 0))))",
                                                      backgroundImage: "",
                                                      backgroundPositionX: "",
                                                      backgroundPositionY: "",
                                                      backgroundSize: "",
                                                      backgroundRepeat: "",
                                                      backgroundAttachment: "",
                                                      backgroundOrigin: "",
                                                      backgroundColor: "",
                                                      backgroundClip:
                                                        "padding-box",
                                                      color:
                                                        "var(--slds-c-card-text-color, var(--sds-c-card-text-color))",
                                                      border: "0px",
                                                      boxShadow: "none",
                                                      paddingLeft: "0px",
                                                      paddingRight: "0px",
                                                      borderWidth:
                                                        "var(--slds-c-card-sizing-border, var(--sds-c-card-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                      borderStyle: "solid",
                                                      borderColor:
                                                        "var(--slds-c-card-color-border, var(--sds-c-card-color-border, var(--slds-g-color-border-base-1, var(--lwc-cardColorBorder,rgb(201, 201, 201)))))",
                                                      borderRadius:
                                                        "var(--slds-c-card-radius-border, var(--sds-c-card-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                    }}
                                                  >
                                                    <div
                                                      className="slds-grid slds-page-header forceRelatedListCardHeader"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        padding:
                                                          "var(--lwc-varSpacingVerticalMedium,1rem) var(--lwc-varSpacingHorizontalMedium,1rem)",
                                                        backgroundImage: "",
                                                        backgroundPositionX: "",
                                                        backgroundPositionY: "",
                                                        backgroundSize: "",
                                                        backgroundRepeat: "",
                                                        backgroundAttachment:
                                                          "",
                                                        backgroundOrigin: "",
                                                        backgroundColor: "",
                                                        backgroundClip:
                                                          "padding-box",
                                                        display: "flex",
                                                        border: "0px",
                                                        borderRadius:
                                                          "var(--lwc-borderRadiusMedium,0.25rem)",
                                                        boxShadow: "none",
                                                      }}
                                                    >
                                                      <header
                                                        className="slds-media slds-media--center slds-has-flexi-truncate"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "1 1 0%",
                                                          minWidth: "0px",
                                                          display: "flex",
                                                          alignItems: "center",
                                                          height:
                                                            "var(--lwc-squareIconMedium,2rem)",
                                                        }}
                                                      >
                                                        <div
                                                          className="slds-m-right_small stencil slds-avatar slds-avatar_small"
                                                          aria-hidden="true"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            overflow: "hidden",
                                                            borderRadius:
                                                              "var(--slds-c-avatar-radius-border, var(--sds-c-avatar-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                            display:
                                                              "inline-block",
                                                            verticalAlign:
                                                              "middle",
                                                            lineHeight:
                                                              "var(--lwc-lineHeightReset,1)",
                                                            color:
                                                              "var(--slds-c-avatar-text-color, var(--sds-c-avatar-text-color, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))))",
                                                            width:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            height:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            fontSize:
                                                              "var(--lwc-fontSize1,0.625rem)",
                                                            marginRight:
                                                              "var(--lwc-spacingSmall,0.75rem)",
                                                            backgroundColor:
                                                              "var(--slds-g-color-neutral-base-80, rgb(236, 235, 234))",
                                                          }}
                                                        >
                                                          <div
                                                            className="extraSmall forceEntityIcon"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              borderRadius:
                                                                "var(--slds-s-icon-radius-border, var(--lwc-borderRadiusSmall,0.125rem))",
                                                              display: "block",
                                                              width:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              height:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              backgroundColor:
                                                                "rgb(147, 147, 147)",
                                                            }}
                                                          >
                                                            <span
                                                              className="uiImage"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                display:
                                                                  "inherit",
                                                              }}
                                                            >
                                                              <img
                                                                className="icon"
                                                                src="/assets/icons/standard/file_120.png"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  border: "0px",
                                                                  verticalAlign:
                                                                    "middle",
                                                                  maxWidth:
                                                                    "100%",
                                                                  height:
                                                                    "auto",
                                                                  display:
                                                                    "inherit",
                                                                }}
                                                              />
                                                            </span>
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="slds-media__body"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            flex: "1 1 0%",
                                                            minWidth: "0px",
                                                            marginBottom: "0px",
                                                          }}
                                                        >
                                                          <h2
                                                            id="header_1595:0"
                                                            className="slds-card__header-title"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              margin: "0px",
                                                              padding: "0px",
                                                              display: "flex",
                                                              fontSize:
                                                                "var(--slds-c-card-heading-font-size, var(--sds-c-card-heading-font-size, var(--lwc-varFontSize5,1rem)))",
                                                              fontWeight:
                                                                "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                              lineHeight:
                                                                "var(--lwc-lineHeightHeading,1.25)",
                                                              marginBottom:
                                                                "0px",
                                                            }}
                                                          >
                                                            <a
                                                              className="slds-card__header-link baseCard__header-title-container"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                backgroundColor:
                                                                  "transparent",
                                                                textDecoration:
                                                                  "none",
                                                                transition:
                                                                  "color 0.1s linear",
                                                                cursor:
                                                                  "pointer",
                                                                color:
                                                                  "inherit",
                                                                fontWeight:
                                                                  "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                                display:
                                                                  "inline-flex",
                                                                maxWidth:
                                                                  "100%",
                                                                alignItems:
                                                                  "center",
                                                              }}
                                                            >
                                                              <span
                                                                className="slds-truncate slds-m-right--xx-small"
                                                                title="Notes & Attachments"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                  overflow:
                                                                    "hidden",
                                                                  whiteSpace:
                                                                    "nowrap",
                                                                  maxWidth:
                                                                    "100%",
                                                                  textOverflow:
                                                                    "ellipsis",
                                                                }}
                                                              >
                                                                Notes &
                                                                Attachments
                                                              </span>
                                                              <span
                                                                className="slds-shrink-none slds-m-right--xx-small"
                                                                title="(0)"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  flexShrink: 0,
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                }}
                                                              >
                                                                (0)
                                                              </span>
                                                            </a>
                                                          </h2>
                                                        </div>
                                                      </header>
                                                      <div
                                                        className="slds-no-flex"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "0 0 auto",
                                                        }}
                                                      >
                                                        <div
                                                          className="actionsContainer"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="slds-float--right forceDeferredDropDownAction"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              cssFloat: "right",
                                                            }}
                                                          >
                                                            <a
                                                              className="slds-button slds-button_icon-small slds-button_icon-border-filled"
                                                              aria-haspopup="true"
                                                              href="#"
                                                              role="button"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                cursor:
                                                                  "pointer",
                                                                backgroundPosition:
                                                                  "initial",
                                                                borderStyle:
                                                                  "solid",
                                                                borderWidth:
                                                                  "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                                borderRadius:
                                                                  "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                                textDecoration:
                                                                  "none",
                                                                whiteSpace:
                                                                  "normal",
                                                                position:
                                                                  "relative",
                                                                display:
                                                                  "inline-flex",
                                                                alignItems:
                                                                  "center",
                                                                paddingTop:
                                                                  "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                                paddingRight:
                                                                  "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                                paddingBottom:
                                                                  "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                                paddingLeft:
                                                                  "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                                backgroundImage:
                                                                  "none",
                                                                backgroundSize:
                                                                  "initial",
                                                                backgroundRepeat:
                                                                  "initial",
                                                                backgroundAttachment:
                                                                  "initial",
                                                                backgroundOrigin:
                                                                  "initial",
                                                                backgroundClip:
                                                                  "border-box",
                                                                boxShadow:
                                                                  "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                                appearance:
                                                                  "none",
                                                                userSelect:
                                                                  "none",
                                                                justifyContent:
                                                                  "center",
                                                                flexShrink: 0,
                                                                transition:
                                                                  "border 0.15s linear",
                                                                borderColor:
                                                                  "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                                                lineHeight:
                                                                  "var(--lwc-lineHeightReset,1)",
                                                                verticalAlign:
                                                                  "middle",
                                                                color:
                                                                  "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                                borderTopStyle:
                                                                  "",
                                                                borderTopWidth:
                                                                  "",
                                                                borderRightStyle:
                                                                  "",
                                                                borderRightWidth:
                                                                  "",
                                                                borderBottomStyle:
                                                                  "",
                                                                borderBottomWidth:
                                                                  "",
                                                                borderLeftStyle:
                                                                  "",
                                                                borderLeftWidth:
                                                                  "",
                                                                borderImageSource:
                                                                  "",
                                                                borderImageSlice:
                                                                  "",
                                                                borderImageWidth:
                                                                  "",
                                                                borderImageOutset:
                                                                  "",
                                                                borderImageRepeat:
                                                                  "",
                                                                backgroundColor:
                                                                  "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                                                                width:
                                                                  "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                                height:
                                                                  "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                                textAlign:
                                                                  "center",
                                                              }}
                                                            >
                                                              <lightning-icon
                                                                className="slds-icon-utility-down slds-icon_container"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  borderRadius:
                                                                    "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                                  display:
                                                                    "inline-block",
                                                                  backgroundColor:
                                                                    "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                                                                  lineHeight:
                                                                    "initial",
                                                                }}
                                                              >
                                                                <span
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <lightning-primitive-icon
                                                                    size="xx-small"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                    }}
                                                                  >
                                                                    <svg
                                                                      className="slds-icon slds-icon-text-default slds-icon_xx-small"
                                                                      aria-hidden="true"
                                                                      focusable="false"
                                                                      viewBox="0 0 520 520"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        verticalAlign:
                                                                          "middle",
                                                                        lineHeight:
                                                                          "var(--lwc-lineHeightReset,1)",
                                                                        overflow:
                                                                          "hidden",
                                                                        width:
                                                                          "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                        height:
                                                                          "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                        fill: "currentcolor",
                                                                      }}
                                                                    >
                                                                      <g
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <path
                                                                          d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                          }}
                                                                        />
                                                                      </g>
                                                                    </svg>
                                                                  </lightning-primitive-icon>
                                                                  <span
                                                                    className="slds-assistive-text"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      margin:
                                                                        "-1px",
                                                                      border:
                                                                        "0px",
                                                                      padding:
                                                                        "0px",
                                                                      overflow:
                                                                        "hidden",
                                                                      whiteSpace:
                                                                        "nowrap",
                                                                      position:
                                                                        "absolute",
                                                                      width:
                                                                        "1px",
                                                                      height:
                                                                        "1px",
                                                                      clip: "rect(0px, 0px, 0px, 0px)",
                                                                      textTransform:
                                                                        "none",
                                                                    }}
                                                                  >
                                                                    Show actions
                                                                    for Notes &
                                                                    Attachments
                                                                  </span>
                                                                </span>
                                                              </lightning-icon>
                                                            </a>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                    <div
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <div
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <div
                                                          className="previewMode SMALL forceRelatedListPreview"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                            }}
                                                          >
                                                            <div
                                                              className="slds-card__body_inner forceContentFileDroppableZone forceContentRelatedListPreviewFileList"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                borderBottom:
                                                                  "var(--slds-g-sizing-border-1, var(--lwc-borderWidthThin,1px)) solid var(--slds-s-container-color-border, var(--lwc-cardColorBorder,rgb(201, 201, 201)))",
                                                                paddingTop:
                                                                  "0px",
                                                                paddingBottom:
                                                                  "0px",
                                                                paddingRight:
                                                                  "var(--slds-c-card-body-spacing-inlineend, var(--slds-c-card-body-spacing-inline-end, var(--sds-c-card-body-spacing-inline-end, var(--slds-c-card-body-spacing-inline, var(--sds-c-card-body-spacing-inline, var(--lwc-varSpacingHorizontalMedium,1rem))))))",
                                                                paddingLeft:
                                                                  "var(--slds-c-card-body-spacing-inlinestart, var(--slds-c-card-body-spacing-inline-start, var(--sds-c-card-body-spacing-inline-start, var(--slds-c-card-body-spacing-inline, var(--sds-c-card-body-spacing-inline, var(--lwc-varSpacingHorizontalMedium,1rem))))))",
                                                              }}
                                                            >
                                                              <div
                                                                className="slds-file-selector slds-file-selector--integrated"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  width: "100%",
                                                                  height:
                                                                    "100%",
                                                                  position:
                                                                    "relative",
                                                                  display:
                                                                    "block",
                                                                }}
                                                              >
                                                                <div
                                                                  className="slds-file-selector__dropzone slds-file-selector__dropzone--integrated"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                    padding:
                                                                      "var(--lwc-spacingXxxSmall,0.125rem)",
                                                                    borderRadius:
                                                                      "var(--lwc-borderRadiusMedium,0.25rem)",
                                                                    placeContent:
                                                                      "center",
                                                                    margin:
                                                                      "auto",
                                                                    inset:
                                                                      "0px",
                                                                    border:
                                                                      "0px",
                                                                    display:
                                                                      "flex",
                                                                    alignItems:
                                                                      "center",
                                                                    position:
                                                                      "absolute",
                                                                    opacity: 0,
                                                                    zIndex: -1,
                                                                  }}
                                                                >
                                                                  <span
                                                                    className="slds-file-selector__body slds-file-selector__body--integrated"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      placeContent:
                                                                        "center",
                                                                      margin:
                                                                        "auto",
                                                                      background:
                                                                        "var(--slds-g-color-neutral-base-100, var(--lwc-colorBackgroundAlt,rgb(255, 255, 255)))",
                                                                      border:
                                                                        "var(--lwc-borderWidthThin,1px) solid var(--slds-g-color-border-base-1, var(--lwc-colorBorder,rgb(229, 229, 229)))",
                                                                      borderRadius:
                                                                        "var(--lwc-borderRadiusMedium,0.25rem)",
                                                                      width:
                                                                        "var(--lwc-sizeXSmall,12rem)",
                                                                      height:
                                                                        "var(--lwc-sizeXSmall,12rem)",
                                                                      display:
                                                                        "flex",
                                                                      alignItems:
                                                                        "center",
                                                                      boxShadow:
                                                                        "var(--lwc-shadowDropDown,0 2px 3px 0 rgba(0, 0, 0, 0.16))",
                                                                      flexDirection:
                                                                        "column",
                                                                    }}
                                                                  >
                                                                    <lightning-icon
                                                                      className="slds-icon-utility-upload slds-file-selector__body-icon slds-icon slds-icon-text-default slds-button__icon slds-icon_container forceIcon"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        borderRadius:
                                                                          "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                                        display:
                                                                          "inline-block",
                                                                        lineHeight:
                                                                          "var(--lwc-lineHeightReset,1)",
                                                                        backgroundColor:
                                                                          "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                                                                        width:
                                                                          "var(--lwc-squareIconMediumBoundary,2rem)",
                                                                        height:
                                                                          "var(--lwc-squareIconMediumBoundary,2rem)",
                                                                        fill: "var(--slds-c-icon-color-foreground, var(--sds-c-icon-color-foreground, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextIconInverse,rgb(255, 255, 255)))))",
                                                                      }}
                                                                    >
                                                                      <span
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <lightning-primitive-icon
                                                                          size="medium"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                          }}
                                                                        >
                                                                          <svg
                                                                            className="slds-icon"
                                                                            aria-hidden="true"
                                                                            focusable="false"
                                                                            viewBox="0 0 520 520"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              verticalAlign:
                                                                                "middle",
                                                                              width:
                                                                                "var(--lwc-squareIconMediumBoundary,2rem)",
                                                                              height:
                                                                                "var(--lwc-squareIconMediumBoundary,2rem)",
                                                                              overflow:
                                                                                "hidden",
                                                                              fill: "currentcolor",
                                                                            }}
                                                                          >
                                                                            <g
                                                                              style={{
                                                                                boxSizing:
                                                                                  "border-box",
                                                                              }}
                                                                            >
                                                                              <path
                                                                                d="M485 310h-30c-8 0-15 8-15 15v100c0 8-7 15-15 15H95c-8 0-15-7-15-15V325c0-7-7-15-15-15H35c-8 0-15 8-15 15v135a40 40 0 0040 40h400a40 40 0 0040-40V325c0-7-7-15-15-15zM270 24c-6-6-15-6-21 0L114 159c-6 6-6 15 0 21l21 21c6 6 15 6 21 0l56-56c6-6 18-2 18 7v212c0 8 6 15 14 15h30c8 0 16-8 16-15V153c0-9 10-13 17-7l56 56c6 6 15 6 21 0l21-21c6-6 6-15 0-21z"
                                                                                style={{
                                                                                  boxSizing:
                                                                                    "border-box",
                                                                                }}
                                                                              />
                                                                            </g>
                                                                          </svg>
                                                                        </lightning-primitive-icon>
                                                                      </span>
                                                                    </lightning-icon>
                                                                    <span
                                                                      className="slds-file-selector__text slds-file-selector__text--integrated slds-text-heading--medium slds-text-align--center"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        marginTop:
                                                                          "var(--lwc-spacingSmall,0.75rem)",
                                                                        fontSize:
                                                                          "var(--lwc-fontSize7,1.25rem)",
                                                                        lineHeight:
                                                                          "var(--lwc-lineHeightHeading,1.25)",
                                                                        textAlign:
                                                                          "center",
                                                                      }}
                                                                    >
                                                                      Drop Files
                                                                    </span>
                                                                  </span>
                                                                </div>
                                                                <div
                                                                  className="drag-over-body"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <lightning-input
                                                                    className="slds-form-element lightningInput"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      position:
                                                                        "relative",
                                                                      minWidth:
                                                                        "0px",
                                                                      display:
                                                                        "block",
                                                                    }}
                                                                  >
                                                                    <lightning-primitive-input-file
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                      }}
                                                                    >
                                                                      <span
                                                                        id="form-label-457"
                                                                        className="slds-form-element__label slds-assistive-text"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          overflowWrap:
                                                                            "break-word",
                                                                          hyphens:
                                                                            "auto",
                                                                          color:
                                                                            "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextLabel,rgb(68, 68, 68)))",
                                                                          fontSize:
                                                                            "var(--lwc-formLabelFontSize,0.75rem)",
                                                                          display:
                                                                            "none",
                                                                          margin:
                                                                            "-1px",
                                                                          border:
                                                                            "0px",
                                                                          padding:
                                                                            "0px",
                                                                          overflow:
                                                                            "hidden",
                                                                          whiteSpace:
                                                                            "nowrap",
                                                                          position:
                                                                            "absolute",
                                                                          marginBottom:
                                                                            "-1px",
                                                                          paddingTop:
                                                                            "0px",
                                                                          paddingRight:
                                                                            "0px",
                                                                          width:
                                                                            "1px",
                                                                          height:
                                                                            "1px",
                                                                          clip: "rect(0px, 0px, 0px, 0px)",
                                                                          textTransform:
                                                                            "none",
                                                                        }}
                                                                      >
                                                                        {" "}
                                                                      </span>
                                                                      <div
                                                                        className="slds-form-element__control"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          clear:
                                                                            "left",
                                                                          position:
                                                                            "relative",
                                                                          display:
                                                                            "block",
                                                                        }}
                                                                      >
                                                                        <div
                                                                          className="slds-file-selector slds-file-selector--images slds-file-selector_images"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                            display:
                                                                              "block",
                                                                          }}
                                                                        >
                                                                          <lightning-primitive-file-droppable-zone
                                                                            className="slds-file-selector__dropzone"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              border:
                                                                                "var(--lwc-borderWidthThin,1px) dashed var(--slds-g-color-border-base-4, var(--lwc-colorBorder,rgb(229, 229, 229)))",
                                                                              borderRadius:
                                                                                "var(--lwc-borderRadiusMedium,0.25rem)",
                                                                              placeContent:
                                                                                "center",
                                                                              margin:
                                                                                "auto",
                                                                              padding:
                                                                                "var(--lwc-spacingMedium,1rem)",
                                                                              display:
                                                                                "flex",
                                                                              alignItems:
                                                                                "center",
                                                                              maxHeight:
                                                                                "150px",
                                                                            }}
                                                                          >
                                                                            <slot
                                                                              style={{
                                                                                boxSizing:
                                                                                  "border-box",
                                                                                display:
                                                                                  "inline-block",
                                                                              }}
                                                                            >
                                                                              <input
                                                                                id="input-file-457"
                                                                                className="slds-file-selector__input slds-assistive-text"
                                                                                name="fileInput"
                                                                                type="file"
                                                                                aria-describedby="help-message-457"
                                                                                aria-labelledby="form-label-457 file-selector-label-457"
                                                                                multiple
                                                                                style={{
                                                                                  boxSizing:
                                                                                    "border-box",
                                                                                  font: "inherit",
                                                                                  color:
                                                                                    "inherit",
                                                                                  lineHeight:
                                                                                    "normal",
                                                                                  margin:
                                                                                    "-1px",
                                                                                  border:
                                                                                    "0px",
                                                                                  padding:
                                                                                    "0px",
                                                                                  overflow:
                                                                                    "hidden",
                                                                                  whiteSpace:
                                                                                    "nowrap",
                                                                                  position:
                                                                                    "absolute",
                                                                                  width:
                                                                                    "1px",
                                                                                  height:
                                                                                    "1px",
                                                                                  clip: "rect(0px, 0px, 0px, 0px)",
                                                                                  textTransform:
                                                                                    "none",
                                                                                }}
                                                                              />
                                                                              <label
                                                                                id="file-selector-label-457"
                                                                                className="slds-file-selector__body"
                                                                                htmlFor="input-file-457"
                                                                                style={{
                                                                                  boxSizing:
                                                                                    "border-box",
                                                                                  textAlign:
                                                                                    "center",
                                                                                }}
                                                                              >
                                                                                <span
                                                                                  className="slds-file-selector__button slds-button slds-button_neutral"
                                                                                  style={{
                                                                                    boxSizing:
                                                                                      "border-box",
                                                                                    backgroundPosition:
                                                                                      "initial",
                                                                                    borderColor:
                                                                                      "var(--slds-c-button-color-border, var(--sds-c-button-color-border, transparent))",
                                                                                    borderStyle:
                                                                                      "solid",
                                                                                    borderWidth:
                                                                                      "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                                                    borderRadius:
                                                                                      "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                                                    textDecoration:
                                                                                      "none",
                                                                                    whiteSpace:
                                                                                      "normal",
                                                                                    position:
                                                                                      "relative",
                                                                                    paddingTop:
                                                                                      "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                                                    paddingRight:
                                                                                      "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                                                    paddingBottom:
                                                                                      "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                                                    paddingLeft:
                                                                                      "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                                                    backgroundImage:
                                                                                      "none",
                                                                                    backgroundSize:
                                                                                      "initial",
                                                                                    backgroundRepeat:
                                                                                      "initial",
                                                                                    backgroundAttachment:
                                                                                      "initial",
                                                                                    backgroundOrigin:
                                                                                      "initial",
                                                                                    backgroundColor:
                                                                                      "var(--slds-c-button-color-background, var(--sds-c-button-color-background, transparent))",
                                                                                    backgroundClip:
                                                                                      "border-box",
                                                                                    boxShadow:
                                                                                      "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                                                    lineHeight:
                                                                                      "var(--slds-c-button-line-height, var(--sds-c-button-line-height, var(--lwc-lineHeightButton,1.875rem)))",
                                                                                    color:
                                                                                      "var(--slds-c-button-text-color, var(--sds-c-button-text-color, var(--lwc-brandAccessible,rgba(1, 118, 211, 1))))",
                                                                                    appearance:
                                                                                      "none",
                                                                                    userSelect:
                                                                                      "none",
                                                                                    transition:
                                                                                      "border 0.15s linear",
                                                                                    textAlign:
                                                                                      "center",
                                                                                    justifyContent:
                                                                                      "center",
                                                                                    display:
                                                                                      "inline-flex",
                                                                                    alignItems:
                                                                                      "center",
                                                                                  }}
                                                                                >
                                                                                  <lightning-primitive-icon
                                                                                    style={{
                                                                                      boxSizing:
                                                                                        "border-box",
                                                                                    }}
                                                                                  >
                                                                                    <svg
                                                                                      className="slds-button__icon slds-button__icon_left"
                                                                                      aria-hidden="true"
                                                                                      focusable="false"
                                                                                      viewBox="0 0 520 520"
                                                                                      style={{
                                                                                        boxSizing:
                                                                                          "border-box",
                                                                                        verticalAlign:
                                                                                          "middle",
                                                                                        width:
                                                                                          "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                                        height:
                                                                                          "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                                        fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                                                                        marginRight:
                                                                                          "var(--lwc-spacingXSmall,0.5rem)",
                                                                                        overflow:
                                                                                          "hidden",
                                                                                      }}
                                                                                    >
                                                                                      <g
                                                                                        style={{
                                                                                          boxSizing:
                                                                                            "border-box",
                                                                                        }}
                                                                                      >
                                                                                        <path
                                                                                          d="M485 310h-30c-8 0-15 8-15 15v100c0 8-7 15-15 15H95c-8 0-15-7-15-15V325c0-7-7-15-15-15H35c-8 0-15 8-15 15v135a40 40 0 0040 40h400a40 40 0 0040-40V325c0-7-7-15-15-15zM270 24c-6-6-15-6-21 0L114 159c-6 6-6 15 0 21l21 21c6 6 15 6 21 0l56-56c6-6 18-2 18 7v212c0 8 6 15 14 15h30c8 0 16-8 16-15V153c0-9 10-13 17-7l56 56c6 6 15 6 21 0l21-21c6-6 6-15 0-21z"
                                                                                          style={{
                                                                                            boxSizing:
                                                                                              "border-box",
                                                                                          }}
                                                                                        />
                                                                                      </g>
                                                                                    </svg>
                                                                                  </lightning-primitive-icon>
                                                                                  Upload
                                                                                  Files
                                                                                </span>
                                                                                <span
                                                                                  className="slds-file-selector__text slds-medium-show"
                                                                                  style={{
                                                                                    boxSizing:
                                                                                      "border-box",
                                                                                    display:
                                                                                      "block",
                                                                                    marginTop:
                                                                                      "var(--lwc-varSpacingHorizontalSmall,0.75rem)",
                                                                                  }}
                                                                                >
                                                                                  Or
                                                                                  drop
                                                                                  files
                                                                                </span>
                                                                              </label>
                                                                            </slot>
                                                                          </lightning-primitive-file-droppable-zone>
                                                                        </div>
                                                                      </div>
                                                                      <div
                                                                        id="help-message-457"
                                                                        className="slds-form-element__help"
                                                                        role="status"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          fontSize:
                                                                            "var(--lwc-fontSize2,0.75rem)",
                                                                          marginTop:
                                                                            "var(--lwc-spacingXxxSmall,0.125rem)",
                                                                          display:
                                                                            "block",
                                                                        }}
                                                                      />
                                                                    </lightning-primitive-input-file>
                                                                  </lightning-input>
                                                                  <div
                                                                    className="slds-hide"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      display:
                                                                        "none",
                                                                    }}
                                                                  >
                                                                    <force-placeholder2
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                      }}
                                                                    >
                                                                      <div
                                                                        className="body slds-grid slds-grid_vertical-align-center slds-p-around_large"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          display:
                                                                            "flex",
                                                                          alignItems:
                                                                            "center",
                                                                          alignContent:
                                                                            "center",
                                                                          padding:
                                                                            "var(--lwc-spacingLarge,1.5rem)",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                        }}
                                                                      >
                                                                        <div
                                                                          className="slds-media__figure slds-avatar slds-m-right_small"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                            overflow:
                                                                              "hidden",
                                                                            borderRadius:
                                                                              "var(--slds-c-avatar-radius-border, var(--sds-c-avatar-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                                            width:
                                                                              "var(--lwc-squareIconMediumBoundary,2rem)",
                                                                            height:
                                                                              "var(--lwc-squareIconMediumBoundary,2rem)",
                                                                            display:
                                                                              "inline-block",
                                                                            verticalAlign:
                                                                              "middle",
                                                                            lineHeight:
                                                                              "var(--lwc-lineHeightReset,1)",
                                                                            fontSize:
                                                                              "var(--lwc-fontSizeHeadingSmall,0.875rem)",
                                                                            color:
                                                                              "var(--slds-c-avatar-text-color, var(--sds-c-avatar-text-color, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))))",
                                                                            flexShrink: 0,
                                                                            marginRight:
                                                                              "var(--lwc-spacingSmall,0.75rem)",
                                                                            backgroundColor:
                                                                              "rgb(243, 243, 243)",
                                                                          }}
                                                                        />
                                                                        <div
                                                                          className="text-container"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                            width:
                                                                              "90%",
                                                                          }}
                                                                        >
                                                                          <div
                                                                            className="text slds-m-bottom_small"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              marginBottom:
                                                                                "var(--lwc-spacingSmall,0.75rem)",
                                                                              backgroundColor:
                                                                                "rgb(243, 243, 243)",
                                                                              borderRadius:
                                                                                "15rem",
                                                                              height:
                                                                                "0.5rem",
                                                                            }}
                                                                          />
                                                                          <div
                                                                            className="text text-medium"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              backgroundColor:
                                                                                "rgb(243, 243, 243)",
                                                                              borderRadius:
                                                                                "15rem",
                                                                              height:
                                                                                "0.5rem",
                                                                              width:
                                                                                "60%",
                                                                            }}
                                                                          />
                                                                        </div>
                                                                      </div>
                                                                    </force-placeholder2>
                                                                  </div>
                                                                  <ul
                                                                    className="uiAbstractList"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      padding:
                                                                        "0px",
                                                                      listStyle:
                                                                        "none",
                                                                      margin:
                                                                        "0px",
                                                                    }}
                                                                  />
                                                                  <div
                                                                    className="emptyContent hidden"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                    }}
                                                                  >
                                                                    <div
                                                                      className="emptyContentInner slds-text-align_center slds-text-align--center"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        textAlign:
                                                                          "center",
                                                                      }}
                                                                    />
                                                                  </div>
                                                                </div>
                                                              </div>
                                                            </div>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </article>
                                                </div>
                                              </div>
                                            </lst-related-list-single-aura-wrapper>
                                          </slot>
                                        </laf-progressive-container>
                                      </lst-related-list-single-container>
                                    </div>
                                    <div
                                      className="listWrapper"
                                      style={{
                                        boxSizing: "border-box",
                                        marginTop: "0.75rem",
                                      }}
                                    >
                                      <lst-related-list-single-container
                                        style={{
                                          boxSizing: "border-box",
                                          display: "block",
                                        }}
                                      >
                                        <laf-progressive-container
                                          aria-busy="false"
                                          style={{
                                            boxSizing: "border-box",
                                            display: "block",
                                          }}
                                        >
                                          <slot
                                            style={{
                                              boxSizing: "border-box",
                                            }}
                                          >
                                            <lst-related-list-single-aura-wrapper
                                              style={{
                                                boxSizing: "border-box",
                                              }}
                                            >
                                              <div
                                                style={{
                                                  boxSizing: "border-box",
                                                }}
                                              >
                                                <div
                                                  className="container forceRelatedListSingleContainer"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    width: "100%",
                                                  }}
                                                >
                                                  <article
                                                    className="slds-card slds-card_boundary slds-card_related-list-fix forceRelatedListCardDesktop"
                                                    aria-label="Contact Roles"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      display: "block",
                                                      position: "relative",
                                                      paddingTop:
                                                        "var(--slds-c-card-spacing-blockstart, var(--slds-c-card-spacing-block-start, var(--sds-c-card-spacing-block-start, var(--sds-c-card-spacing-block, 0))))",
                                                      paddingBottom:
                                                        "var(--slds-c-card-spacing-blockend, var(--slds-c-card-spacing-block-end, var(--sds-c-card-spacing-block-end, var(--sds-c-card-spacing-block, 0))))",
                                                      backgroundImage: "",
                                                      backgroundPositionX: "",
                                                      backgroundPositionY: "",
                                                      backgroundSize: "",
                                                      backgroundRepeat: "",
                                                      backgroundAttachment: "",
                                                      backgroundOrigin: "",
                                                      backgroundColor: "",
                                                      backgroundClip:
                                                        "padding-box",
                                                      color:
                                                        "var(--slds-c-card-text-color, var(--sds-c-card-text-color))",
                                                      border: "0px",
                                                      boxShadow: "none",
                                                      paddingLeft: "0px",
                                                      paddingRight: "0px",
                                                      borderWidth:
                                                        "var(--slds-c-card-sizing-border, var(--sds-c-card-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                      borderStyle: "solid",
                                                      borderColor:
                                                        "var(--slds-c-card-color-border, var(--sds-c-card-color-border, var(--slds-g-color-border-base-1, var(--lwc-cardColorBorder,rgb(201, 201, 201)))))",
                                                      borderRadius:
                                                        "var(--slds-c-card-radius-border, var(--sds-c-card-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                    }}
                                                  >
                                                    <div
                                                      className="slds-grid slds-page-header forceRelatedListCardHeader"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        padding:
                                                          "var(--lwc-varSpacingVerticalMedium,1rem) var(--lwc-varSpacingHorizontalMedium,1rem)",
                                                        backgroundImage: "",
                                                        backgroundPositionX: "",
                                                        backgroundPositionY: "",
                                                        backgroundSize: "",
                                                        backgroundRepeat: "",
                                                        backgroundAttachment:
                                                          "",
                                                        backgroundOrigin: "",
                                                        backgroundColor: "",
                                                        backgroundClip:
                                                          "padding-box",
                                                        display: "flex",
                                                        border: "0px",
                                                        borderRadius:
                                                          "var(--lwc-borderRadiusMedium,0.25rem)",
                                                        boxShadow: "none",
                                                      }}
                                                    >
                                                      <header
                                                        className="slds-media slds-media--center slds-has-flexi-truncate"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "1 1 0%",
                                                          minWidth: "0px",
                                                          display: "flex",
                                                          alignItems: "center",
                                                          height:
                                                            "var(--lwc-squareIconMedium,2rem)",
                                                        }}
                                                      >
                                                        <div
                                                          className="slds-m-right_small stencil slds-avatar slds-avatar_small"
                                                          aria-hidden="true"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            overflow: "hidden",
                                                            borderRadius:
                                                              "var(--slds-c-avatar-radius-border, var(--sds-c-avatar-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                            display:
                                                              "inline-block",
                                                            verticalAlign:
                                                              "middle",
                                                            lineHeight:
                                                              "var(--lwc-lineHeightReset,1)",
                                                            color:
                                                              "var(--slds-c-avatar-text-color, var(--sds-c-avatar-text-color, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))))",
                                                            width:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            height:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            fontSize:
                                                              "var(--lwc-fontSize1,0.625rem)",
                                                            marginRight:
                                                              "var(--lwc-spacingSmall,0.75rem)",
                                                            backgroundColor:
                                                              "var(--slds-g-color-neutral-base-80, rgb(236, 235, 234))",
                                                          }}
                                                        >
                                                          <div
                                                            className="extraSmall forceEntityIcon"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              borderRadius:
                                                                "var(--slds-s-icon-radius-border, var(--lwc-borderRadiusSmall,0.125rem))",
                                                              display: "block",
                                                              width:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              height:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              backgroundColor:
                                                                "rgb(88, 103, 232)",
                                                            }}
                                                          >
                                                            <span
                                                              className="uiImage"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                display:
                                                                  "inherit",
                                                              }}
                                                            >
                                                              <img
                                                                className="icon"
                                                                src="/assets/icons/standard/opportunity_contact_role_120.png"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  border: "0px",
                                                                  verticalAlign:
                                                                    "middle",
                                                                  maxWidth:
                                                                    "100%",
                                                                  height:
                                                                    "auto",
                                                                  display:
                                                                    "inherit",
                                                                }}
                                                              />
                                                            </span>
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="slds-media__body"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            flex: "1 1 0%",
                                                            minWidth: "0px",
                                                            marginBottom: "0px",
                                                          }}
                                                        >
                                                          <h2
                                                            id="header_1731:0"
                                                            className="slds-card__header-title"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              margin: "0px",
                                                              padding: "0px",
                                                              display: "flex",
                                                              fontSize:
                                                                "var(--slds-c-card-heading-font-size, var(--sds-c-card-heading-font-size, var(--lwc-varFontSize5,1rem)))",
                                                              fontWeight:
                                                                "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                              lineHeight:
                                                                "var(--lwc-lineHeightHeading,1.25)",
                                                              marginBottom:
                                                                "0px",
                                                            }}
                                                          >
                                                            <a
                                                              className="slds-card__header-link baseCard__header-title-container"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                backgroundColor:
                                                                  "transparent",
                                                                textDecoration:
                                                                  "none",
                                                                transition:
                                                                  "color 0.1s linear",
                                                                cursor:
                                                                  "pointer",
                                                                color:
                                                                  "inherit",
                                                                fontWeight:
                                                                  "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                                display:
                                                                  "inline-flex",
                                                                maxWidth:
                                                                  "100%",
                                                                alignItems:
                                                                  "center",
                                                              }}
                                                            >
                                                              <span
                                                                className="slds-truncate slds-m-right--xx-small"
                                                                title="Contact Roles"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                  overflow:
                                                                    "hidden",
                                                                  whiteSpace:
                                                                    "nowrap",
                                                                  maxWidth:
                                                                    "100%",
                                                                  textOverflow:
                                                                    "ellipsis",
                                                                }}
                                                              >
                                                                Contact Roles
                                                              </span>
                                                              <span
                                                                className="slds-shrink-none slds-m-right--xx-small"
                                                                title="(0)"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  flexShrink: 0,
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                }}
                                                              >
                                                                (0)
                                                              </span>
                                                            </a>
                                                          </h2>
                                                        </div>
                                                      </header>
                                                      <div
                                                        className="slds-no-flex"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "0 0 auto",
                                                        }}
                                                      >
                                                        <div
                                                          className="actionsContainer"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="slds-float--right forceDeferredDropDownAction"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              cssFloat: "right",
                                                            }}
                                                          >
                                                            <a
                                                              className="slds-button slds-button_icon-small slds-button_icon-border-filled"
                                                              aria-haspopup="true"
                                                              href="#"
                                                              role="button"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                cursor:
                                                                  "pointer",
                                                                backgroundPosition:
                                                                  "initial",
                                                                borderStyle:
                                                                  "solid",
                                                                borderWidth:
                                                                  "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                                borderRadius:
                                                                  "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                                textDecoration:
                                                                  "none",
                                                                whiteSpace:
                                                                  "normal",
                                                                position:
                                                                  "relative",
                                                                display:
                                                                  "inline-flex",
                                                                alignItems:
                                                                  "center",
                                                                paddingTop:
                                                                  "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                                paddingRight:
                                                                  "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                                paddingBottom:
                                                                  "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                                paddingLeft:
                                                                  "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                                backgroundImage:
                                                                  "none",
                                                                backgroundSize:
                                                                  "initial",
                                                                backgroundRepeat:
                                                                  "initial",
                                                                backgroundAttachment:
                                                                  "initial",
                                                                backgroundOrigin:
                                                                  "initial",
                                                                backgroundClip:
                                                                  "border-box",
                                                                boxShadow:
                                                                  "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                                appearance:
                                                                  "none",
                                                                userSelect:
                                                                  "none",
                                                                justifyContent:
                                                                  "center",
                                                                flexShrink: 0,
                                                                transition:
                                                                  "border 0.15s linear",
                                                                borderColor:
                                                                  "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                                                lineHeight:
                                                                  "var(--lwc-lineHeightReset,1)",
                                                                verticalAlign:
                                                                  "middle",
                                                                color:
                                                                  "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                                borderTopStyle:
                                                                  "",
                                                                borderTopWidth:
                                                                  "",
                                                                borderRightStyle:
                                                                  "",
                                                                borderRightWidth:
                                                                  "",
                                                                borderBottomStyle:
                                                                  "",
                                                                borderBottomWidth:
                                                                  "",
                                                                borderLeftStyle:
                                                                  "",
                                                                borderLeftWidth:
                                                                  "",
                                                                borderImageSource:
                                                                  "",
                                                                borderImageSlice:
                                                                  "",
                                                                borderImageWidth:
                                                                  "",
                                                                borderImageOutset:
                                                                  "",
                                                                borderImageRepeat:
                                                                  "",
                                                                backgroundColor:
                                                                  "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                                                                width:
                                                                  "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                                height:
                                                                  "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                                textAlign:
                                                                  "center",
                                                              }}
                                                            >
                                                              <lightning-icon
                                                                className="slds-icon-utility-down slds-icon_container"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  borderRadius:
                                                                    "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                                  display:
                                                                    "inline-block",
                                                                  backgroundColor:
                                                                    "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                                                                  lineHeight:
                                                                    "initial",
                                                                }}
                                                              >
                                                                <span
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <lightning-primitive-icon
                                                                    size="xx-small"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                    }}
                                                                  >
                                                                    <svg
                                                                      className="slds-icon slds-icon-text-default slds-icon_xx-small"
                                                                      aria-hidden="true"
                                                                      focusable="false"
                                                                      viewBox="0 0 520 520"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        verticalAlign:
                                                                          "middle",
                                                                        lineHeight:
                                                                          "var(--lwc-lineHeightReset,1)",
                                                                        overflow:
                                                                          "hidden",
                                                                        width:
                                                                          "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                        height:
                                                                          "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                        fill: "currentcolor",
                                                                      }}
                                                                    >
                                                                      <g
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <path
                                                                          d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                          }}
                                                                        />
                                                                      </g>
                                                                    </svg>
                                                                  </lightning-primitive-icon>
                                                                  <span
                                                                    className="slds-assistive-text"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      margin:
                                                                        "-1px",
                                                                      border:
                                                                        "0px",
                                                                      padding:
                                                                        "0px",
                                                                      overflow:
                                                                        "hidden",
                                                                      whiteSpace:
                                                                        "nowrap",
                                                                      position:
                                                                        "absolute",
                                                                      width:
                                                                        "1px",
                                                                      height:
                                                                        "1px",
                                                                      clip: "rect(0px, 0px, 0px, 0px)",
                                                                      textTransform:
                                                                        "none",
                                                                    }}
                                                                  >
                                                                    Show actions
                                                                    for Contact
                                                                    Roles
                                                                  </span>
                                                                </span>
                                                              </lightning-icon>
                                                            </a>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                    <div
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <div
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <div
                                                          className="previewMode SMALL forceRelatedListPreview"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="hide"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              display: "none",
                                                            }}
                                                          >
                                                            <div
                                                              className="slds-card__body"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                marginTop:
                                                                  "var(--slds-c-card-body-spacing-blockstart, var(--slds-c-card-body-spacing-block-start, var(--sds-c-card-body-spacing-block-start, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                                marginBottom:
                                                                  "var(--slds-c-card-body-spacing-blockend, var(--slds-c-card-body-spacing-block-end, var(--sds-c-card-body-spacing-block-end, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                                paddingLeft:
                                                                  "0px",
                                                                paddingRight:
                                                                  "0px",
                                                                marginLeft:
                                                                  "0px",
                                                                marginRight:
                                                                  "0px",
                                                              }}
                                                            >
                                                              <div
                                                                className="slds-hide"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  display:
                                                                    "none",
                                                                }}
                                                              >
                                                                <force-placeholder2
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <div
                                                                    className="slds-p-around_xxx-small"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      padding:
                                                                        "var(--lwc-spacingXxxSmall,0.125rem)",
                                                                    }}
                                                                  >
                                                                    <div
                                                                      className="field"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        padding:
                                                                          "0 1rem",
                                                                      }}
                                                                    >
                                                                      <div
                                                                        className="text text-primary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          height:
                                                                            "0.25rem",
                                                                          maxWidth:
                                                                            "265px",
                                                                        }}
                                                                      />
                                                                      <div
                                                                        className="text text-secondary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          height:
                                                                            "0.25rem",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          maxWidth:
                                                                            "130px",
                                                                        }}
                                                                      />
                                                                    </div>
                                                                    <div
                                                                      className="field"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        padding:
                                                                          "0 1rem",
                                                                      }}
                                                                    >
                                                                      <div
                                                                        className="text text-primary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          height:
                                                                            "0.25rem",
                                                                          maxWidth:
                                                                            "180px",
                                                                        }}
                                                                      />
                                                                      <div
                                                                        className="text text-secondary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          height:
                                                                            "0.25rem",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          maxWidth:
                                                                            "210px",
                                                                        }}
                                                                      />
                                                                    </div>
                                                                  </div>
                                                                </force-placeholder2>
                                                              </div>
                                                              <ul
                                                                className="uiAbstractList"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  padding:
                                                                    "0px",
                                                                  listStyle:
                                                                    "none",
                                                                  margin: "0px",
                                                                }}
                                                              />
                                                              <div
                                                                className="emptyContent hidden"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <div
                                                                  className="emptyContentInner slds-text-align_center slds-text-align--center"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                    textAlign:
                                                                      "center",
                                                                  }}
                                                                />
                                                              </div>
                                                            </div>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </article>
                                                </div>
                                              </div>
                                            </lst-related-list-single-aura-wrapper>
                                          </slot>
                                        </laf-progressive-container>
                                      </lst-related-list-single-container>
                                    </div>
                                    <div
                                      className="listWrapper"
                                      style={{
                                        boxSizing: "border-box",
                                        marginTop: "0.75rem",
                                      }}
                                    >
                                      <lst-related-list-single-container
                                        style={{
                                          boxSizing: "border-box",
                                          display: "block",
                                        }}
                                      >
                                        <laf-progressive-container
                                          aria-busy="false"
                                          style={{
                                            boxSizing: "border-box",
                                            display: "block",
                                          }}
                                        >
                                          <slot
                                            style={{
                                              boxSizing: "border-box",
                                            }}
                                          >
                                            <lst-related-list-single-aura-wrapper
                                              style={{
                                                boxSizing: "border-box",
                                              }}
                                            >
                                              <div
                                                style={{
                                                  boxSizing: "border-box",
                                                }}
                                              >
                                                <div
                                                  className="container forceRelatedListSingleContainer"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    width: "100%",
                                                  }}
                                                >
                                                  <article
                                                    className="slds-card slds-card_boundary slds-card_related-list-fix forceRelatedListCardDesktop"
                                                    aria-label="Partners"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      display: "block",
                                                      position: "relative",
                                                      paddingTop:
                                                        "var(--slds-c-card-spacing-blockstart, var(--slds-c-card-spacing-block-start, var(--sds-c-card-spacing-block-start, var(--sds-c-card-spacing-block, 0))))",
                                                      paddingBottom:
                                                        "var(--slds-c-card-spacing-blockend, var(--slds-c-card-spacing-block-end, var(--sds-c-card-spacing-block-end, var(--sds-c-card-spacing-block, 0))))",
                                                      backgroundImage: "",
                                                      backgroundPositionX: "",
                                                      backgroundPositionY: "",
                                                      backgroundSize: "",
                                                      backgroundRepeat: "",
                                                      backgroundAttachment: "",
                                                      backgroundOrigin: "",
                                                      backgroundColor: "",
                                                      backgroundClip:
                                                        "padding-box",
                                                      color:
                                                        "var(--slds-c-card-text-color, var(--sds-c-card-text-color))",
                                                      border: "0px",
                                                      boxShadow: "none",
                                                      paddingLeft: "0px",
                                                      paddingRight: "0px",
                                                      borderWidth:
                                                        "var(--slds-c-card-sizing-border, var(--sds-c-card-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                      borderStyle: "solid",
                                                      borderColor:
                                                        "var(--slds-c-card-color-border, var(--sds-c-card-color-border, var(--slds-g-color-border-base-1, var(--lwc-cardColorBorder,rgb(201, 201, 201)))))",
                                                      borderRadius:
                                                        "var(--slds-c-card-radius-border, var(--sds-c-card-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                    }}
                                                  >
                                                    <div
                                                      className="slds-grid slds-page-header forceRelatedListCardHeader"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        padding:
                                                          "var(--lwc-varSpacingVerticalMedium,1rem) var(--lwc-varSpacingHorizontalMedium,1rem)",
                                                        backgroundImage: "",
                                                        backgroundPositionX: "",
                                                        backgroundPositionY: "",
                                                        backgroundSize: "",
                                                        backgroundRepeat: "",
                                                        backgroundAttachment:
                                                          "",
                                                        backgroundOrigin: "",
                                                        backgroundColor: "",
                                                        backgroundClip:
                                                          "padding-box",
                                                        display: "flex",
                                                        border: "0px",
                                                        borderRadius:
                                                          "var(--lwc-borderRadiusMedium,0.25rem)",
                                                        boxShadow: "none",
                                                      }}
                                                    >
                                                      <header
                                                        className="slds-media slds-media--center slds-has-flexi-truncate"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "1 1 0%",
                                                          minWidth: "0px",
                                                          display: "flex",
                                                          alignItems: "center",
                                                          height:
                                                            "var(--lwc-squareIconMedium,2rem)",
                                                        }}
                                                      >
                                                        <div
                                                          className="slds-m-right_small stencil slds-avatar slds-avatar_small"
                                                          aria-hidden="true"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            overflow: "hidden",
                                                            borderRadius:
                                                              "var(--slds-c-avatar-radius-border, var(--sds-c-avatar-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                            display:
                                                              "inline-block",
                                                            verticalAlign:
                                                              "middle",
                                                            lineHeight:
                                                              "var(--lwc-lineHeightReset,1)",
                                                            color:
                                                              "var(--slds-c-avatar-text-color, var(--sds-c-avatar-text-color, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))))",
                                                            width:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            height:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            fontSize:
                                                              "var(--lwc-fontSize1,0.625rem)",
                                                            marginRight:
                                                              "var(--lwc-spacingSmall,0.75rem)",
                                                            backgroundColor:
                                                              "var(--slds-g-color-neutral-base-80, rgb(236, 235, 234))",
                                                          }}
                                                        >
                                                          <div
                                                            className="extraSmall forceEntityIcon"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              borderRadius:
                                                                "var(--slds-s-icon-radius-border, var(--lwc-borderRadiusSmall,0.125rem))",
                                                              display: "block",
                                                              width:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              height:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              backgroundColor:
                                                                "rgb(88, 103, 232)",
                                                            }}
                                                          >
                                                            <span
                                                              className="uiImage"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                display:
                                                                  "inherit",
                                                              }}
                                                            >
                                                              <img
                                                                className="icon"
                                                                src="/assets/downloaded/account_partner_120.png"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  border: "0px",
                                                                  verticalAlign:
                                                                    "middle",
                                                                  maxWidth:
                                                                    "100%",
                                                                  height:
                                                                    "auto",
                                                                  display:
                                                                    "inherit",
                                                                }}
                                                              />
                                                            </span>
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="slds-media__body"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            flex: "1 1 0%",
                                                            minWidth: "0px",
                                                            marginBottom: "0px",
                                                          }}
                                                        >
                                                          <h2
                                                            id="header_1836:0"
                                                            className="slds-card__header-title"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              margin: "0px",
                                                              padding: "0px",
                                                              display: "flex",
                                                              fontSize:
                                                                "var(--slds-c-card-heading-font-size, var(--sds-c-card-heading-font-size, var(--lwc-varFontSize5,1rem)))",
                                                              fontWeight:
                                                                "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                              lineHeight:
                                                                "var(--lwc-lineHeightHeading,1.25)",
                                                              marginBottom:
                                                                "0px",
                                                            }}
                                                          >
                                                            <a
                                                              className="slds-card__header-link baseCard__header-title-container"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                backgroundColor:
                                                                  "transparent",
                                                                textDecoration:
                                                                  "none",
                                                                transition:
                                                                  "color 0.1s linear",
                                                                cursor:
                                                                  "pointer",
                                                                color:
                                                                  "inherit",
                                                                fontWeight:
                                                                  "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                                display:
                                                                  "inline-flex",
                                                                maxWidth:
                                                                  "100%",
                                                                alignItems:
                                                                  "center",
                                                              }}
                                                            >
                                                              <span
                                                                className="slds-truncate slds-m-right--xx-small"
                                                                title="Partners"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                  overflow:
                                                                    "hidden",
                                                                  whiteSpace:
                                                                    "nowrap",
                                                                  maxWidth:
                                                                    "100%",
                                                                  textOverflow:
                                                                    "ellipsis",
                                                                }}
                                                              >
                                                                Partners
                                                              </span>
                                                              <span
                                                                className="slds-shrink-none slds-m-right--xx-small"
                                                                title="(0)"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  flexShrink: 0,
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                }}
                                                              >
                                                                (0)
                                                              </span>
                                                            </a>
                                                          </h2>
                                                        </div>
                                                      </header>
                                                      <div
                                                        className="slds-no-flex"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "0 0 auto",
                                                        }}
                                                      >
                                                        <div
                                                          className="actionsContainer"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="slds-float--right forceDeferredDropDownAction"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              cssFloat: "right",
                                                            }}
                                                          >
                                                            <a
                                                              className="slds-button slds-button_icon-small slds-button_icon-border-filled"
                                                              aria-haspopup="true"
                                                              href="#"
                                                              role="button"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                cursor:
                                                                  "pointer",
                                                                backgroundPosition:
                                                                  "initial",
                                                                borderStyle:
                                                                  "solid",
                                                                borderWidth:
                                                                  "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                                borderRadius:
                                                                  "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                                textDecoration:
                                                                  "none",
                                                                whiteSpace:
                                                                  "normal",
                                                                position:
                                                                  "relative",
                                                                display:
                                                                  "inline-flex",
                                                                alignItems:
                                                                  "center",
                                                                paddingTop:
                                                                  "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                                paddingRight:
                                                                  "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                                paddingBottom:
                                                                  "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                                paddingLeft:
                                                                  "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                                backgroundImage:
                                                                  "none",
                                                                backgroundSize:
                                                                  "initial",
                                                                backgroundRepeat:
                                                                  "initial",
                                                                backgroundAttachment:
                                                                  "initial",
                                                                backgroundOrigin:
                                                                  "initial",
                                                                backgroundClip:
                                                                  "border-box",
                                                                boxShadow:
                                                                  "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                                appearance:
                                                                  "none",
                                                                userSelect:
                                                                  "none",
                                                                justifyContent:
                                                                  "center",
                                                                flexShrink: 0,
                                                                transition:
                                                                  "border 0.15s linear",
                                                                borderColor:
                                                                  "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                                                lineHeight:
                                                                  "var(--lwc-lineHeightReset,1)",
                                                                verticalAlign:
                                                                  "middle",
                                                                color:
                                                                  "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                                borderTopStyle:
                                                                  "",
                                                                borderTopWidth:
                                                                  "",
                                                                borderRightStyle:
                                                                  "",
                                                                borderRightWidth:
                                                                  "",
                                                                borderBottomStyle:
                                                                  "",
                                                                borderBottomWidth:
                                                                  "",
                                                                borderLeftStyle:
                                                                  "",
                                                                borderLeftWidth:
                                                                  "",
                                                                borderImageSource:
                                                                  "",
                                                                borderImageSlice:
                                                                  "",
                                                                borderImageWidth:
                                                                  "",
                                                                borderImageOutset:
                                                                  "",
                                                                borderImageRepeat:
                                                                  "",
                                                                backgroundColor:
                                                                  "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                                                                width:
                                                                  "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                                height:
                                                                  "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                                textAlign:
                                                                  "center",
                                                              }}
                                                            >
                                                              <lightning-icon
                                                                className="slds-icon-utility-down slds-icon_container"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  borderRadius:
                                                                    "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                                  display:
                                                                    "inline-block",
                                                                  backgroundColor:
                                                                    "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                                                                  lineHeight:
                                                                    "initial",
                                                                }}
                                                              >
                                                                <span
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <lightning-primitive-icon
                                                                    size="xx-small"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                    }}
                                                                  >
                                                                    <svg
                                                                      className="slds-icon slds-icon-text-default slds-icon_xx-small"
                                                                      aria-hidden="true"
                                                                      focusable="false"
                                                                      viewBox="0 0 520 520"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        verticalAlign:
                                                                          "middle",
                                                                        lineHeight:
                                                                          "var(--lwc-lineHeightReset,1)",
                                                                        overflow:
                                                                          "hidden",
                                                                        width:
                                                                          "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                        height:
                                                                          "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                        fill: "currentcolor",
                                                                      }}
                                                                    >
                                                                      <g
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <path
                                                                          d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                          }}
                                                                        />
                                                                      </g>
                                                                    </svg>
                                                                  </lightning-primitive-icon>
                                                                  <span
                                                                    className="slds-assistive-text"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      margin:
                                                                        "-1px",
                                                                      border:
                                                                        "0px",
                                                                      padding:
                                                                        "0px",
                                                                      overflow:
                                                                        "hidden",
                                                                      whiteSpace:
                                                                        "nowrap",
                                                                      position:
                                                                        "absolute",
                                                                      width:
                                                                        "1px",
                                                                      height:
                                                                        "1px",
                                                                      clip: "rect(0px, 0px, 0px, 0px)",
                                                                      textTransform:
                                                                        "none",
                                                                    }}
                                                                  >
                                                                    Show actions
                                                                    for Partners
                                                                  </span>
                                                                </span>
                                                              </lightning-icon>
                                                            </a>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                    <div
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <div
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <div
                                                          className="previewMode SMALL forceRelatedListPreview"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="hide"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              display: "none",
                                                            }}
                                                          >
                                                            <div
                                                              className="slds-card__body"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                marginTop:
                                                                  "var(--slds-c-card-body-spacing-blockstart, var(--slds-c-card-body-spacing-block-start, var(--sds-c-card-body-spacing-block-start, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                                marginBottom:
                                                                  "var(--slds-c-card-body-spacing-blockend, var(--slds-c-card-body-spacing-block-end, var(--sds-c-card-body-spacing-block-end, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                                paddingLeft:
                                                                  "0px",
                                                                paddingRight:
                                                                  "0px",
                                                                marginLeft:
                                                                  "0px",
                                                                marginRight:
                                                                  "0px",
                                                              }}
                                                            >
                                                              <div
                                                                className="slds-hide"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  display:
                                                                    "none",
                                                                }}
                                                              >
                                                                <force-placeholder2
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <div
                                                                    className="slds-p-around_xxx-small"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      padding:
                                                                        "var(--lwc-spacingXxxSmall,0.125rem)",
                                                                    }}
                                                                  >
                                                                    <div
                                                                      className="field"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        padding:
                                                                          "0 1rem",
                                                                      }}
                                                                    >
                                                                      <div
                                                                        className="text text-primary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          height:
                                                                            "0.25rem",
                                                                          maxWidth:
                                                                            "265px",
                                                                        }}
                                                                      />
                                                                      <div
                                                                        className="text text-secondary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          height:
                                                                            "0.25rem",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          maxWidth:
                                                                            "130px",
                                                                        }}
                                                                      />
                                                                    </div>
                                                                    <div
                                                                      className="field"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        padding:
                                                                          "0 1rem",
                                                                      }}
                                                                    >
                                                                      <div
                                                                        className="text text-primary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          height:
                                                                            "0.25rem",
                                                                          maxWidth:
                                                                            "180px",
                                                                        }}
                                                                      />
                                                                      <div
                                                                        className="text text-secondary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          height:
                                                                            "0.25rem",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          maxWidth:
                                                                            "210px",
                                                                        }}
                                                                      />
                                                                    </div>
                                                                  </div>
                                                                </force-placeholder2>
                                                              </div>
                                                              <ul
                                                                className="uiAbstractList"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  padding:
                                                                    "0px",
                                                                  listStyle:
                                                                    "none",
                                                                  margin: "0px",
                                                                }}
                                                              />
                                                              <div
                                                                className="emptyContent hidden"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <div
                                                                  className="emptyContentInner slds-text-align_center slds-text-align--center"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                    textAlign:
                                                                      "center",
                                                                  }}
                                                                />
                                                              </div>
                                                            </div>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </article>
                                                </div>
                                              </div>
                                            </lst-related-list-single-aura-wrapper>
                                          </slot>
                                        </laf-progressive-container>
                                      </lst-related-list-single-container>
                                    </div>
                                    <div
                                      className="listWrapper"
                                      style={{
                                        boxSizing: "border-box",
                                        marginTop: "0.75rem",
                                      }}
                                    >
                                      <lst-related-list-single-container
                                        style={{
                                          boxSizing: "border-box",
                                          display: "block",
                                        }}
                                      >
                                        <laf-progressive-container
                                          aria-busy="false"
                                          style={{
                                            boxSizing: "border-box",
                                            display: "block",
                                          }}
                                        >
                                          <slot
                                            style={{
                                              boxSizing: "border-box",
                                            }}
                                          >
                                            <lst-related-list-single-aura-wrapper
                                              style={{
                                                boxSizing: "border-box",
                                              }}
                                            >
                                              <div
                                                style={{
                                                  boxSizing: "border-box",
                                                }}
                                              >
                                                <div
                                                  className="container forceRelatedListSingleContainer"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    width: "100%",
                                                  }}
                                                >
                                                  <article
                                                    className="slds-card slds-card_boundary slds-card_related-list-fix headerBottomBorder forceRelatedListCardDesktop"
                                                    aria-label="Stage History"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      display: "block",
                                                      position: "relative",
                                                      paddingTop:
                                                        "var(--slds-c-card-spacing-blockstart, var(--slds-c-card-spacing-block-start, var(--sds-c-card-spacing-block-start, var(--sds-c-card-spacing-block, 0))))",
                                                      paddingBottom:
                                                        "var(--slds-c-card-spacing-blockend, var(--slds-c-card-spacing-block-end, var(--sds-c-card-spacing-block-end, var(--sds-c-card-spacing-block, 0))))",
                                                      backgroundImage: "",
                                                      backgroundPositionX: "",
                                                      backgroundPositionY: "",
                                                      backgroundSize: "",
                                                      backgroundRepeat: "",
                                                      backgroundAttachment: "",
                                                      backgroundOrigin: "",
                                                      backgroundColor: "",
                                                      backgroundClip:
                                                        "padding-box",
                                                      color:
                                                        "var(--slds-c-card-text-color, var(--sds-c-card-text-color))",
                                                      border: "0px",
                                                      boxShadow: "none",
                                                      paddingLeft: "0px",
                                                      paddingRight: "0px",
                                                      borderWidth:
                                                        "var(--slds-c-card-sizing-border, var(--sds-c-card-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                      borderStyle: "solid",
                                                      borderColor:
                                                        "var(--slds-c-card-color-border, var(--sds-c-card-color-border, var(--slds-g-color-border-base-1, var(--lwc-cardColorBorder,rgb(201, 201, 201)))))",
                                                      borderRadius:
                                                        "var(--slds-c-card-radius-border, var(--sds-c-card-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                    }}
                                                  >
                                                    <div
                                                      className="slds-grid slds-page-header forceRelatedListCardHeader"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        padding:
                                                          "var(--lwc-varSpacingVerticalMedium,1rem) var(--lwc-varSpacingHorizontalMedium,1rem)",
                                                        backgroundImage: "",
                                                        backgroundPositionX: "",
                                                        backgroundPositionY: "",
                                                        backgroundSize: "",
                                                        backgroundRepeat: "",
                                                        backgroundAttachment:
                                                          "",
                                                        backgroundOrigin: "",
                                                        backgroundColor: "",
                                                        backgroundClip:
                                                          "padding-box",
                                                        display: "flex",
                                                        border: "0px",
                                                        borderRadius:
                                                          "var(--lwc-borderRadiusMedium,0.25rem)",
                                                        boxShadow: "none",
                                                        borderBottom:
                                                          "var(--slds-g-sizing-border-1, var(--lwc-borderWidthThin,1px)) solid var(--slds-s-container-color-border, var(--lwc-cardColorBorder,rgb(201, 201, 201)))",
                                                        borderBottomLeftRadius:
                                                          "0px",
                                                        borderBottomRightRadius:
                                                          "0px",
                                                      }}
                                                    >
                                                      <header
                                                        className="slds-media slds-media--center slds-has-flexi-truncate"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "1 1 0%",
                                                          minWidth: "0px",
                                                          display: "flex",
                                                          alignItems: "center",
                                                          height:
                                                            "var(--lwc-squareIconMedium,2rem)",
                                                        }}
                                                      >
                                                        <div
                                                          className="slds-m-right_small stencil slds-avatar slds-avatar_small"
                                                          aria-hidden="true"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            overflow: "hidden",
                                                            borderRadius:
                                                              "var(--slds-c-avatar-radius-border, var(--sds-c-avatar-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                            display:
                                                              "inline-block",
                                                            verticalAlign:
                                                              "middle",
                                                            lineHeight:
                                                              "var(--lwc-lineHeightReset,1)",
                                                            color:
                                                              "var(--slds-c-avatar-text-color, var(--sds-c-avatar-text-color, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))))",
                                                            width:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            height:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            fontSize:
                                                              "var(--lwc-fontSize1,0.625rem)",
                                                            marginRight:
                                                              "var(--lwc-spacingSmall,0.75rem)",
                                                            backgroundColor:
                                                              "var(--slds-g-color-neutral-base-80, rgb(236, 235, 234))",
                                                          }}
                                                        >
                                                          <div
                                                            className="extraSmall forceEntityIcon"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              borderRadius:
                                                                "var(--slds-s-icon-radius-border, var(--lwc-borderRadiusSmall,0.125rem))",
                                                              display: "block",
                                                              width:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              height:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              backgroundColor:
                                                                "rgb(6, 165, 154)",
                                                            }}
                                                          >
                                                            <span
                                                              className="uiImage"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                display:
                                                                  "inherit",
                                                              }}
                                                            >
                                                              <img
                                                                className="icon"
                                                                src="/assets/icons/standard/custom_120.png"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  border: "0px",
                                                                  verticalAlign:
                                                                    "middle",
                                                                  maxWidth:
                                                                    "100%",
                                                                  height:
                                                                    "auto",
                                                                  display:
                                                                    "inherit",
                                                                }}
                                                              />
                                                            </span>
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="slds-media__body"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            flex: "1 1 0%",
                                                            minWidth: "0px",
                                                            marginBottom: "0px",
                                                          }}
                                                        >
                                                          <h2
                                                            id="header_2129:0"
                                                            className="slds-card__header-title"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              margin: "0px",
                                                              padding: "0px",
                                                              display: "flex",
                                                              fontSize:
                                                                "var(--slds-c-card-heading-font-size, var(--sds-c-card-heading-font-size, var(--lwc-varFontSize5,1rem)))",
                                                              fontWeight:
                                                                "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                              lineHeight:
                                                                "var(--lwc-lineHeightHeading,1.25)",
                                                              marginBottom:
                                                                "0px",
                                                            }}
                                                          >
                                                            <a
                                                              className="slds-card__header-link baseCard__header-title-container"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                backgroundColor:
                                                                  "transparent",
                                                                textDecoration:
                                                                  "none",
                                                                transition:
                                                                  "color 0.1s linear",
                                                                cursor:
                                                                  "pointer",
                                                                color:
                                                                  "inherit",
                                                                fontWeight:
                                                                  "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                                display:
                                                                  "inline-flex",
                                                                maxWidth:
                                                                  "100%",
                                                                alignItems:
                                                                  "center",
                                                              }}
                                                            >
                                                              <span
                                                                className="slds-truncate slds-m-right--xx-small"
                                                                title="Stage History"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                  overflow:
                                                                    "hidden",
                                                                  whiteSpace:
                                                                    "nowrap",
                                                                  maxWidth:
                                                                    "100%",
                                                                  textOverflow:
                                                                    "ellipsis",
                                                                }}
                                                              >
                                                                Stage History
                                                              </span>
                                                              <span
                                                                className="slds-shrink-none slds-m-right--xx-small"
                                                                title="(1)"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  flexShrink: 0,
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                }}
                                                              >
                                                                (1)
                                                              </span>
                                                            </a>
                                                          </h2>
                                                        </div>
                                                      </header>
                                                      <div
                                                        className="slds-no-flex"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "0 0 auto",
                                                        }}
                                                      >
                                                        <div
                                                          className="actionsContainer"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="slds-float--right forceDeferredDropDownAction"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              cssFloat: "right",
                                                            }}
                                                          >
                                                            <a
                                                              className="slds-button slds-button_icon-small slds-button_icon-border-filled"
                                                              aria-haspopup="true"
                                                              href="#"
                                                              role="button"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                cursor:
                                                                  "pointer",
                                                                backgroundPosition:
                                                                  "initial",
                                                                borderStyle:
                                                                  "solid",
                                                                borderWidth:
                                                                  "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                                borderRadius:
                                                                  "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                                textDecoration:
                                                                  "none",
                                                                whiteSpace:
                                                                  "normal",
                                                                position:
                                                                  "relative",
                                                                display:
                                                                  "inline-flex",
                                                                alignItems:
                                                                  "center",
                                                                paddingTop:
                                                                  "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                                paddingRight:
                                                                  "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                                paddingBottom:
                                                                  "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                                paddingLeft:
                                                                  "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                                backgroundImage:
                                                                  "none",
                                                                backgroundSize:
                                                                  "initial",
                                                                backgroundRepeat:
                                                                  "initial",
                                                                backgroundAttachment:
                                                                  "initial",
                                                                backgroundOrigin:
                                                                  "initial",
                                                                backgroundClip:
                                                                  "border-box",
                                                                boxShadow:
                                                                  "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                                appearance:
                                                                  "none",
                                                                userSelect:
                                                                  "none",
                                                                justifyContent:
                                                                  "center",
                                                                flexShrink: 0,
                                                                transition:
                                                                  "border 0.15s linear",
                                                                borderColor:
                                                                  "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                                                lineHeight:
                                                                  "var(--lwc-lineHeightReset,1)",
                                                                verticalAlign:
                                                                  "middle",
                                                                color:
                                                                  "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                                borderTopStyle:
                                                                  "",
                                                                borderTopWidth:
                                                                  "",
                                                                borderRightStyle:
                                                                  "",
                                                                borderRightWidth:
                                                                  "",
                                                                borderBottomStyle:
                                                                  "",
                                                                borderBottomWidth:
                                                                  "",
                                                                borderLeftStyle:
                                                                  "",
                                                                borderLeftWidth:
                                                                  "",
                                                                borderImageSource:
                                                                  "",
                                                                borderImageSlice:
                                                                  "",
                                                                borderImageWidth:
                                                                  "",
                                                                borderImageOutset:
                                                                  "",
                                                                borderImageRepeat:
                                                                  "",
                                                                backgroundColor:
                                                                  "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                                                                width:
                                                                  "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                                height:
                                                                  "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                                textAlign:
                                                                  "center",
                                                              }}
                                                            >
                                                              <lightning-icon
                                                                className="slds-icon-utility-down slds-icon_container"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  borderRadius:
                                                                    "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                                  display:
                                                                    "inline-block",
                                                                  backgroundColor:
                                                                    "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                                                                  lineHeight:
                                                                    "initial",
                                                                }}
                                                              >
                                                                <span
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <lightning-primitive-icon
                                                                    size="xx-small"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                    }}
                                                                  >
                                                                    <svg
                                                                      className="slds-icon slds-icon-text-default slds-icon_xx-small"
                                                                      aria-hidden="true"
                                                                      focusable="false"
                                                                      viewBox="0 0 520 520"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        verticalAlign:
                                                                          "middle",
                                                                        lineHeight:
                                                                          "var(--lwc-lineHeightReset,1)",
                                                                        overflow:
                                                                          "hidden",
                                                                        width:
                                                                          "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                        height:
                                                                          "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                        fill: "currentcolor",
                                                                      }}
                                                                    >
                                                                      <g
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <path
                                                                          d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                          }}
                                                                        />
                                                                      </g>
                                                                    </svg>
                                                                  </lightning-primitive-icon>
                                                                  <span
                                                                    className="slds-assistive-text"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      margin:
                                                                        "-1px",
                                                                      border:
                                                                        "0px",
                                                                      padding:
                                                                        "0px",
                                                                      overflow:
                                                                        "hidden",
                                                                      whiteSpace:
                                                                        "nowrap",
                                                                      position:
                                                                        "absolute",
                                                                      width:
                                                                        "1px",
                                                                      height:
                                                                        "1px",
                                                                      clip: "rect(0px, 0px, 0px, 0px)",
                                                                      textTransform:
                                                                        "none",
                                                                    }}
                                                                  >
                                                                    Show actions
                                                                    for Stage
                                                                    History
                                                                  </span>
                                                                </span>
                                                              </lightning-icon>
                                                            </a>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                    <div
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <div
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <div
                                                          className="previewMode SMALL forceRelatedListPreview"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                            }}
                                                          >
                                                            <div
                                                              className="slds-card__body"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                marginTop:
                                                                  "var(--slds-c-card-body-spacing-blockstart, var(--slds-c-card-body-spacing-block-start, var(--sds-c-card-body-spacing-block-start, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                                marginBottom:
                                                                  "var(--slds-c-card-body-spacing-blockend, var(--slds-c-card-body-spacing-block-end, var(--sds-c-card-body-spacing-block-end, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                                paddingLeft:
                                                                  "0px",
                                                                paddingRight:
                                                                  "0px",
                                                                marginLeft:
                                                                  "0px",
                                                                marginRight:
                                                                  "0px",
                                                              }}
                                                            >
                                                              <div
                                                                className="slds-hide"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  display:
                                                                    "none",
                                                                }}
                                                              >
                                                                <force-placeholder2
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <div
                                                                    className="slds-p-around_xxx-small"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      padding:
                                                                        "var(--lwc-spacingXxxSmall,0.125rem)",
                                                                    }}
                                                                  >
                                                                    <div
                                                                      className="field"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        padding:
                                                                          "0 1rem",
                                                                      }}
                                                                    >
                                                                      <div
                                                                        className="text text-primary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          height:
                                                                            "0.25rem",
                                                                          maxWidth:
                                                                            "265px",
                                                                        }}
                                                                      />
                                                                      <div
                                                                        className="text text-secondary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          height:
                                                                            "0.25rem",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          maxWidth:
                                                                            "130px",
                                                                        }}
                                                                      />
                                                                    </div>
                                                                    <div
                                                                      className="field"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        padding:
                                                                          "0 1rem",
                                                                      }}
                                                                    >
                                                                      <div
                                                                        className="text text-primary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          height:
                                                                            "0.25rem",
                                                                          maxWidth:
                                                                            "180px",
                                                                        }}
                                                                      />
                                                                      <div
                                                                        className="text text-secondary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          height:
                                                                            "0.25rem",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          maxWidth:
                                                                            "210px",
                                                                        }}
                                                                      />
                                                                    </div>
                                                                  </div>
                                                                </force-placeholder2>
                                                              </div>
                                                              <ul
                                                                className="uiAbstractList"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  padding:
                                                                    "0px",
                                                                  listStyle:
                                                                    "none",
                                                                  margin: "0px",
                                                                }}
                                                              >
                                                                <li
                                                                  className="slds-var-p-horizontal_medium slds-var-p-vertical_xx-small desktop forceReadOnlyRelatedListStencil forceRecordLayout"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                    paddingLeft:
                                                                      "var(--lwc-varSpacingHorizontalMedium,1rem)",
                                                                    paddingRight:
                                                                      "var(--lwc-varSpacingHorizontalMedium,1rem)",
                                                                    paddingTop:
                                                                      "var(--lwc-varSpacingVerticalXxSmall,0.25rem)",
                                                                    paddingBottom:
                                                                      "var(--lwc-varSpacingVerticalXxSmall,0.25rem)",
                                                                    listStyle:
                                                                      "none",
                                                                    borderBottom:
                                                                      "1px solid var(--lwc-colorBorderSeparator,rgb(243, 243, 243))",
                                                                    display:
                                                                      "inline-block",
                                                                    verticalAlign:
                                                                      "top",
                                                                    width:
                                                                      "100%",
                                                                  }}
                                                                >
                                                                  <div
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                    }}
                                                                  >
                                                                    <ul
                                                                      className="slds-truncate"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        padding:
                                                                          "0px",
                                                                        overflow:
                                                                          "hidden",
                                                                        whiteSpace:
                                                                          "nowrap",
                                                                        maxWidth:
                                                                          "100%",
                                                                        textOverflow:
                                                                          "ellipsis",
                                                                        listStyle:
                                                                          "none",
                                                                        margin:
                                                                          "0px",
                                                                      }}
                                                                    >
                                                                      <li
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <div
                                                                          className="slds-list--horizontal forceListRecordItem"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                            display:
                                                                              "flex",
                                                                          }}
                                                                        >
                                                                          <div
                                                                            className="slds-item_label slds-truncate recordCell recordCellLabel slds-text-color_weak"
                                                                            title="Stage:"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              color:
                                                                                "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                                                                              width:
                                                                                "30%",
                                                                              paddingRight:
                                                                                "var(--lwc-spacingSmall,0.75rem)",
                                                                            }}
                                                                          >
                                                                            Stage:
                                                                          </div>
                                                                          <div
                                                                            className="slds-item_detail slds-truncate recordCell recordCellDetail"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              width:
                                                                                "70%",
                                                                            }}
                                                                          >
                                                                            <span
                                                                              style={{
                                                                                boxSizing:
                                                                                  "border-box",
                                                                              }}
                                                                            >
                                                                              {allVariableData
                                                                                ?.opportunityList
                                                                                ?.length
                                                                                ? allVariableData.opportunityList.slice(
                                                                                    -1
                                                                                  )[0]
                                                                                    .Stage
                                                                                : "Qualification"}
                                                                            </span>
                                                                          </div>
                                                                        </div>
                                                                      </li>
                                                                      <li
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <div
                                                                          className="slds-list--horizontal forceListRecordItem"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                            display:
                                                                              "flex",
                                                                          }}
                                                                        >
                                                                          <div
                                                                            className="slds-item_label slds-truncate recordCell recordCellLabel slds-text-color_weak"
                                                                            title="Amount:"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              color:
                                                                                "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                                                                              width:
                                                                                "30%",
                                                                              paddingRight:
                                                                                "var(--lwc-spacingSmall,0.75rem)",
                                                                            }}
                                                                          >
                                                                            Amount:
                                                                          </div>
                                                                          <div
                                                                            className="slds-item_detail slds-truncate recordCell recordCellDetail"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              width:
                                                                                "70%",
                                                                            }}
                                                                          >
                                                                            <span
                                                                              className="forceOutputCurrency"
                                                                              style={{
                                                                                boxSizing:
                                                                                  "border-box",
                                                                              }}
                                                                            >
                                                                              {allVariableData
                                                                                ?.opportunityList
                                                                                ?.length &&
                                                                              allVariableData.opportunityList.slice(
                                                                                -1
                                                                              )[0]
                                                                                .Amount
                                                                                ? `$${parseFloat(
                                                                                    allVariableData.opportunityList.slice(
                                                                                      -1
                                                                                    )[0]
                                                                                      .Amount
                                                                                  ).toLocaleString(
                                                                                    undefined,
                                                                                    {
                                                                                      minimumFractionDigits: 2,
                                                                                      maximumFractionDigits: 2,
                                                                                    }
                                                                                  )}`
                                                                                : "$0.00"}
                                                                            </span>
                                                                          </div>
                                                                        </div>
                                                                      </li>
                                                                      <li
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <div
                                                                          className="slds-list--horizontal forceListRecordItem"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                            display:
                                                                              "flex",
                                                                          }}
                                                                        >
                                                                          <div
                                                                            className="slds-item_label slds-truncate recordCell recordCellLabel slds-text-color_weak"
                                                                            title="Probability (%):"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              color:
                                                                                "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                                                                              width:
                                                                                "30%",
                                                                              paddingRight:
                                                                                "var(--lwc-spacingSmall,0.75rem)",
                                                                            }}
                                                                          >
                                                                            Probability
                                                                            (%):
                                                                          </div>
                                                                          <div
                                                                            className="slds-item_detail slds-truncate recordCell recordCellDetail"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              width:
                                                                                "70%",
                                                                            }}
                                                                          >
                                                                            <span
                                                                              className="uiOutputPercent"
                                                                              style={{
                                                                                boxSizing:
                                                                                  "border-box",
                                                                              }}
                                                                            >
                                                                              {allVariableData
                                                                                ?.opportunityList
                                                                                ?.length &&
                                                                              allVariableData.opportunityList.slice(
                                                                                -1
                                                                              )[0]
                                                                                .Probability
                                                                                ? `${
                                                                                    allVariableData.opportunityList.slice(
                                                                                      -1
                                                                                    )[0]
                                                                                      .Probability
                                                                                  }%`
                                                                                : "0%"}
                                                                            </span>
                                                                          </div>
                                                                        </div>
                                                                      </li>
                                                                      <li
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <div
                                                                          className="slds-list--horizontal forceListRecordItem"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                            display:
                                                                              "flex",
                                                                          }}
                                                                        >
                                                                          <div
                                                                            className="slds-item_label slds-truncate recordCell recordCellLabel slds-text-color_weak"
                                                                            title="Expected Revenue:"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              color:
                                                                                "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                                                                              width:
                                                                                "30%",
                                                                              paddingRight:
                                                                                "var(--lwc-spacingSmall,0.75rem)",
                                                                            }}
                                                                          >
                                                                            Expected
                                                                            Revenue:
                                                                          </div>
                                                                          <div
                                                                            className="slds-item_detail slds-truncate recordCell recordCellDetail"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              width:
                                                                                "70%",
                                                                            }}
                                                                          >
                                                                            <span
                                                                              className="forceOutputCurrency"
                                                                              style={{
                                                                                boxSizing:
                                                                                  "border-box",
                                                                              }}
                                                                            >
                                                                              $100.00
                                                                            </span>
                                                                          </div>
                                                                        </div>
                                                                      </li>
                                                                      <li
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <div
                                                                          className="slds-list--horizontal forceListRecordItem"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                            display:
                                                                              "flex",
                                                                          }}
                                                                        >
                                                                          <div
                                                                            className="slds-item_label slds-truncate recordCell recordCellLabel slds-text-color_weak"
                                                                            title="Close Date:"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              color:
                                                                                "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                                                                              width:
                                                                                "30%",
                                                                              paddingRight:
                                                                                "var(--lwc-spacingSmall,0.75rem)",
                                                                            }}
                                                                          >
                                                                            Close
                                                                            Date:
                                                                          </div>
                                                                          <div
                                                                            className="slds-item_detail slds-truncate recordCell recordCellDetail"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              width:
                                                                                "70%",
                                                                            }}
                                                                          >
                                                                            <span
                                                                              className="uiOutputDate"
                                                                              style={{
                                                                                boxSizing:
                                                                                  "border-box",
                                                                              }}
                                                                            >
                                                                              {allVariableData
                                                                                ?.opportunityList
                                                                                ?.length
                                                                                ? allVariableData.opportunityList.slice(
                                                                                    -1
                                                                                  )[0]
                                                                                    .CloseDate
                                                                                : ""}
                                                                            </span>
                                                                          </div>
                                                                        </div>
                                                                      </li>
                                                                      <li
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <div
                                                                          className="slds-list--horizontal forceListRecordItem"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                            display:
                                                                              "flex",
                                                                          }}
                                                                        >
                                                                          <div
                                                                            className="slds-item_label slds-truncate recordCell recordCellLabel slds-text-color_weak"
                                                                            title="Last Modified By:"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              color:
                                                                                "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                                                                              width:
                                                                                "30%",
                                                                              paddingRight:
                                                                                "var(--lwc-spacingSmall,0.75rem)",
                                                                            }}
                                                                          >
                                                                            Last
                                                                            Modified
                                                                            By:
                                                                          </div>
                                                                          <div
                                                                            className="slds-item_detail slds-truncate recordCell recordCellDetail"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              width:
                                                                                "70%",
                                                                            }}
                                                                          >
                                                                            <a
                                                                              className="outputLookupLink slds-truncate outputLookupLink-005aj00000EJT0jAAH-62:2110;a forceOutputLookup"
                                                                              rel="noreferrer"
                                                                              target="_blank"
                                                                              title="seven steven"
                                                                              style={{
                                                                                boxSizing:
                                                                                  "border-box",
                                                                                backgroundColor:
                                                                                  "transparent",
                                                                                textDecoration:
                                                                                  "none",
                                                                                transition:
                                                                                  "color 0.1s linear",
                                                                                color:
                                                                                  "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                                                                                cursor:
                                                                                  "pointer",
                                                                                overflow:
                                                                                  "hidden",
                                                                                whiteSpace:
                                                                                  "nowrap",
                                                                                maxWidth:
                                                                                  "100%",
                                                                                textOverflow:
                                                                                  "ellipsis",
                                                                              }}
                                                                            >
                                                                              seven
                                                                              steven
                                                                            </a>
                                                                          </div>
                                                                        </div>
                                                                      </li>
                                                                      <li
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                        }}
                                                                      >
                                                                        <div
                                                                          className="slds-list--horizontal forceListRecordItem"
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                            display:
                                                                              "flex",
                                                                          }}
                                                                        >
                                                                          <div
                                                                            className="slds-item_label slds-truncate recordCell recordCellLabel slds-text-color_weak"
                                                                            title="Last Modified:"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              color:
                                                                                "var(--slds-g-color-neutral-base-30, var(--lwc-colorTextWeak,rgb(68, 68, 68)))",
                                                                              width:
                                                                                "30%",
                                                                              paddingRight:
                                                                                "var(--lwc-spacingSmall,0.75rem)",
                                                                            }}
                                                                          >
                                                                            Last
                                                                            Modified:
                                                                          </div>
                                                                          <div
                                                                            className="slds-item_detail slds-truncate recordCell recordCellDetail"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              overflow:
                                                                                "hidden",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              maxWidth:
                                                                                "100%",
                                                                              textOverflow:
                                                                                "ellipsis",
                                                                              width:
                                                                                "70%",
                                                                            }}
                                                                          >
                                                                            <span
                                                                              className="uiOutputDateTime"
                                                                              style={{
                                                                                boxSizing:
                                                                                  "border-box",
                                                                              }}
                                                                            >
                                                                              5/16/2025,
                                                                              8:41
                                                                              AM
                                                                            </span>
                                                                          </div>
                                                                        </div>
                                                                      </li>
                                                                    </ul>
                                                                  </div>
                                                                </li>
                                                              </ul>
                                                              <div
                                                                className="emptyContent hidden"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <div
                                                                  className="emptyContentInner slds-text-align_center slds-text-align--center"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                    textAlign:
                                                                      "center",
                                                                  }}
                                                                />
                                                              </div>
                                                            </div>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                    <a
                                                      style={{
                                                        boxSizing: "border-box",
                                                        backgroundColor:
                                                          "transparent",
                                                        textDecoration: "none",
                                                        transition:
                                                          "color 0.1s linear",
                                                        color:
                                                          "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                                                        cursor: "pointer",
                                                      }}
                                                    >
                                                      <div
                                                        className="slds-card__footer"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          textAlign:
                                                            "var(--slds-c-card-footer-text-align, var(--sds-c-card-footer-text-align, var(--lwc-cardFooterTextAlign,center)))",
                                                          fontSize:
                                                            "var(--slds-c-card-footer-font-size, var(--sds-c-card-footer-font-size, var(--lwc-fontSize3,0.8125rem)))",
                                                          borderTopWidth:
                                                            "var(--slds-c-card-footer-sizing-border, var(--sds-c-card-footer-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                          borderTopStyle:
                                                            "solid",
                                                          borderTopColor:
                                                            "var(--slds-c-card-footer-color-border, var(--sds-c-card-footer-color-border, var(--slds-g-color-border-base-1, var(--lwc-cardFooterColorBorder,rgb(201, 201, 201)))))",
                                                          marginTop: "-1px",
                                                          marginLeft: "0px",
                                                          marginRight: "0px",
                                                          paddingTop:
                                                            "var(--slds-c-card-footer-spacing-blockstart, var(--slds-c-card-footer-spacing-block-start, var(--sds-c-card-footer-spacing-block-start, var(--slds-c-card-footer-spacing-block, var(--sds-c-card-footer-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                          paddingRight:
                                                            "var(--slds-c-card-footer-spacing-inlineend, var(--slds-c-card-footer-spacing-inline-end, var(--sds-c-card-footer-spacing-inline-end, var(--slds-c-card-footer-spacing-inline, var(--sds-c-card-footer-spacing-inline, var(--lwc-varSpacingHorizontalMedium,1rem))))))",
                                                          paddingBottom:
                                                            "var(--slds-c-card-footer-spacing-blockend, var(--slds-c-card-footer-spacing-block-end, var(--sds-c-card-footer-spacing-block-end, var(--slds-c-card-footer-spacing-block, var(--sds-c-card-footer-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                          paddingLeft:
                                                            "var(--slds-c-card-footer-spacing-inlinestart, var(--slds-c-card-footer-spacing-inline-start, var(--sds-c-card-footer-spacing-inline-start, var(--slds-c-card-footer-spacing-inline, var(--sds-c-card-footer-spacing-inline, var(--lwc-varSpacingHorizontalMedium,1rem))))))",
                                                        }}
                                                      >
                                                        <span
                                                          className="view-all-label"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          View All
                                                          <span
                                                            className="assistiveText"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              border: "0px",
                                                              margin: "-1px",
                                                              padding: "0px",
                                                              overflow:
                                                                "hidden",
                                                              clip: "rect(0px, 0px, 0px, 0px)",
                                                              width: "1px",
                                                              height: "1px",
                                                              position:
                                                                "absolute",
                                                            }}
                                                          >
                                                            Stage History
                                                          </span>
                                                        </span>
                                                      </div>
                                                    </a>
                                                  </article>
                                                </div>
                                              </div>
                                            </lst-related-list-single-aura-wrapper>
                                          </slot>
                                        </laf-progressive-container>
                                      </lst-related-list-single-container>
                                    </div>
                                    <div
                                      className="listWrapper"
                                      style={{
                                        boxSizing: "border-box",
                                        marginTop: "0.75rem",
                                      }}
                                    >
                                      <lst-related-list-single-container
                                        style={{
                                          boxSizing: "border-box",
                                          display: "block",
                                        }}
                                      >
                                        <laf-progressive-container
                                          aria-busy="false"
                                          style={{
                                            boxSizing: "border-box",
                                            display: "block",
                                          }}
                                        >
                                          <slot
                                            style={{
                                              boxSizing: "border-box",
                                            }}
                                          >
                                            <lst-related-list-single-aura-wrapper
                                              style={{
                                                boxSizing: "border-box",
                                              }}
                                            >
                                              <div
                                                style={{
                                                  boxSizing: "border-box",
                                                }}
                                              >
                                                <div
                                                  className="container forceRelatedListSingleContainer"
                                                  style={{
                                                    boxSizing: "border-box",
                                                    width: "100%",
                                                  }}
                                                >
                                                  <article
                                                    className="slds-card slds-card_boundary slds-card_related-list-fix forceRelatedListCardDesktop"
                                                    aria-label="Approval History"
                                                    style={{
                                                      boxSizing: "border-box",
                                                      display: "block",
                                                      position: "relative",
                                                      paddingTop:
                                                        "var(--slds-c-card-spacing-blockstart, var(--slds-c-card-spacing-block-start, var(--sds-c-card-spacing-block-start, var(--sds-c-card-spacing-block, 0))))",
                                                      paddingBottom:
                                                        "var(--slds-c-card-spacing-blockend, var(--slds-c-card-spacing-block-end, var(--sds-c-card-spacing-block-end, var(--sds-c-card-spacing-block, 0))))",
                                                      backgroundImage: "",
                                                      backgroundPositionX: "",
                                                      backgroundPositionY: "",
                                                      backgroundSize: "",
                                                      backgroundRepeat: "",
                                                      backgroundAttachment: "",
                                                      backgroundOrigin: "",
                                                      backgroundColor: "",
                                                      backgroundClip:
                                                        "padding-box",
                                                      color:
                                                        "var(--slds-c-card-text-color, var(--sds-c-card-text-color))",
                                                      border: "0px",
                                                      boxShadow: "none",
                                                      paddingLeft: "0px",
                                                      paddingRight: "0px",
                                                      borderWidth:
                                                        "var(--slds-c-card-sizing-border, var(--sds-c-card-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                      borderStyle: "solid",
                                                      borderColor:
                                                        "var(--slds-c-card-color-border, var(--sds-c-card-color-border, var(--slds-g-color-border-base-1, var(--lwc-cardColorBorder,rgb(201, 201, 201)))))",
                                                      borderRadius:
                                                        "var(--slds-c-card-radius-border, var(--sds-c-card-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                    }}
                                                  >
                                                    <div
                                                      className="slds-grid slds-page-header forceRelatedListCardHeader"
                                                      style={{
                                                        boxSizing: "border-box",
                                                        padding:
                                                          "var(--lwc-varSpacingVerticalMedium,1rem) var(--lwc-varSpacingHorizontalMedium,1rem)",
                                                        backgroundImage: "",
                                                        backgroundPositionX: "",
                                                        backgroundPositionY: "",
                                                        backgroundSize: "",
                                                        backgroundRepeat: "",
                                                        backgroundAttachment:
                                                          "",
                                                        backgroundOrigin: "",
                                                        backgroundColor: "",
                                                        backgroundClip:
                                                          "padding-box",
                                                        display: "flex",
                                                        border: "0px",
                                                        borderRadius:
                                                          "var(--lwc-borderRadiusMedium,0.25rem)",
                                                        boxShadow: "none",
                                                      }}
                                                    >
                                                      <header
                                                        className="slds-media slds-media--center slds-has-flexi-truncate"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "1 1 0%",
                                                          minWidth: "0px",
                                                          display: "flex",
                                                          alignItems: "center",
                                                          height:
                                                            "var(--lwc-squareIconMedium,2rem)",
                                                        }}
                                                      >
                                                        <div
                                                          className="slds-m-right_small stencil slds-avatar slds-avatar_small"
                                                          aria-hidden="true"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            overflow: "hidden",
                                                            borderRadius:
                                                              "var(--slds-c-avatar-radius-border, var(--sds-c-avatar-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                            display:
                                                              "inline-block",
                                                            verticalAlign:
                                                              "middle",
                                                            lineHeight:
                                                              "var(--lwc-lineHeightReset,1)",
                                                            color:
                                                              "var(--slds-c-avatar-text-color, var(--sds-c-avatar-text-color, var(--slds-g-color-neutral-base-100, var(--lwc-colorTextInverse,rgb(255, 255, 255)))))",
                                                            width:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            height:
                                                              "var(--lwc-squareIconSmallBoundary,1.5rem)",
                                                            fontSize:
                                                              "var(--lwc-fontSize1,0.625rem)",
                                                            marginRight:
                                                              "var(--lwc-spacingSmall,0.75rem)",
                                                            backgroundColor:
                                                              "var(--slds-g-color-neutral-base-80, rgb(236, 235, 234))",
                                                          }}
                                                        >
                                                          <div
                                                            className="extraSmall forceEntityIcon"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              borderRadius:
                                                                "var(--slds-s-icon-radius-border, var(--lwc-borderRadiusSmall,0.125rem))",
                                                              display: "block",
                                                              width:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              height:
                                                                "var(--slds-g-sizing-7, var(--lwc-squareIconSmallBoundary,1.5rem))",
                                                              backgroundColor:
                                                                "rgb(255, 93, 45)",
                                                            }}
                                                          >
                                                            <span
                                                              className="uiImage"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                display:
                                                                  "inherit",
                                                              }}
                                                            >
                                                              <img
                                                                className="icon"
                                                                src="/assets/icons/action/approval_120.png"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  border: "0px",
                                                                  verticalAlign:
                                                                    "middle",
                                                                  maxWidth:
                                                                    "100%",
                                                                  height:
                                                                    "auto",
                                                                  display:
                                                                    "inherit",
                                                                }}
                                                              />
                                                            </span>
                                                          </div>
                                                        </div>
                                                        <div
                                                          className="slds-media__body"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                            flex: "1 1 0%",
                                                            minWidth: "0px",
                                                            marginBottom: "0px",
                                                          }}
                                                        >
                                                          <h2
                                                            id="header_2203:0"
                                                            className="slds-card__header-title"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              margin: "0px",
                                                              padding: "0px",
                                                              display: "flex",
                                                              fontSize:
                                                                "var(--slds-c-card-heading-font-size, var(--sds-c-card-heading-font-size, var(--lwc-varFontSize5,1rem)))",
                                                              fontWeight:
                                                                "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                              lineHeight:
                                                                "var(--lwc-lineHeightHeading,1.25)",
                                                              marginBottom:
                                                                "0px",
                                                            }}
                                                          >
                                                            <a
                                                              className="slds-card__header-link baseCard__header-title-container"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                backgroundColor:
                                                                  "transparent",
                                                                textDecoration:
                                                                  "none",
                                                                transition:
                                                                  "color 0.1s linear",
                                                                cursor:
                                                                  "pointer",
                                                                color:
                                                                  "inherit",
                                                                fontWeight:
                                                                  "var(--slds-c-card-heading-font-weight, var(--sds-c-card-heading-font-weight, var(--lwc-cardFontWeight,700)))",
                                                                display:
                                                                  "inline-flex",
                                                                maxWidth:
                                                                  "100%",
                                                                alignItems:
                                                                  "center",
                                                              }}
                                                            >
                                                              <span
                                                                className="slds-truncate slds-m-right--xx-small"
                                                                title="Approval History"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                  overflow:
                                                                    "hidden",
                                                                  whiteSpace:
                                                                    "nowrap",
                                                                  maxWidth:
                                                                    "100%",
                                                                  textOverflow:
                                                                    "ellipsis",
                                                                }}
                                                              >
                                                                Approval History
                                                              </span>
                                                              <span
                                                                className="slds-shrink-none slds-m-right--xx-small"
                                                                title="(0)"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  flexShrink: 0,
                                                                  marginRight:
                                                                    "var(--lwc-spacingXxSmall,0.25rem)",
                                                                }}
                                                              >
                                                                (0)
                                                              </span>
                                                            </a>
                                                          </h2>
                                                        </div>
                                                      </header>
                                                      <div
                                                        className="slds-no-flex"
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                          flex: "0 0 auto",
                                                        }}
                                                      >
                                                        <div
                                                          className="actionsContainer"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="forceDeferredDropDownAction"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                            }}
                                                          >
                                                            <div
                                                              className="uiMenu forceNoRowActions"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                position:
                                                                  "relative",
                                                                display:
                                                                  "inline-block",
                                                              }}
                                                            >
                                                              <div
                                                                id="2285:0"
                                                                className="uiPopupTrigger"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <div
                                                                  id="2298:0"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <div
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                    }}
                                                                  >
                                                                    <a
                                                                      className="menuTrigger slds-button slds-button--icon-border-filled slds-button_icon-border-filled"
                                                                      aria-disabled="false"
                                                                      aria-expanded="false"
                                                                      aria-haspopup="true"
                                                                      href="#"
                                                                      role="button"
                                                                      tabIndex="0"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        cursor:
                                                                          "pointer",
                                                                        backgroundPosition:
                                                                          "initial",
                                                                        borderStyle:
                                                                          "solid",
                                                                        borderWidth:
                                                                          "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                                                        borderRadius:
                                                                          "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                                                        textDecoration:
                                                                          "none",
                                                                        alignItems:
                                                                          "center",
                                                                        paddingTop:
                                                                          "var(--slds-c-button-spacing-blockstart, var(--slds-c-button-spacing-block-start, var(--sds-c-button-spacing-block-start, 0)))",
                                                                        paddingRight:
                                                                          "var(--slds-c-button-spacing-inlineend, var(--slds-c-button-spacing-inline-end, var(--sds-c-button-spacing-inline-end, 0)))",
                                                                        paddingBottom:
                                                                          "var(--slds-c-button-spacing-blockend, var(--slds-c-button-spacing-block-end, var(--sds-c-button-spacing-block-end, 0)))",
                                                                        paddingLeft:
                                                                          "var(--slds-c-button-spacing-inlinestart, var(--slds-c-button-spacing-inline-start, var(--sds-c-button-spacing-inline-start, 0)))",
                                                                        backgroundImage:
                                                                          "none",
                                                                        backgroundSize:
                                                                          "initial",
                                                                        backgroundRepeat:
                                                                          "initial",
                                                                        backgroundAttachment:
                                                                          "initial",
                                                                        backgroundOrigin:
                                                                          "initial",
                                                                        backgroundClip:
                                                                          "border-box",
                                                                        boxShadow:
                                                                          "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                                                        appearance:
                                                                          "none",
                                                                        userSelect:
                                                                          "none",
                                                                        justifyContent:
                                                                          "center",
                                                                        flexShrink: 0,
                                                                        transition:
                                                                          "border 0.15s linear",
                                                                        borderColor:
                                                                          "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                                                        borderTopStyle:
                                                                          "",
                                                                        borderTopWidth:
                                                                          "",
                                                                        borderRightStyle:
                                                                          "",
                                                                        borderRightWidth:
                                                                          "",
                                                                        borderBottomStyle:
                                                                          "",
                                                                        borderBottomWidth:
                                                                          "",
                                                                        borderLeftStyle:
                                                                          "",
                                                                        borderLeftWidth:
                                                                          "",
                                                                        borderImageSource:
                                                                          "",
                                                                        borderImageSlice:
                                                                          "",
                                                                        borderImageWidth:
                                                                          "",
                                                                        borderImageOutset:
                                                                          "",
                                                                        borderImageRepeat:
                                                                          "",
                                                                        whiteSpace:
                                                                          "nowrap",
                                                                        backgroundColor:
                                                                          "var(--slds-g-color-surface-1, var(--lwc-colorBackgroundAlt,rgb(255, 255, 255)))",
                                                                        fontWeight:
                                                                          "var(--slds-g-font-weight-4, var(--lwc-fontWeightRegular,400))",
                                                                        fontSize:
                                                                          "var(--slds-g-font-size-base, var(--lwc-fontSizeMedium,0.8125rem))",
                                                                        position:
                                                                          "static",
                                                                        display:
                                                                          "table-cell",
                                                                        verticalAlign:
                                                                          "middle",
                                                                        lineHeight:
                                                                          "var(--slds-g-font-lineheight-1, var(--lwc-lineHeightReset,1))",
                                                                        height:
                                                                          "var(--slds-g-sizing-6, var(--lwc-squareIconUtilityMedium,1.25rem))",
                                                                        color:
                                                                          "var(--slds-g-color-on-surface-1, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                                                        textAlign:
                                                                          "center",
                                                                        width:
                                                                          "var(--slds-g-sizing-6, var(--lwc-squareIconUtilityMedium,1.25rem))",
                                                                      }}
                                                                    >
                                                                      <lightning-icon
                                                                        className="slds-icon-utility-down forceIcon slds-button__icon slds-icon_container"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          width:
                                                                            "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                          height:
                                                                            "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                                                          fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                                                          borderRadius:
                                                                            "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                                                          display:
                                                                            "inline-block",
                                                                          backgroundColor:
                                                                            "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                                                                          lineHeight:
                                                                            "initial",
                                                                        }}
                                                                      >
                                                                        <span
                                                                          style={{
                                                                            boxSizing:
                                                                              "border-box",
                                                                          }}
                                                                        >
                                                                          <lightning-primitive-icon
                                                                            size="xx-small"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                            }}
                                                                          >
                                                                            <svg
                                                                              className="slds-icon slds-icon_xx-small"
                                                                              aria-hidden="true"
                                                                              focusable="false"
                                                                              viewBox="0 0 520 520"
                                                                              style={{
                                                                                boxSizing:
                                                                                  "border-box",
                                                                                verticalAlign:
                                                                                  "middle",
                                                                                lineHeight:
                                                                                  "var(--lwc-lineHeightReset,1)",
                                                                                overflow:
                                                                                  "hidden",
                                                                                width:
                                                                                  "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                                height:
                                                                                  "var(--slds-g-sizing-4, var(--lwc-squareIconSmallContent,0.75rem))",
                                                                                fill: "currentcolor",
                                                                              }}
                                                                            >
                                                                              <g
                                                                                style={{
                                                                                  boxSizing:
                                                                                    "border-box",
                                                                                }}
                                                                              >
                                                                                <path
                                                                                  d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                                                                  style={{
                                                                                    boxSizing:
                                                                                      "border-box",
                                                                                  }}
                                                                                />
                                                                              </g>
                                                                            </svg>
                                                                          </lightning-primitive-icon>
                                                                          <span
                                                                            className="slds-assistive-text"
                                                                            style={{
                                                                              boxSizing:
                                                                                "border-box",
                                                                              whiteSpace:
                                                                                "nowrap",
                                                                              textTransform:
                                                                                "none",
                                                                              margin:
                                                                                "-1px",
                                                                              border:
                                                                                "var(--lwc-spacingNone, 0)",
                                                                              padding:
                                                                                "var(--lwc-spacingNone, 0)",
                                                                              overflow:
                                                                                "hidden",
                                                                              position:
                                                                                "absolute",
                                                                              width:
                                                                                "var(--lwc-borderWidthThin, 1px)",
                                                                              height:
                                                                                "var(--lwc-borderWidthThin, 1px)",
                                                                              clip: "rect(0px, 0px, 0px, 0px)",
                                                                            }}
                                                                          >
                                                                            Show
                                                                            More
                                                                          </span>
                                                                        </span>
                                                                      </lightning-icon>
                                                                    </a>
                                                                  </div>
                                                                </div>
                                                              </div>
                                                            </div>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                    <div
                                                      style={{
                                                        boxSizing: "border-box",
                                                      }}
                                                    >
                                                      <div
                                                        style={{
                                                          boxSizing:
                                                            "border-box",
                                                        }}
                                                      >
                                                        <div
                                                          className="previewMode SMALL forceRelatedListPreview"
                                                          style={{
                                                            boxSizing:
                                                              "border-box",
                                                          }}
                                                        >
                                                          <div
                                                            className="hide"
                                                            style={{
                                                              boxSizing:
                                                                "border-box",
                                                              display: "none",
                                                            }}
                                                          >
                                                            <div
                                                              className="slds-card__body"
                                                              style={{
                                                                boxSizing:
                                                                  "border-box",
                                                                marginTop:
                                                                  "var(--slds-c-card-body-spacing-blockstart, var(--slds-c-card-body-spacing-block-start, var(--sds-c-card-body-spacing-block-start, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                                marginBottom:
                                                                  "var(--slds-c-card-body-spacing-blockend, var(--slds-c-card-body-spacing-block-end, var(--sds-c-card-body-spacing-block-end, var(--slds-c-card-body-spacing-block, var(--sds-c-card-body-spacing-block, var(--lwc-varSpacingVerticalSmall,0.75rem))))))",
                                                                paddingLeft:
                                                                  "0px",
                                                                paddingRight:
                                                                  "0px",
                                                                marginLeft:
                                                                  "0px",
                                                                marginRight:
                                                                  "0px",
                                                              }}
                                                            >
                                                              <div
                                                                className="slds-hide"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  display:
                                                                    "none",
                                                                }}
                                                              >
                                                                <force-placeholder2
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                  }}
                                                                >
                                                                  <div
                                                                    className="slds-p-around_xxx-small"
                                                                    style={{
                                                                      boxSizing:
                                                                        "border-box",
                                                                      padding:
                                                                        "var(--lwc-spacingXxxSmall,0.125rem)",
                                                                    }}
                                                                  >
                                                                    <div
                                                                      className="field"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        padding:
                                                                          "0 1rem",
                                                                      }}
                                                                    >
                                                                      <div
                                                                        className="text text-primary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          height:
                                                                            "0.25rem",
                                                                          maxWidth:
                                                                            "265px",
                                                                        }}
                                                                      />
                                                                      <div
                                                                        className="text text-secondary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          height:
                                                                            "0.25rem",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          maxWidth:
                                                                            "130px",
                                                                        }}
                                                                      />
                                                                    </div>
                                                                    <div
                                                                      className="field"
                                                                      style={{
                                                                        boxSizing:
                                                                          "border-box",
                                                                        padding:
                                                                          "0 1rem",
                                                                      }}
                                                                    >
                                                                      <div
                                                                        className="text text-primary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          height:
                                                                            "0.25rem",
                                                                          maxWidth:
                                                                            "180px",
                                                                        }}
                                                                      />
                                                                      <div
                                                                        className="text text-secondary"
                                                                        style={{
                                                                          boxSizing:
                                                                            "border-box",
                                                                          borderRadius:
                                                                            "15rem",
                                                                          margin:
                                                                            "1rem 0",
                                                                          height:
                                                                            "0.25rem",
                                                                          backgroundColor:
                                                                            "rgb(243, 243, 243)",
                                                                          maxWidth:
                                                                            "210px",
                                                                        }}
                                                                      />
                                                                    </div>
                                                                  </div>
                                                                </force-placeholder2>
                                                              </div>
                                                              <ul
                                                                className="uiAbstractList"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                  padding:
                                                                    "0px",
                                                                  listStyle:
                                                                    "none",
                                                                  margin: "0px",
                                                                }}
                                                              />
                                                              <div
                                                                className="emptyContent hidden"
                                                                style={{
                                                                  boxSizing:
                                                                    "border-box",
                                                                }}
                                                              >
                                                                <div
                                                                  className="emptyContentInner slds-text-align_center slds-text-align--center"
                                                                  style={{
                                                                    boxSizing:
                                                                      "border-box",
                                                                    textAlign:
                                                                      "center",
                                                                  }}
                                                                />
                                                              </div>
                                                            </div>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </article>
                                                </div>
                                              </div>
                                            </lst-related-list-single-aura-wrapper>
                                          </slot>
                                        </laf-progressive-container>
                                      </lst-related-list-single-container>
                                    </div>
                                  </div>
                                </lst-related-list-container>
                              </slot>
                            </flexipage-component2>
                          </slot>
                        </flexipage-tab2>
                      </slot>
                    </slot>
                  </div>
                </lightning-tabset>
              </div>
            </flexipage-tabset2>
          </slot>
        </flexipage-component2>
      </slot>
    </div>
  );
}
