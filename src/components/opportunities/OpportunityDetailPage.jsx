// src/components/opportunities/OpportunityDetailPage.jsx
import { useContext, useEffect } from "react";
import { useParams } from "react-router-dom";
import { GlobalContext } from "../../context/GlobalContext";

import styled from "styled-components";
import Completepage from "./stage_view/Details";

// Styled Components for Layout
const Container = styled.div`
  flex: 1;
  margin-top: 85px;
  padding: 1rem;
  background-image: url("/assets/images/themes/oneSalesforce/lightning_blue_background.png");
  background-repeat: no-repeat;
  background-color: #b0c4e0;
  height: auto;
  overflow-y: auto;
`;

export default function OpportunityDetailPage() {
  const { allVariableData, setIsSidebarVisible, saveToLocalStorage } =
    useContext(GlobalContext);
  const { id } = useParams();

  // Hide sidebar on component mount and load the opportunity data
  useEffect(() => {
    setIsSidebarVisible(false);

    // Get the opportunity by ID from the list
    if (
      allVariableData.opportunityList &&
      allVariableData.opportunityList.length > 0
    ) {
      // If we have a specific ID, try to find that opportunity
      if (
        id &&
        !isNaN(parseInt(id)) &&
        parseInt(id) < allVariableData.opportunityList.length
      ) {
        const selectedOpportunity =
          allVariableData.opportunityList[parseInt(id)];
        saveToLocalStorage("opportunity", {});
      }
      // Otherwise, use the current opportunity from context or the most recent one
      else if (Object.keys(allVariableData.opportunity || {}).length > 0) {
        // Current opportunity is already set in context
      } else {
        const latestOpportunity = allVariableData.opportunityList.slice(-1)[0];
        saveToLocalStorage("opportunity", {});
      }
    }
  }, [
    allVariableData.opportunityList,
    allVariableData.opportunity,
    id,
    setIsSidebarVisible,
    saveToLocalStorage,
  ]);

  return (
    <Container>
      <Completepage />
    </Container>
  );
}
