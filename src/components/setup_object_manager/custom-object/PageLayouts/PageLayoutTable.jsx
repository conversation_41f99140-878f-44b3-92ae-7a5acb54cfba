import React, { useContext, useEffect, useState } from "react";
import { GlobalContext } from "../../../../context/GlobalContext";
import { useParams, useNavigate } from "react-router-dom";
import { useObjectManager } from "../../../../context/ObjectManagerContext";

// Layout data for different object types
const getLayoutsForObject = (objectApiName) => {
  switch (objectApiName) {
    case "Opportunity":
      return [
        {
          id: 1,
          name: "Opportunity (Marketing) Layout",
          createdBy: "seven steven",
          createdDate: "2/27/2025, 4:42 PM",
          modifiedBy: "seven steven",
          modifiedDate: "5/14/2025, 7:48 AM"
        },
        {
          id: 2,
          name: "Opportunity (Sales) Layout",
          createdBy: "seven steven",
          createdDate: "2/27/2025, 4:42 PM",
          modifiedBy: "seven steven",
          modifiedDate: "5/14/2025, 7:48 AM"
        },
        {
          id: 3,
          name: "Opportunity (Support) Layout",
          createdBy: "seven steven",
          createdDate: "2/27/2025, 4:42 PM",
          modifiedBy: "seven steven",
          modifiedDate: "5/14/2025, 7:48 AM"
        },
        {
          id: 4,
          name: "Opportunity Layout",
          createdBy: "seven steven",
          createdDate: "2/27/2025, 4:42 PM",
          modifiedBy: "seven steven",
          modifiedDate: "5/14/2025, 7:48 AM"
        }
      ];
    case "OpportunityContactRole":
      return [
        {
          id: 1,
          name: "Opportunity Contact Role Layout",
          createdBy: "seven steven",
          createdDate: "2/27/2025, 4:42 PM",
          modifiedBy: "seven steven",
          modifiedDate: "5/14/2025, 7:48 AM"
        }
      ];
    case "OpportunityLineItem":
      return [
        {
          id: 1,
          name: "Opportunity Product Layout",
          createdBy: "seven steven",
          createdDate: "2/27/2025, 4:42 PM",
          modifiedBy: "seven steven",
          modifiedDate: "5/14/2025, 7:48 AM"
        }
      ];
    default:
      return [
        {
          id: 1,
          name: "Default Layout",
          createdBy: "seven steven",
          createdDate: "2/27/2025, 4:42 PM",
          modifiedBy: "seven steven",
          modifiedDate: "5/14/2025, 7:48 AM"
        }
      ];
  }
};

export default function PageLayoutTable({ selectedObject }) {
  const { allVariableData, setAllVariableData, saveToLocalStorage } =
    useContext(GlobalContext);
  const [customObjectName, setCustomObjectName] = useState("");
  const [layouts, setLayouts] = useState([]);
  const { apiName } = useParams();
  const { findObjectByApiName } = useObjectManager();
  const navigate = useNavigate();

  // Get the custom object name and layouts when component mounts or when selectedObject changes
  useEffect(() => {
    // Set layouts based on the API name
    setLayouts(getLayoutsForObject(apiName));

    // First priority: Use the selectedObject passed as prop
    if (selectedObject && selectedObject.MasterLabel) {
      setCustomObjectName(selectedObject.MasterLabel);
      return;
    }

    // Second priority: Try to find the object by API name
    if (apiName) {
      const foundObject = findObjectByApiName(apiName);
      if (foundObject && foundObject.MasterLabel) {
        setCustomObjectName(foundObject.MasterLabel);
        return;
      }
    }

    // Fallback: Use data from global state (for backward compatibility)
    const windowData = window.allVariableData?.objectManager?.customObjectData;
    const contextData = allVariableData?.objectManager?.customObjectData;
    const customObjectData = windowData || contextData;

    if (
      customObjectData &&
      Array.isArray(customObjectData) &&
      customObjectData.length > 0
    ) {
      const mostRecentObject = customObjectData.slice(-1)[0];
      if (mostRecentObject && mostRecentObject.MasterLabel) {
        setCustomObjectName(mostRecentObject.MasterLabel);
      }
    }
  }, [selectedObject, apiName, allVariableData, findObjectByApiName]);

  const handleLayoutClick = (pageLayout) => {
    // Store the selected layout in local storage
    
    

    // Navigate to the CreatedPageLayouts component
    navigate(`/setup/objectManager/${apiName}/page-layouts/${pageLayout.id}/view`);
  };

  return (
    <>
      <section
        className="related-list-card"
        style={{
          boxSizing: "border-box",
          display: "block",
          margin: "0px",
          flexFlow: "column",
          inset: "71px 0px 0px",
          backgroundColor: "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
          position: "absolute",
        }}
      >
        <div
          className="related-list-card-content"
          style={{ boxSizing: "border-box", height: "100%" }}
        >
          <div
            className="scrollerWrapper"
            style={{ boxSizing: "border-box", zIndex: 0, height: "100%" }}
          >
            <div
              className="scroller uiScroller scroller-wrapper scroll-bidirectional native"
              tabIndex="-1"
              style={{
                boxSizing: "border-box",
                position: "relative",
                height: "100%",
                transform: "translateZ(0px)",
                overflow: "auto",
                width: "100%",
              }}
            >
              <div
                className="scroller"
                style={{
                  boxSizing: "border-box",
                  position: "absolute",
                  WebkitTapHighlightColor: "var(--slds-g-color-neutral-base-10, rgba(0,0,0,0))",
                  transform: "translateZ(0px)",
                  textSizeAdjust: "none",
                  width: "100%",
                  userSelect: "text",
                }}
              >
                <table
                  className="slds-table slds-table-bordered uiVirtualDataGrid--default uiVirtualDataGrid"
                  style={{
                    boxSizing: "border-box",
                    borderSpacing: "0px",
                    width: "100%",
                    backgroundColor: "var(--slds-g-color-neutral-base-100, var(--lwc-colorBackgroundAlt,rgb(255, 255, 255)))",
                    fontSize: "inherit",
                    borderCollapse: "inherit",
                  }}
                >
                  <thead style={{ boxSizing: "border-box", verticalAlign: "middle" }}>
                    <tr style={{ boxSizing: "border-box", verticalAlign: "middle" }}>
                      <th
                        className="slds-text-heading--label slds-is-sortable standard-col slds-truncate ascending"
                        aria-label="Page Layout Name"
                        scope="col"
                        title="Page Layout Name"
                        style={{
                          boxSizing: "border-box",
                          textAlign: "left",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          fontSize: "var(--lwc-fontSize2,0.75rem)",
                          textTransform: "uppercase",
                          letterSpacing: "0.0625rem",
                          verticalAlign: "middle",
                          whiteSpace: "nowrap",
                          position: "relative",
                          padding: "var(--lwc-tableCellSpacing,0.25rem 0.5rem)",
                          backgroundColor: "var(--slds-g-color-neutral-base-95, var(--lwc-tableColorBackgroundHeader,rgb(243, 243, 243)))",
                          color: "var(--slds-g-color-neutral-base-30, var(--lwc-tableColorTextHeader,rgb(68, 68, 68)))",
                          fontWeight: "var(--lwc-fontWeightBold,700)",
                          lineHeight: "normal",
                          borderBottom: "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          paddingRight: "var(--lwc-spacingLarge,1.5rem)",
                          maxWidth: "150px",
                        }}
                      >
                        <a
                          className="toggle"
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color: "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                            marginRight: "20px",
                          }}
                        >
                          Page Layout Name
                        </a>
                      </th>
                      <th
                        className="slds-text-heading--label slds-is-sortable standard-col slds-truncate"
                        aria-label="Created By"
                        scope="col"
                        title="Created By"
                        style={{
                          boxSizing: "border-box",
                          textAlign: "left",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          fontSize: "var(--lwc-fontSize2,0.75rem)",
                          textTransform: "uppercase",
                          letterSpacing: "0.0625rem",
                          verticalAlign: "middle",
                          whiteSpace: "nowrap",
                          position: "relative",
                          padding: "var(--lwc-tableCellSpacing,0.25rem 0.5rem)",
                          backgroundColor: "var(--slds-g-color-neutral-base-95, var(--lwc-tableColorBackgroundHeader,rgb(243, 243, 243)))",
                          color: "var(--slds-g-color-neutral-base-30, var(--lwc-tableColorTextHeader,rgb(68, 68, 68)))",
                          fontWeight: "var(--lwc-fontWeightBold,700)",
                          lineHeight: "normal",
                          borderBottom: "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          maxWidth: "150px",
                        }}
                      >
                        <a
                          className="toggle"
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color: "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                          }}
                        >
                          Created By
                        </a>
                      </th>
                      <th
                        className="slds-text-heading--label slds-is-sortable standard-col slds-truncate"
                        aria-label="Modified By"
                        scope="col"
                        title="Modified By"
                        style={{
                          boxSizing: "border-box",
                          textAlign: "left",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          fontSize: "var(--lwc-fontSize2,0.75rem)",
                          textTransform: "uppercase",
                          letterSpacing: "0.0625rem",
                          verticalAlign: "middle",
                          whiteSpace: "nowrap",
                          position: "relative",
                          padding: "var(--lwc-tableCellSpacing,0.25rem 0.5rem)",
                          backgroundColor: "var(--slds-g-color-neutral-base-95, var(--lwc-tableColorBackgroundHeader,rgb(243, 243, 243)))",
                          color: "var(--slds-g-color-neutral-base-30, var(--lwc-tableColorTextHeader,rgb(68, 68, 68)))",
                          fontWeight: "var(--lwc-fontWeightBold,700)",
                          lineHeight: "normal",
                          borderBottom: "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                        }}
                      >
                        <a
                          className="toggle"
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color: "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                          }}
                        >
                          Modified By
                        </a>
                      </th>
                      <th
                        className="actionCol slds-truncate"
                        aria-label="Actions"
                        scope="col"
                        title="Actions"
                        style={{
                          boxSizing: "border-box",
                          textAlign: "left",
                          overflow: "hidden",
                          maxWidth: "100%",
                          textOverflow: "ellipsis",
                          verticalAlign: "middle",
                          whiteSpace: "nowrap",
                          position: "relative",
                          padding: "var(--lwc-tableCellSpacing,0.25rem 0.5rem)",
                          backgroundColor: "var(--slds-g-color-neutral-base-95, var(--lwc-tableColorBackgroundHeader,rgb(243, 243, 243)))",
                          color: "var(--slds-g-color-neutral-base-30, var(--lwc-tableColorTextHeader,rgb(68, 68, 68)))",
                          fontWeight: "var(--lwc-fontWeightBold,700)",
                          lineHeight: "normal",
                          borderBottom: "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                        }}
                      >
                        <span
                          className="assistiveText"
                          style={{
                            boxSizing: "border-box",
                            border: "0px",
                            margin: "-1px",
                            padding: "0px",
                            overflow: "hidden",
                            clip: "rect(0px, 0px, 0px, 0px)",
                            width: "1px",
                            height: "1px",
                            position: "absolute",
                          }}
                        >
                          Actions
                        </span>
                      </th>
                    </tr>
                  </thead>
                  <tfoot style={{ boxSizing: "border-box" }} />
                  <tbody style={{ boxSizing: "border-box", verticalAlign: "middle" }}>
                    {layouts.map((layout) => (
                      <tr
                        key={layout.id}
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          counterIncrement: "row-number 1",
                        }}
                      >
                        <td
                          style={{
                            boxSizing: "border-box",
                            verticalAlign: "middle",
                            position: "relative",
                            borderBottom: "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                            padding: "var(--lwc-spacingXSmall,0.5rem)",
                            whiteSpace: "pre-line",
                            maxWidth: "250px",
                            visibility: "visible",
                            fontWeight: "normal",
                            textAlign: "left",
                            backgroundColor: "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                          }}
                        >
                          <a
                            onClick={(e) => {
                              e.preventDefault();
                              handleLayoutClick({
                                id: layout.id,
                                label: layout.name,
                                apiName: apiName
                              });
                            }}
                            href={`/setup/objectManager/${apiName}/page-layouts/${layout.id}/view`}
                            style={{
                              boxSizing: "border-box",
                              backgroundColor: "transparent",
                              textDecoration: "none",
                              transition: "color 0.1s linear",
                              color: "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                              cursor: "pointer",
                            }}
                          >
                            <span
                              className="uiOutputText"
                              style={{ boxSizing: "border-box" }}
                            >
                              {layout.name}
                            </span>
                          </a>
                        </td>
                        <td
                          style={{
                            boxSizing: "border-box",
                            verticalAlign: "middle",
                            position: "relative",
                            borderBottom: "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                            padding: "var(--lwc-spacingXSmall,0.5rem)",
                            whiteSpace: "pre-line",
                            maxWidth: "250px",
                            visibility: "visible",
                            fontWeight: "normal",
                            textAlign: "left",
                            backgroundColor: "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                          }}
                        >
                          <a
                            className="setupLink"
                            href="#"
                            style={{
                              boxSizing: "border-box",
                              backgroundColor: "transparent",
                              textDecoration: "none",
                              transition: "color 0.1s linear",
                              cursor: "pointer",
                              fontWeight: "var(--lwc-fontWeightRegular,400)",
                              color: "var(--lwc-colorTextLink,rgb(1, 118, 211))",
                            }}
                          >
                            {layout.createdBy}
                          </a>
                          , {layout.createdDate}
                        </td>
                        <td
                          style={{
                            boxSizing: "border-box",
                            verticalAlign: "middle",
                            position: "relative",
                            borderBottom: "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                            padding: "var(--lwc-spacingXSmall,0.5rem)",
                            whiteSpace: "pre-line",
                            maxWidth: "250px",
                            visibility: "visible",
                            fontWeight: "normal",
                            textAlign: "left",
                            backgroundColor: "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                          }}
                        >
                          <a
                            className="setupLink"
                            href="#"
                            style={{
                              boxSizing: "border-box",
                              backgroundColor: "transparent",
                              textDecoration: "none",
                              transition: "color 0.1s linear",
                              cursor: "pointer",
                              fontWeight: "var(--lwc-fontWeightRegular,400)",
                              color: "var(--lwc-colorTextLink,rgb(1, 118, 211))",
                            }}
                          >
                            {layout.modifiedBy}
                          </a>
                          , {layout.modifiedDate}
                        </td>
                        <td
                          style={{
                            boxSizing: "border-box",
                            verticalAlign: "middle",
                            position: "relative",
                            borderBottom: "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                            padding: "var(--lwc-spacingXSmall,0.5rem)",
                            whiteSpace: "pre-line",
                            maxWidth: "250px",
                            visibility: "visible",
                            fontWeight: "normal",
                            textAlign: "left",
                            backgroundColor: "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                          }}
                        >
                          <div
                            className="objectManagerVirtualActionMenu"
                            style={{ boxSizing: "border-box" }}
                          >
                            <div
                              className="uiMenu objectManagerActionsMenu"
                              style={{
                                boxSizing: "border-box",
                                position: "relative",
                              }}
                            >
                              <div
                                className="uiPopupTrigger"
                                style={{ boxSizing: "border-box" }}
                              >
                                <div style={{ boxSizing: "border-box" }}>
                                  <div style={{ boxSizing: "border-box" }}>
                                    <a
                                      className="slds-dropdown-trigger slds-dropdown-trigger--click slds-button slds-button--icon-border-filled"
                                      aria-disabled="false"
                                      aria-expanded="false"
                                      aria-haspopup="true"
                                      href="#"
                                      role="button"
                                      tabIndex="0"
                                      style={{
                                        boxSizing: "border-box",
                                        cursor: "pointer",
                                        backgroundColor: "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                                        borderColor: "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                        color: "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                        borderRadius: "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                        borderStyle: "solid",
                                        borderWidth: "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                        display: "inline-block",
                                        padding: "var(--lwc-borderWidthThin,1px)",
                                        textAlign: "center",
                                        position: "relative",
                                        width: "var(--lwc-squareIconMediumBoundary,2rem)",
                                        height: "var(--lwc-squareIconMediumBoundary,2rem)",
                                      }}
                                    >
                                      <svg
                                        className="slds-icon slds-icon_xx-small"
                                        aria-hidden="true"
                                        focusable="false"
                                        viewBox="0 0 520 520"
                                        style={{
                                          boxSizing: "border-box",
                                          verticalAlign: "middle",
                                          width: "var(--lwc-squareIconXxSmallContent,.875rem)",
                                          height: "var(--lwc-squareIconXxSmallContent,.875rem)",
                                          fill: "currentcolor",
                                        }}
                                      >
                                        <g style={{ boxSizing: "border-box" }}>
                                          <path
                                            d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                            style={{ boxSizing: "border-box" }}
                                          />
                                        </g>
                                      </svg>
                                    </a>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                <div
                  className="infinite-loading"
                  style={{ boxSizing: "border-box" }}
                >
                  <span className="il" style={{ boxSizing: "border-box" }} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <style
        dangerouslySetInnerHTML={{
          __html: `
            .slds-table > tbody > tr:hover > th,
            .slds-table > tbody > tr:hover > td {
              background-color: rgb(243, 243, 243) !important;
            }
          `,
        }}
      />
    </>
  );
}
