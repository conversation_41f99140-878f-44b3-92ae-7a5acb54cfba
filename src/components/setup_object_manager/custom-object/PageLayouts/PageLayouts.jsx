import React from "react";
import PageLayoutTable from "./PageLayoutTable.jsx";
import { useNavigate, useParams, useOutletContext } from "react-router-dom";
import { useObjectManager } from "../../../../context/ObjectManagerContext";
// Assuming you have SLDS CSS loaded globally or scoped appropriately

// --- Button Group Component (Simplified) ---
function ButtonGroup({ apiName }) {
  // Use the useNavigate hook
  const navigate = useNavigate();

  // This component now only renders the grouped buttons
  return (
    <div className="slds-button-group" role="group">
      <button
        className="slds-button slds-button_neutral"
        type="button"
        title="New Page Layout"
        onClick={() => {
          // Navigate to the new page layout creation page (placeholder)
          navigate(`/setup/objectManager/${apiName}/page-layouts/new`);
        }}
      >
        New
      </button>
      <button
        className="slds-button slds-button_neutral"
        type="button"
        title="Page Layouts Assignment"
        onClick={() => {
          // Navigate to the page layout assignment page (placeholder)
          navigate(`/setup/objectManager/${apiName}/page-layouts/assignment`);
        }}
      >
        Page Layout Assignment
      </button>
    </div>
  );
}

// --- PageLayouts Component (Main Structure) ---
export default function PageLayouts() {
  const { apiName } = useParams();
  const { selectedObject } = useOutletContext() || {};
  const { findObjectByApiName } = useObjectManager();

  // Try to find the object by context first, then by API name
  const foundObject = selectedObject || findObjectByApiName(apiName);

  return (
    <>
      <GlobalStyle />
      {/* Using slds-card instead of raw divs for better structure often */}
      <article
        className="slds-card"
        style={{
          border: "none",
          boxShadow: "none",
          backgroundColor: "transparent" /* Allow parent bg */,
        }}
      >
        <div
          className="slds-card__header slds-grid"
          style={{
            padding:
              "var(--lwc-varSpacingVerticalMedium,1rem) var(--lwc-varSpacingHorizontalMedium,1rem)",
            backgroundColor: "white",
            borderRadius: "var(--lwc-borderRadiusMedium,0.25rem)",
          }}
        >
          {/* Header Title Area */}
          <header className="slds-media slds-media_center slds-has-flexi-truncate slds-col">
            {/* Icon can go here if needed */}
            {/* <div className="slds-media__figure"> ... </div> */}
            <div className="slds-media__body">
              <h2 className="slds-card__header-title">
                <span
                  className="slds-text-heading_medium slds-text slds-truncate"
                  title="Fields & Relationships"
                  style={{ fontWeight: "bold" }}
                >
                  Page Layouts
                </span>
              </h2>
              <p
                className="slds-text-body_small slds-truncate"
                title="4 Items, Sorted by Field Label"
              >
                1 Items, Sorted by Page Layout Name{" "}
                {/* This might need to be dynamic */}
              </p>
            </div>
          </header>

          {/* Header Actions Area */}
          <div className="slds-col slds-no-flex slds-grid slds-align-middle slds-grid_vertical-align-center">
            {" "}
            {/* Align items vertically */}
            {/* Quick Find Input */}
            <div className="slds-form-element slds-m-right_small">
              {" "}
              {/* Add margin between elements */}
              <div className="slds-form-element__control slds-input-has-icon slds-input-has-icon_left">
                {/* SLDS Search Icon using SVG */}
                <svg
                  className="slds-icon slds-input__icon slds-input__icon_left slds-icon-text-default"
                  aria-hidden="true"
                >
                  {/* Ensure this path is correct for your setup */}
                  <use xlinkHref="/assets/icons/utility-sprite/svg/symbols.svg#search"></use>
                </svg>
                <input
                  type="search"
                  id="text-input-id-1" // Use a more unique ID generator if needed
                  placeholder="Quick Find"
                  className="slds-input"
                  style={{ width: "var(--lwc-sizeXSmall,12rem)" }} // Keep specific width if needed
                />
                {/* Hidden label for accessibility */}
                <label
                  className="slds-assistive-text"
                  htmlFor="text-input-id-1"
                >
                  Quick Find
                </label>
              </div>
            </div>
            {/* Button Group Component */}
            <div className="slds-no-flex">
              {" "}
              {/* Prevent button group from shrinking */}
              <ButtonGroup apiName={apiName} />
            </div>
          </div>
        </div>

        {/* Optional: Card Body for the actual field list would go here */}
        <div className="slds-card__body slds-card__body_inner">
          {/* Pass the found object to PageLayoutTable */}
          <PageLayoutTable selectedObject={foundObject} />
        </div>
      </article>
    </>
  );
}

const GlobalStyle = () => {
  return (
    <style>
      {`
                html {
                  overflow: auto;
                }
                body {
                  background-position: left top;
                  font-size: 71%; /* Matches original Salesforce styling */
                  background: none transparent;
                  margin: 0px;
                  padding: 0px;
                  overflow: auto;
                  font-family: Arial, Helvetica, sans-serif; /* Explicitly set default */
                  color: rgb(60, 61, 62); /* Default text color */
                  background-repeat: initial;
                  background-color: transparent;
                  height: 100%;
                }
                /* Add hover/focus styles for the info icon tooltip */
                .mouseOverInfoOuter:hover .mouseOverInfo,
                .mouseOverInfoOuter:focus .mouseOverInfo,
                .mouseOverInfoOuter:focus-within .mouseOverInfo { /* Added focus-within */
                  display: block;
                  opacity: 1;
                  pointer-events: auto; /* Allow interaction with link inside tooltip */
                }
            `}
    </style>
  );
};
