import React from "react";

export default function MobileCards() {
  return (
    <div
      id="ext-gen195"
      className="bPageBlock secondaryPalette relatedContentWrapper"
      style={{
        backgroundPosition: "left bottom",
        backgroundRepeat: "no-repeat",
        clear: "both",
        margin: "0px",
        backgroundImage: "none",
        padding: "0px",
        paddingBottom: "0px",
        marginBottom: "10px",
        borderTop: "3px solid rgb(116, 126, 150)",
        borderColor: "rgb(229, 193, 48)",
        borderBottom: "1px solid rgb(234, 234, 234)",
        borderLeft: "1px solid rgb(234, 234, 234)",
        borderRight: "1px solid rgb(234, 234, 234)",
        borderRadius: "4px",
        border: "0px none transparent",
        borderTopWidth: "0px",
        borderRightColor: "transparent",
        borderBottomColor: "transparent",
        borderLeftColor: "transparent",
        backgroundColor: "transparent",
      }}
    >
      <div
        id="ext-gen196"
        className="pbHeader"
        style={{
          lineHeight: "17px",
          borderBottom: "1px solid rgb(255, 255, 255)",
          padding: "0px",
          margin: "0px",
          backgroundColor: "transparent",
          paddingBottom: "0px",
          border: "0px",
        }}
      >
        <table style={{ borderSpacing: "0px", width: "100%" }}>
          <tbody>
            <tr>
              <td
                className="pbTitle"
                style={{
                  fontFamily: "Arial, Helvetica, sans-serif",
                  margin: "0px",
                  color: "rgb(34, 34, 34)",
                  fontSize: "91%",
                  width: "100%",
                  padding: "5px 12px",
                  verticalAlign: "top",
                }}
              >
                <h3
                  style={{
                    fontWeight: "bold",
                    fontFamily: "Arial, Helvetica, sans-serif",
                    padding: "0px",
                    display: "block",
                    margin: "0px",
                    color: "rgb(0, 0, 0)",
                    fontSize: "1.3em",
                  }}
                >
                  Mobile Cards (Salesforce mobile only){" "}
                  <div
                    className="mouseOverInfoOuter"
                    tabIndex="0"
                    style={{
                      margin: "0px",
                      padding: "0px",
                      textDecoration: "none",
                      position: "relative",
                      display: "inline",
                      lineHeight: "1.2em",
                      marginLeft: "3px",
                    }}
                  >
                    <img
                      className="infoIcon"
                      src="/assets/downloaded/s.gif"
                      style={{
                        border: "0px",
                        width: "16px",
                        backgroundPosition: "left top",
                        backgroundImage:
                          'url("/assets/downloaded/info_sprite.png")',
                        height: "15px",
                      }}
                    />
                    <div
                      className="mouseOverInfo"
                      style={{
                        margin: "0px",
                        border: "1px solid black",
                        whiteSpace: "normal",
                        position: "absolute",
                        bottom: "20px",
                        width: "20em",
                        zIndex: 11,
                        opacity: 0,
                        fontWeight: "normal",
                        color: "rgb(0, 0, 0)",
                        borderColor: "rgb(51, 51, 51)",
                        padding: "8px 10px",
                        borderRadius: "3px",
                        backgroundColor: "rgb(255, 255, 208)",
                        fontSize: "1em",
                        display: "none",
                        left: "-23px",
                      }}
                    >
                      Expanded lookups and mobile-enabled Visualforce pages
                      placed here display as mobile cards on a record's home
                      page in the mobile app. Visualforce pages in this area
                      must be enabled for Salesforce mobile apps or they won't
                      display.
                    </div>
                  </div>
                </h3>
              </td>
              <td
                className="pbButtons"
                style={{
                  fontFamily: "Arial, Helvetica, sans-serif",
                  color: "rgb(34, 34, 34)",
                  margin: "0px",
                  padding: "5px 12px",
                }}
              />
            </tr>
          </tbody>
        </table>
      </div>
      <div
        id="ext-gen197"
        className="pbBody"
        style={{
          color: "rgb(0, 0, 0)",
          margin: "0px",
          backgroundColor: "transparent",
          backgroundImage: "none",
          marginRight: "0px",
          padding: "0px 6px 6px",
        }}
      >
        <div
          id="01Baj00000XNrMF"
          className="section relatedContent rloDrop"
          style={{
            margin: "0px",
            padding: "0px",
            paddingBottom: "3px",
            border: "none",
            marginTop: "10px",
          }}
        >
          <div
            id="ext-gen385"
            className="section-bwrap"
            style={{ margin: "0px", padding: "0px" }}
          >
            <div
              id="ext-gen386"
              className="section-body section-body-noheader"
              style={{
                margin: "0px",
                padding: "0px",
                border: "1px dotted rgb(204, 204, 204)",
              }}
            >
              <table
                id="ext-gen393"
                cellPadding="0"
                cellSpacing="0"
                style={{ width: "100%" }}
              >
                <tbody>
                  <tr id="ext-gen389">
                    <td
                      className="entryCell col1"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(0, 0, 0)",
                        borderStyle: "solid",
                        borderColor: "rgb(221, 221, 221)",
                        borderImage: "initial",
                        borderWidth: "0px 1px 1px",
                        margin: "0px",
                        width: "100%",
                        border: "0px none",
                        padding: "1px 0px",
                        verticalAlign: "top",
                      }}
                    >
                      <div
                        id="ext-gen390"
                        style={{ margin: "0px", padding: "0px" }}
                      >
                        <div
                          id="item_RLo__Account"
                          className="entry blockEntry"
                          style={{
                            margin: "0px",
                            position: "relative",
                            padding: "2px",
                            cursor: "move",
                            verticalAlign: "top",
                          }}
                        >
                          <div
                            id="ext-gen86"
                            className="widget"
                            style={{
                              margin: "0px",
                              padding: "0px",
                              position: "absolute",
                              right: "0px",
                              zIndex: 2,
                              display: "none",
                            }}
                          >
                            <div
                              id="ext-gen87"
                              className="remove"
                              style={{
                                margin: "0px",
                                padding: "0px",
                                height: "18px",
                                width: "20px",
                                cursor: "pointer",
                                cssFloat: "right",
                                marginTop: "-2px",
                                backgroundPosition: "2px -14px",
                                backgroundImage:
                                  'url("/assets/downloaded/wrenchRemove.png")',
                                backgroundRepeat: "no-repeat",
                              }}
                            ></div>
                          </div>
                          <img
                            className="dragHandle"
                            src="/assets/downloaded/dragaffordance.gif"
                            style={{
                              border: "0px",
                              visibility: "hidden",
                              position: "absolute",
                              left: "0px",
                              cssFloat: "none",
                            }}
                          />
                          <div
                            className="blockOuter"
                            style={{
                              margin: "0px",
                              padding: "0px",
                              paddingLeft: "2px",
                              height: "30px",
                            }}
                          >
                            <div
                              className="block"
                              style={{
                                margin: "0px",
                                padding: "0px",
                                border: "1px solid rgb(209, 208, 208)",
                                background: "rgb(221, 221, 221)",
                                height: "100%",
                                color: "rgb(0, 0, 0)",
                                position: "relative",
                              }}
                            >
                              <div
                                className="pageLabel"
                                style={{
                                  margin: "0px",
                                  padding: "0px",
                                  whiteSpace: "nowrap",
                                  position: "absolute",
                                  top: "45%",
                                  textAlign: "center",
                                  marginTop: "-0.5em",
                                  width: "100%",
                                  zIndex: 1,
                                }}
                              >
                                <img
                                  alt="Expanded Lookup"
                                  src="/assets/downloaded/el_icon.png"
                                  style={{
                                    border: "0px",
                                    verticalAlign: "bottom",
                                    color: "rgb(0, 0, 0)",
                                    cursor: "move",
                                    fontFamily: "Arial, Helvetica, sans-serif",
                                    font: "11.36px Arial, Helvetica, sans-serif",
                                    textAlign: "center",
                                    whiteSpace: "nowrap",
                                  }}
                                />{" "}
                                <span className="labelText">Account Name</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr id="ext-gen391">
                    <td
                      className="entryCell col1"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(0, 0, 0)",
                        borderStyle: "solid",
                        borderColor: "rgb(221, 221, 221)",
                        borderImage: "initial",
                        borderWidth: "0px 1px 1px",
                        margin: "0px",
                        width: "100%",
                        border: "0px none",
                        padding: "1px 0px",
                        verticalAlign: "top",
                      }}
                    >
                      <div
                        id="ext-gen392"
                        style={{ margin: "0px", padding: "0px" }}
                      >
                        <div
                          id="item_RLo__Owner"
                          className="entry blockEntry"
                          style={{
                            margin: "0px",
                            position: "relative",
                            padding: "2px",
                            cursor: "move",
                            verticalAlign: "top",
                          }}
                        >
                          <div
                            id="ext-gen88"
                            className="widget"
                            style={{
                              margin: "0px",
                              padding: "0px",
                              position: "absolute",
                              right: "0px",
                              zIndex: 2,
                              display: "none",
                            }}
                          >
                            <div
                              id="ext-gen89"
                              className="remove"
                              style={{
                                margin: "0px",
                                padding: "0px",
                                height: "18px",
                                width: "20px",
                                cursor: "pointer",
                                cssFloat: "right",
                                marginTop: "-2px",
                                backgroundPosition: "2px -14px",
                                backgroundImage:
                                  'url("/assets/downloaded/wrenchRemove.png")',
                                backgroundRepeat: "no-repeat",
                              }}
                            ></div>
                          </div>
                          <img
                            className="dragHandle"
                            src="/assets/downloaded/dragaffordance.gif"
                            style={{
                              border: "0px",
                              visibility: "hidden",
                              position: "absolute",
                              left: "0px",
                              cssFloat: "none",
                            }}
                          />
                          <div
                            className="blockOuter"
                            style={{
                              margin: "0px",
                              padding: "0px",
                              paddingLeft: "2px",
                              height: "30px",
                            }}
                          >
                            <div
                              className="block"
                              style={{
                                margin: "0px",
                                padding: "0px",
                                border: "1px solid rgb(209, 208, 208)",
                                background: "rgb(221, 221, 221)",
                                height: "100%",
                                color: "rgb(0, 0, 0)",
                                position: "relative",
                              }}
                            >
                              <div
                                className="pageLabel"
                                style={{
                                  margin: "0px",
                                  padding: "0px",
                                  whiteSpace: "nowrap",
                                  position: "absolute",
                                  top: "45%",
                                  textAlign: "center",
                                  marginTop: "-0.5em",
                                  width: "100%",
                                  zIndex: 1,
                                }}
                              >
                                <img
                                  alt="Expanded Lookup"
                                  src="/assets/downloaded/el_icon.png"
                                  style={{
                                    border: "0px",
                                    verticalAlign: "bottom",
                                    color: "rgb(0, 0, 0)",
                                    cursor: "move",
                                    fontFamily: "Arial, Helvetica, sans-serif",
                                    font: "11.36px Arial, Helvetica, sans-serif",
                                    textAlign: "center",
                                    whiteSpace: "nowrap",
                                  }}
                                />{" "}
                                <span className="labelText">
                                  Opportunity Owner
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div
        id="ext-gen198"
        className="pbFooter secondaryPalette"
        style={{
          margin: "0px",
          padding: "0px",
          backgroundPosition: "right bottom",
          height: "9px",
          width: "9px",
          cssFloat: "right",
          backgroundImage: 'url("/assets/downloaded/bgPageBlockRight.gif")',
          backgroundRepeat: "repeat-x",
          display: "none",
          borderColor: "rgb(229, 193, 48)",
          backgroundColor: "rgb(229, 193, 48)",
        }}
      >
        <div className="bg" style={{ margin: "0px", padding: "0px" }} />
      </div>
    </div>
  );
}
