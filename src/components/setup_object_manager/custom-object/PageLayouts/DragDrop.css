/* DragDrop.css */

.section-body {
  margin: 0;
  padding: 0;
}

.draggable-item {
  margin: 0;
  position: relative;
  padding: 2px;
  cursor: move;
  vertical-align: top;
  height: 26px;
}

.draggable-item.selected {
  background-color: #b0e7ff;
}

.draggable-item.dragging {
  opacity: 0.5;
}

/* While any item is being dragged, shade the whole section body pale green */
.dragging-active .section-body {
  background-color: #d6f6bd !important;
}
.entry {
  transition: background-color 150ms ease;
}
.entry:hover {
  background-color: #f0f8ff; /* light azure on hover */
}
.entry.selected {
  background-color: #b0e7ff !important; /* solid blue when selected */
}
.entryCell {
  font-family: Arial, Helvetica, sans-serif;
  color: rgb(0, 0, 0);
  border-style: solid;
  border-color: rgb(221, 221, 221);
  border-image: initial;
  border-width: 0px 1px 1px;
  margin: 0px;
  width: 50%;
  border: 0px none;
  padding: 1px 0px;
  vertical-align: top;
  position: relative;
}

.entryCell.drag-over {
  background-color: #d6f6bd;
}

.drop-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 4px;
  background-color: rgb(0, 95, 0);
  z-index: 1000;
  pointer-events: none;
  animation: pulseIndicator 1s infinite alternate;
}

@keyframes pulseIndicator {
  from {
    opacity: 0.7;
  }
  to {
    opacity: 1;
    box-shadow: 0 0 6px rgba(0, 95, 0, 1);
  }
}

.itemLabel {
  margin: 0;
  padding: 0;
  font-weight: bold;
  float: left;
  width: 130px;
  text-align: right;
  font-size: 91%;
  color: rgb(51, 51, 51);
}

.sampleData {
  margin: 0;
  padding: 0;
  padding-left: 150px;
}

.labelIcons {
  display: inline-block;
  margin-right: 3px;
}

.dragHandle {
  border: 0;
  float: left;
  visibility: hidden !important;
  opacity: 0;
  transition: visibility 150ms ease, opacity 150ms ease;
}

/* Show drag handle only on entry hover */
.entry:hover .dragHandle {
  visibility: visible !important;
  opacity: 1;
}

.linkSample {
  text-decoration: underline;
}
