import React from "react";

export default function RelatedList4() {
  return (
    <>
      <div
        id="item_RL__RelatedNoteList"
        className="relatedList rlDrop"
        style={{
          margin: "0px",
          padding: "0px",
          borderSpacing: "0px",
          paddingTop: "2px",
          paddingBottom: "2px",
        }}
      >
        <div
          id="ext-gen226"
          className="handle"
          style={{
            margin: "0px",
            backgroundPosition: "right top",
            padding: "1px 2px 0px",
            borderLeft: "1px solid rgb(203, 203, 203)",
            cursor: "move",
            backgroundImage: 'url("/assets/downloaded/relatedtoolbg.png")',
            backgroundRepeat: "repeat-x",
            width: "150px",
            height: "18px",
          }}
        >
          <div className="widget" style={{ margin: "0px", padding: "0px" }}>
            <div
              id="ext-gen227"
              className="remove"
              style={{
                margin: "0px",
                padding: "0px",
                height: "18px",
                width: "20px",
                cursor: "pointer",
                cssFloat: "right",
                backgroundPosition: "2px -14px",
                backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
                backgroundRepeat: "no-repeat",
                marginTop: "0px",
              }}
            >
               
            </div>
          </div>
          <img
            className="dragHandle"
            src="/assets/downloaded/dragaffordance.gif"
            style={{ border: "0px", padding: "2px 0px 0px" }}
          />
           
        </div>
        <div
          id="ext-gen228"
          className="rlBody"
          style={{ margin: "0px", padding: "0px" }}
        >
          <div
            id="ext-gen229"
            className="bPageBlock secondaryPalette"
            style={{
              backgroundPosition: "left bottom",
              backgroundRepeat: "no-repeat",
              clear: "both",
              margin: "0px",
              backgroundImage: "none",
              marginBottom: "10px",
              borderTop: "3px solid rgb(116, 126, 150)",
              borderTopWidth: "3px",
              borderColor: "rgb(229, 193, 48)",
              borderBottom: "1px solid rgb(234, 234, 234)",
              borderLeft: "1px solid rgb(234, 234, 234)",
              borderRight: "1px solid rgb(234, 234, 234)",
              borderRadius: "4px",
              backgroundColor: "rgb(248, 248, 248)",
              borderBottomColor: "rgb(234, 234, 234)",
              borderLeftColor: "rgb(234, 234, 234)",
              borderRightColor: "rgb(234, 234, 234)",
              padding: "0px 5px 5px",
              paddingBottom: "5px",
            }}
          >
            <div
              id="ext-gen230"
              className="pbHeader"
              style={{
                lineHeight: "17px",
                borderBottom: "1px solid rgb(255, 255, 255)",
                padding: "0px",
                margin: "0px",
                backgroundColor: "transparent",
                paddingBottom: "0px",
                border: "0px",
              }}
            >
              <table style={{ borderSpacing: "0px", width: "100%" }}>
                <tbody>
                  <tr>
                    <td
                      className="pbTitle"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "0px",
                        color: "rgb(34, 34, 34)",
                        fontSize: "91%",
                        width: "30%",
                        padding: "0px",
                        verticalAlign: "middle",
                      }}
                    >
                      <h3
                        style={{
                          fontWeight: "bold",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          padding: "0px",
                          display: "block",
                          margin: "0px",
                          color: "rgb(0, 0, 0)",
                          fontSize: "1.3em",
                        }}
                      >
                        Notes & Attachments
                      </h3>
                    </td>
                    <td
                      className="pbButtons"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(34, 34, 34)",
                        margin: "0px",
                        padding: "0px",
                        verticalAlign: "middle",
                      }}
                    />
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              id="ext-gen231"
              className="pbBody"
              style={{
                color: "rgb(0, 0, 0)",
                margin: "0px",
                backgroundColor: "transparent",
                backgroundImage: "none",
                marginRight: "0px",
                padding: "0px 6px 6px",
              }}
            >
              This list is not customizable
            </div>
            <div
              id="ext-gen232"
              className="pbFooter secondaryPalette"
              style={{
                margin: "0px",
                padding: "0px",
                backgroundPosition: "right bottom",
                height: "9px",
                width: "9px",
                cssFloat: "right",
                backgroundImage:
                  'url("/assets/downloaded/bgPageBlockRight.gif")',
                backgroundRepeat: "repeat-x",
                display: "none",
                borderColor: "rgb(229, 193, 48)",
                backgroundColor: "rgb(229, 193, 48)",
              }}
            >
              <div className="bg" style={{ margin: "0px", padding: "0px" }} />
            </div>
          </div>
        </div>
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
html {
  margin: 0px;
  padding: 0px;
  border: 0px;
  overflow: auto;
}

body {
  background-position: left top;
  border: 0px;
  font-size: 71%;
  background: none transparent;
  margin: 0px;
  padding: 0px;
  overflow: auto;
  font-family: Helvetica;
  color: rgb(60, 61, 62);
  background-repeat: initial;
  background-color: transparent;
  height: 100%;
}
`,
        }}
      />
    </>
  );
}
