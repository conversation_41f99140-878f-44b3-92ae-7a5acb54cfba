import React from "react";

export default function RelatedList2() {
  return (
    <>
      <div
        id="item_RL__RelatedActivityList"
        className="relatedList rlDrop"
        style={{
          margin: "0px",
          padding: "0px",
          borderSpacing: "0px",
          paddingTop: "2px",
          paddingBottom: "2px",
        }}
      >
        <div
          id="ext-gen208"
          className="handle"
          style={{
            margin: "0px",
            backgroundPosition: "right top",
            padding: "1px 2px 0px",
            borderLeft: "1px solid rgb(203, 203, 203)",
            cursor: "move",
            backgroundImage: 'url("/assets/downloaded/relatedtoolbg.png")',
            backgroundRepeat: "repeat-x",
            width: "150px",
            height: "18px",
          }}
        >
          <div className="widget" style={{ margin: "0px", padding: "0px" }}>
            <div
              id="ext-gen210"
              className="properties"
              style={{
                margin: "0px",
                padding: "0px",
                height: "18px",
                width: "20px",
                cursor: "pointer",
                cssFloat: "right",
                backgroundPosition: "2px 2px",
                backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
                backgroundRepeat: "no-repeat",
                marginRight: "6px",
                marginTop: "0px",
              }}
            >
               
            </div>
            <div
              id="ext-gen209"
              className="remove"
              style={{
                margin: "0px",
                padding: "0px",
                height: "18px",
                width: "20px",
                cursor: "pointer",
                cssFloat: "right",
                backgroundPosition: "2px -14px",
                backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
                backgroundRepeat: "no-repeat",
                marginTop: "0px",
              }}
            >
               
            </div>
          </div>
          <img
            className="dragHandle"
            src="/assets/downloaded/dragaffordance.gif"
            style={{ border: "0px", padding: "2px 0px 0px" }}
          />
        </div>
        <div
          id="ext-gen211"
          className="rlBody"
          style={{ margin: "0px", padding: "0px" }}
        >
          <div
            id="ext-gen212"
            className="bPageBlock secondaryPalette"
            style={{
              backgroundPosition: "left bottom",
              backgroundRepeat: "no-repeat",
              clear: "both",
              margin: "0px",
              backgroundImage: "none",
              marginBottom: "10px",
              borderTop: "3px solid rgb(116, 126, 150)",
              borderTopWidth: "3px",
              borderColor: "rgb(229, 193, 48)",
              borderBottom: "1px solid rgb(234, 234, 234)",
              borderLeft: "1px solid rgb(234, 234, 234)",
              borderRight: "1px solid rgb(234, 234, 234)",
              borderRadius: "4px",
              backgroundColor: "rgb(248, 248, 248)",
              borderBottomColor: "rgb(234, 234, 234)",
              borderLeftColor: "rgb(234, 234, 234)",
              borderRightColor: "rgb(234, 234, 234)",
              padding: "0px 5px 5px",
              paddingBottom: "5px",
            }}
          >
            <div
              id="ext-gen213"
              className="pbHeader"
              style={{
                lineHeight: "17px",
                borderBottom: "1px solid rgb(255, 255, 255)",
                padding: "0px",
                margin: "0px",
                backgroundColor: "transparent",
                paddingBottom: "0px",
                border: "0px",
              }}
            >
              <table style={{ borderSpacing: "0px", width: "100%" }}>
                <tbody>
                  <tr>
                    <td
                      className="pbTitle"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "0px",
                        color: "rgb(34, 34, 34)",
                        fontSize: "91%",
                        width: "30%",
                        padding: "0px",
                        verticalAlign: "middle",
                      }}
                    >
                      <h3
                        style={{
                          fontWeight: "bold",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          padding: "0px",
                          display: "block",
                          margin: "0px",
                          color: "rgb(0, 0, 0)",
                          fontSize: "1.3em",
                        }}
                      >
                        Open Activities
                      </h3>
                    </td>
                    <td
                      className="pbButtons"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(34, 34, 34)",
                        margin: "0px",
                        padding: "0px",
                        verticalAlign: "middle",
                      }}
                    >
                      <div
                        id="RL__RelatedActivityList_NewTask"
                        className="btnDisabled"
                        style={{
                          cursor: "default",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          background:
                            'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                          borderRadius: "3px",
                          backgroundImage:
                            'url("/assets/downloaded/btn_sprite.png")',
                          backgroundRepeat: "repeat-x",
                          fontWeight: "bold",
                          fontSize: "0.9em",
                          backgroundPosition: "0px -90px",
                          borderColor: "rgb(196, 196, 196)",
                          color: "rgb(144, 144, 144)",
                          display: "inline",
                        }}
                      >
                        New Task
                      </div>
                      <div
                        id="RL__RelatedActivityList_NewEvent"
                        className="btnDisabled"
                        style={{
                          cursor: "default",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          background:
                            'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                          borderRadius: "3px",
                          backgroundImage:
                            'url("/assets/downloaded/btn_sprite.png")',
                          backgroundRepeat: "repeat-x",
                          fontWeight: "bold",
                          fontSize: "0.9em",
                          backgroundPosition: "0px -90px",
                          borderColor: "rgb(196, 196, 196)",
                          color: "rgb(144, 144, 144)",
                          display: "inline",
                        }}
                      >
                        New Event
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              id="ext-gen214"
              className="pbBody"
              style={{
                color: "rgb(0, 0, 0)",
                margin: "0px",
                backgroundColor: "transparent",
                backgroundImage: "none",
                marginRight: "0px",
                padding: "0px 6px 6px",
              }}
            >
              <table
                className="list"
                cellPadding="0"
                cellSpacing="0"
                style={{
                  width: "100%",
                  borderTop: "1px solid rgb(224, 227, 229)",
                  backgroundColor: "rgb(255, 255, 255)",
                  border: "1px solid rgb(224, 227, 229)",
                  borderCollapse: "collapse",
                }}
              >
                <tbody>
                  <tr className="headerRow">
                    <th
                      scope="col"
                      style={{
                        margin: "0px",
                        fontStyle: "normal",
                        textAlign: "left",
                        borderBottom: "2px solid rgb(204, 204, 204)",
                        whiteSpace: "nowrap",
                        border: "1px solid rgb(237, 237, 237)",
                        background: "rgb(242, 243, 243)",
                        borderWidth: "0px 0px 1px 1px",
                        borderColor: "rgb(224, 227, 229)",
                        padding: "5px 2px 4px 5px",
                        color: "rgb(0, 0, 0)",
                        fontSize: "0.9em",
                        fontWeight: "bold",
                        borderLeftWidth: "0px",
                      }}
                    >
                      Subject
                    </th>
                    <th
                      scope="col"
                      style={{
                        margin: "0px",
                        fontStyle: "normal",
                        textAlign: "left",
                        borderBottom: "2px solid rgb(204, 204, 204)",
                        whiteSpace: "nowrap",
                        border: "1px solid rgb(237, 237, 237)",
                        background: "rgb(242, 243, 243)",
                        borderWidth: "0px 0px 1px 1px",
                        borderColor: "rgb(224, 227, 229)",
                        padding: "5px 2px 4px 5px",
                        color: "rgb(0, 0, 0)",
                        fontSize: "0.9em",
                        fontWeight: "bold",
                      }}
                    >
                      Name
                    </th>
                    <th
                      scope="col"
                      style={{
                        margin: "0px",
                        fontStyle: "normal",
                        textAlign: "left",
                        borderBottom: "2px solid rgb(204, 204, 204)",
                        whiteSpace: "nowrap",
                        border: "1px solid rgb(237, 237, 237)",
                        background: "rgb(242, 243, 243)",
                        borderWidth: "0px 0px 1px 1px",
                        borderColor: "rgb(224, 227, 229)",
                        padding: "5px 2px 4px 5px",
                        color: "rgb(0, 0, 0)",
                        fontSize: "0.9em",
                        fontWeight: "bold",
                      }}
                    >
                      Task
                    </th>
                    <th
                      scope="col"
                      style={{
                        margin: "0px",
                        fontStyle: "normal",
                        textAlign: "left",
                        borderBottom: "2px solid rgb(204, 204, 204)",
                        whiteSpace: "nowrap",
                        border: "1px solid rgb(237, 237, 237)",
                        background: "rgb(242, 243, 243)",
                        borderWidth: "0px 0px 1px 1px",
                        borderColor: "rgb(224, 227, 229)",
                        padding: "5px 2px 4px 5px",
                        color: "rgb(0, 0, 0)",
                        fontSize: "0.9em",
                        fontWeight: "bold",
                      }}
                    >
                      Due Date
                    </th>
                    <th
                      scope="col"
                      style={{
                        margin: "0px",
                        fontStyle: "normal",
                        textAlign: "left",
                        borderBottom: "2px solid rgb(204, 204, 204)",
                        whiteSpace: "nowrap",
                        border: "1px solid rgb(237, 237, 237)",
                        background: "rgb(242, 243, 243)",
                        borderWidth: "0px 0px 1px 1px",
                        borderColor: "rgb(224, 227, 229)",
                        padding: "5px 2px 4px 5px",
                        color: "rgb(0, 0, 0)",
                        fontSize: "0.9em",
                        fontWeight: "bold",
                      }}
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      style={{
                        margin: "0px",
                        fontStyle: "normal",
                        textAlign: "left",
                        borderBottom: "2px solid rgb(204, 204, 204)",
                        whiteSpace: "nowrap",
                        border: "1px solid rgb(237, 237, 237)",
                        background: "rgb(242, 243, 243)",
                        borderWidth: "0px 0px 1px 1px",
                        borderColor: "rgb(224, 227, 229)",
                        padding: "5px 2px 4px 5px",
                        color: "rgb(0, 0, 0)",
                        fontSize: "0.9em",
                        fontWeight: "bold",
                      }}
                    >
                      Priority
                    </th>
                    <th
                      scope="col"
                      style={{
                        margin: "0px",
                        fontStyle: "normal",
                        textAlign: "left",
                        borderBottom: "2px solid rgb(204, 204, 204)",
                        whiteSpace: "nowrap",
                        border: "1px solid rgb(237, 237, 237)",
                        background: "rgb(242, 243, 243)",
                        borderWidth: "0px 0px 1px 1px",
                        borderColor: "rgb(224, 227, 229)",
                        padding: "5px 2px 4px 5px",
                        color: "rgb(0, 0, 0)",
                        fontSize: "0.9em",
                        fontWeight: "bold",
                      }}
                    >
                      Assigned To
                    </th>
                  </tr>
                  <tr className="dataRow even last first">
                    <th
                      className="dataCell"
                      scope="row"
                      style={{
                        margin: "0px",
                        fontStyle: "normal",
                        textAlign: "left",
                        padding: "4px 2px 4px 5px",
                        borderBottom: "1px solid rgb(233, 232, 223)",
                        borderColor: "rgb(236, 236, 236)",
                        whiteSpace: "normal",
                        fontWeight: "normal",
                        border: "1px solid rgb(237, 237, 237)",
                        color: "rgb(0, 0, 0)",
                        borderWidth: "0px 0px 1px",
                        verticalAlign: "middle",
                        borderBottomWidth: "0px",
                      }}
                    >
                      Sample Text
                    </th>
                    <td
                      className="dataCell"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "0px",
                        padding: "4px 2px 4px 5px",
                        borderBottom: "1px solid rgb(233, 232, 223)",
                        borderColor: "rgb(236, 236, 236)",
                        border: "1px solid rgb(237, 237, 237)",
                        color: "rgb(0, 0, 0)",
                        borderWidth: "0px 0px 1px",
                        verticalAlign: "middle",
                        borderBottomWidth: "0px",
                        borderLeftWidth: "0px",
                        borderRightWidth: "0px",
                      }}
                    >
                      Sample Text
                    </td>
                    <td
                      className="dataCell"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "0px",
                        padding: "4px 2px 4px 5px",
                        borderBottom: "1px solid rgb(233, 232, 223)",
                        borderColor: "rgb(236, 236, 236)",
                        border: "1px solid rgb(237, 237, 237)",
                        color: "rgb(0, 0, 0)",
                        borderWidth: "0px 0px 1px",
                        verticalAlign: "middle",
                        borderBottomWidth: "0px",
                        borderLeftWidth: "0px",
                        borderRightWidth: "0px",
                      }}
                    >
                      <img
                        alt="Checked"
                        src="/assets/downloaded/checkbox_checked.gif"
                        style={{ border: "0px" }}
                      />
                    </td>
                    <td
                      className="dataCell"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "0px",
                        padding: "4px 2px 4px 5px",
                        borderBottom: "1px solid rgb(233, 232, 223)",
                        borderColor: "rgb(236, 236, 236)",
                        border: "1px solid rgb(237, 237, 237)",
                        color: "rgb(0, 0, 0)",
                        borderWidth: "0px 0px 1px",
                        verticalAlign: "middle",
                        borderBottomWidth: "0px",
                        borderLeftWidth: "0px",
                        borderRightWidth: "0px",
                      }}
                    >
                      5/22/2025, 2:59 AM
                    </td>
                    <td
                      className="dataCell"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "0px",
                        padding: "4px 2px 4px 5px",
                        borderBottom: "1px solid rgb(233, 232, 223)",
                        borderColor: "rgb(236, 236, 236)",
                        border: "1px solid rgb(237, 237, 237)",
                        color: "rgb(0, 0, 0)",
                        borderWidth: "0px 0px 1px",
                        verticalAlign: "middle",
                        borderBottomWidth: "0px",
                        borderLeftWidth: "0px",
                        borderRightWidth: "0px",
                      }}
                    >
                      Sample Text
                    </td>
                    <td
                      className="dataCell"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "0px",
                        padding: "4px 2px 4px 5px",
                        borderBottom: "1px solid rgb(233, 232, 223)",
                        borderColor: "rgb(236, 236, 236)",
                        border: "1px solid rgb(237, 237, 237)",
                        color: "rgb(0, 0, 0)",
                        borderWidth: "0px 0px 1px",
                        verticalAlign: "middle",
                        borderBottomWidth: "0px",
                        borderLeftWidth: "0px",
                        borderRightWidth: "0px",
                      }}
                    >
                      Sample Text
                    </td>
                    <td
                      className="dataCell"
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "0px",
                        padding: "4px 2px 4px 5px",
                        borderBottom: "1px solid rgb(233, 232, 223)",
                        borderColor: "rgb(236, 236, 236)",
                        border: "1px solid rgb(237, 237, 237)",
                        color: "rgb(0, 0, 0)",
                        borderWidth: "0px 0px 1px",
                        verticalAlign: "middle",
                        borderBottomWidth: "0px",
                        borderLeftWidth: "0px",
                        borderRightWidth: "0px",
                      }}
                    >
                      Sarah Sample
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              id="ext-gen215"
              className="pbFooter secondaryPalette"
              style={{
                margin: "0px",
                padding: "0px",
                backgroundPosition: "right bottom",
                height: "9px",
                width: "9px",
                cssFloat: "right",
                backgroundImage:
                  'url("/assets/downloaded/bgPageBlockRight.gif")',
                backgroundRepeat: "repeat-x",
                display: "none",
                borderColor: "rgb(229, 193, 48)",
                backgroundColor: "rgb(229, 193, 48)",
              }}
            >
              <div className="bg" style={{ margin: "0px", padding: "0px" }} />
            </div>
          </div>
        </div>
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
html {
  margin: 0px;
  padding: 0px;
  border: 0px;
  overflow: auto;
}

body {
  background-position: left top;
  border: 0px;
  font-size: 71%;
  background: none transparent;
  margin: 0px;
  padding: 0px;
  overflow: auto;
  font-family: Helvetica;
  color: rgb(60, 61, 62);
  background-repeat: initial;
  background-color: transparent;
  height: 100%;
}
`,
        }}
      />
    </>
  );
}
