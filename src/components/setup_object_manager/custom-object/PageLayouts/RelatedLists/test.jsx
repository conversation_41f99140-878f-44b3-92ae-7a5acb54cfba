<fieldset
  id="ext-gen171"
  className="customButtons paDrop hover"
  style={{
    margin: "2px 2px 0px 0px",
    display: "inline",
    width: "100%",
    backgroundColor: "rgb(225, 246, 255)",
    border: "1px dotted rgb(204, 204, 204)",
    padding: "10px 0px",
    paddingLeft: "0px",
    font: "11.36px / 23.856px Helvetica",
    lineHeight: "23.856px",
  }}
>
  <legend
    style={{
      textAlign: "left",
      fontSize: "91%",
      fontWeight: "normal",
      lineHeight: "10px",
      color: "rgb(153, 153, 153)",
      margin: "0px 3px",
      padding: "0px",
    }}
  />
  <div
    className="firstSpace"
    style={{
      margin: "0px",
      padding: "0px",
      marginLeft: "10px",
      display: "inline",
    }}
  />
  <div
    id="item_QuickAction__Post__FeedItem.TextPost"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Post
  </div>
  <span> </span>
  <div
    id="item_QuickAction__Post__FeedItem.ContentPost"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    File
  </div>
  <span> </span>
  <div
    id="item_QuickAction__Create__NewTask"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    New Task
  </div>
  <span> </span>
  <div
    id="item_QuickAction__LogACall__LogACall"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Log a Call
  </div>
  <span> </span>
  <div
    id="item_QuickAction__Create__NewCase"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    New Case
  </div>
  <span> </span>
  <div
    id="item_QuickAction__Create__NewNote"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    New Note
  </div>
  <span> </span>
  <div
    id="item_QuickAction__Create__NewEvent"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    New Event
  </div>
  <span> </span>
  <div
    id="item_QuickAction__Post__FeedItem.LinkPost"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Link
  </div>
  <span> </span>
  <div
    id="item_QuickAction__Post__FeedItem.PollPost"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Poll
  </div>
  <span> </span>
  <div
    id="item_QuickAction__Post__FeedItem.QuestionPost"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Question
  </div>
  <span> </span>
  <div
    id="item_QuickAction__SendEmail__SendEmail"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Email
  </div>
  <span> </span>
  <div
    id="item_StandardButton__null__Submit"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Submit for Approval
  </div>
  <span> </span>
  <div
    id="item_StandardButton__null__ChangeRecordType"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Change Record Type
  </div>
  <span> </span>
  <div
    id="item_StandardButton__null__Clone"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Clone
  </div>
  <span> </span>
  <div
    id="item_StandardButton__null__Share"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Sharing
  </div>
  <span> </span>
  <div
    id="item_StandardButton__null__Edit"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Edit
  </div>
  <span> </span>
  <div
    id="item_StandardButton__null__Delete"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Delete
  </div>
  <span> </span>
  <div
    id="item_StandardButton__null__ChangeOwnerOne"
    className="btn customButton"
    style={{
      backgroundPosition: "left top",
      borderRight: "1px solid rgb(92, 93, 97)",
      borderBottom: "1px solid rgb(92, 93, 97)",
      borderTop: "none",
      borderLeft: "none",
      cursor: "pointer",
      fontFamily: "Arial, Helvetica, sans-serif",
      margin: "1px",
      padding: "2px 3px",
      borderWidth: "1px",
      borderStyle: "solid",
      borderImage: "initial",
      fontWeight: "bold",
      whiteSpace: "nowrap",
      background: "transparent",
      borderColor: "rgb(165, 177, 184)",
      backgroundImage: "initial",
      backgroundRepeat: "initial",
      borderRadius: "2px",
      border: "1px solid rgb(226, 226, 226)",
      paddingLeft: "10px",
      paddingRight: "30px",
      marginLeft: "3px",
      backgroundColor: "rgb(248, 248, 248)",
      color: "rgb(34, 34, 34)",
      fontSize: "1em",
      display: "inline-block",
      lineHeight: "1em",
    }}
  >
    Change Owner
  </div>
  <span> </span>
  <div
    id="ext-gen172"
    className="revertPlatformActionIconContainer"
    title="Revert to Mobile Action Defaults"
    style={{ margin: "0px", padding: "0px", display: "inline" }}
  >
    <div
      className="revertPlatformActionIcon"
      style={{
        margin: "0px",
        padding: "0px",
        cssFloat: "right",
        width: "16px",
        height: "16px",
        marginRight: "8px",
        backgroundImage:
          'url("asset/quickActions_revert.png")',
        cursor: "pointer",
        visibility: "visible",
      }}
    />
  </div>
</fieldset>;
