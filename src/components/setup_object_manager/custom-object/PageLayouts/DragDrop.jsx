/**
 * Simple Implementation Guide:
 *
 * 1. Import the component:
 *    import DragDropSalesforce from './DragDrop.jsx';
 *
 * 2. Prepare your data shape: an array of two column objects:
 *    const myColumns = [
 *      {
 *        id: 'col-1',
 *        title: 'Left Column',
 *        items: [{ id: 'i1', label: 'Label1', value: 'Value1', columnId: 'col-1' },.. ],
 *      },
 *      {
 *        id: 'col-2',
 *        title: 'Right Column',
 *        items: [{ id: 'i2', label: 'Label2', value: 'Value2', columnId: 'col-2' }, ..],
 *      },
 *    ];
 *
 * 3. Render anywhere in your module:
 *    <DragDropSalesforce
 *      sectionTitle="My Section Title"
 *      initialItems={myColumns}
 *    />
 *
 * 4. Props:
 *    - sectionTitle (string): header for the first column
 *    - initialItems (array): two-column data as shown above
 */

"use client";

import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useContext,
} from "react";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import PropTypes from "prop-types";
import "./DragDrop.css";
import { GlobalContext } from "../../../../context/GlobalContext";
import MobileCards from "./MobileCards";
import RelatedList1 from "./RelatedLists/RelatedList1";
import RelatedList2 from "./RelatedLists/RelatedList2";
import RelatedList3 from "./RelatedLists/RelatedList3";
import RelatedList4 from "./RelatedLists/RelatedList4";
import RelatedList5 from "./RelatedLists/RelatedList5";
import RelatedList6 from "./RelatedLists/RelatedList6";
import RelatedList7 from "./RelatedLists/RelatedList7";
import RelatedList8 from "./RelatedLists/RelatedList8";
import RelatedList9 from "./RelatedLists/RelatedList9";

/**
 * Main drag and drop component for Salesforce-style layouts
 *
 *
 */
export default function DragDropSalesforce({
  sectionTitle = "Opportunity Information",
  initialItems = null,
}) {
  // We're using hardcoded values for the fields now, so we don't need to access the context
  // Just keeping the useContext for future reference if needed
  useContext(GlobalContext);

  // Create default items based on the reference image
  const defaultItems = [
    {
      id: "column-1",
      title: sectionTitle,
      items: [
        // Left column fields in exact order from the image
        {
          id: "item-opportunity-owner",
          label: "Opportunity Owner",
          value: "Sample Text",
          columnId: "column-1",
          isRequired: false,
          isBlueIcon: false,
        },
        {
          id: "item-private",
          label: "Private",
          value: "✓", // Checkmark
          columnId: "column-1",
          isRequired: false,
          isBlueIcon: false,
        },
        {
          id: "item-opportunity-name",
          label: "Opportunity Name",
          value: "Sample Text",
          columnId: "column-1",
          isRequired: true, // Red asterisk
          isBlueIcon: true, // Blue dot
        },
        {
          id: "item-account-name",
          label: "Account Name",
          value: "Sample Text",
          columnId: "column-1",
          isRequired: false,
          isBlueIcon: true, // Blue dot
        },
        {
          id: "item-type",
          label: "Type",
          value: "Sample Text",
          columnId: "column-1",
          isRequired: true, // Red asterisk
          isBlueIcon: false,
        },
        {
          id: "item-lead-source",
          label: "Lead Source",
          value: "Sample Text",
          columnId: "column-1",
          isRequired: false,
          isBlueIcon: false,
        },
        {
          id: "item-primary-campaign-source",
          label: "Primary Campaign Source",
          value: "Sample Text",
          columnId: "column-1",
          isRequired: false,
          isBlueIcon: false,
        },
      ],
    },
    {
      id: "column-2",
      title: "",
      items: [
        // Right column fields in exact order from the image
        {
          id: "item-amount",
          label: "Amount",
          value: "$123.45",
          columnId: "column-2",
          isRequired: true, // Red asterisk
          isBlueIcon: false,
        },
        {
          id: "item-expected-revenue",
          label: "Expected Revenue",
          value: "$123.45",
          columnId: "column-2",
          isRequired: false,
          isBlueIcon: false,
        },
        {
          id: "item-close-date",
          label: "Close Date",
          value: "5/22/2025",
          columnId: "column-2",
          isRequired: true, // Red asterisk
          isBlueIcon: true, // Blue dot
        },
        {
          id: "item-next-step",
          label: "Next Step",
          value: "Sample Text",
          columnId: "column-2",
          isRequired: false,
          isBlueIcon: false,
        },
        {
          id: "item-stage",
          label: "Stage",
          value: "Sample Text",
          columnId: "column-2",
          isRequired: true, // Red asterisk
          isBlueIcon: true, // Blue dot
        },
        {
          id: "item-probability",
          label: "Probability (%)",
          value: "165%",
          columnId: "column-2",
          isRequired: false,
          isBlueIcon: true, // Blue dot
        },
      ],
    },
  ];

  // Other Information section data
  const otherInfoItems = [
    {
      id: "column-1",
      title: "Other Information",
      items: [
        {
          id: "item-order-number",
          label: "Order Number",
          value: "Sample Text",
          columnId: "column-1",
          isRequired: false,
          isBlueIcon: false,
        },
        {
          id: "item-tracking-number",
          label: "Tracking Number",
          value: "Sample Text",
          columnId: "column-1",
          isRequired: false,
          isBlueIcon: false,
        },
      ],
    },
    {
      id: "column-2",
      title: "",
      items: [
        {
          id: "item-delivery-installation-status",
          label: "Delivery/Installation Status",
          value: "Sample Text",
          columnId: "column-2",
          isRequired: false,
          isBlueIcon: false,
        },
      ],
    },
  ];

  // Additional Information section data
  const additionalInfoItems = [
    {
      id: "column-1",
      title: "Additional Information",
      items: [
        {
          id: "item-main-competitor",
          label: "Main Competitor(s)",
          value: "Sample Text",
          columnId: "column-1",
          isRequired: false,
          isBlueIcon: false,
        },
      ],
    },
    {
      id: "column-2",
      title: "",
      items: [
        {
          id: "item-current-generator",
          label: "Current Generator(s)",
          value: "Sample Text",
          columnId: "column-2",
          isRequired: false,
          isBlueIcon: false,
        },
      ],
    },
  ];

  // System Information section data
  const systemInfoItems = [
    {
      id: "column-1",
      title: "System Information",
      items: [
        {
          id: "item-created-by",
          label: "Created By",
          value: "Sample Text",
          columnId: "column-1",
          isRequired: false,
          isBlueIcon: false,
          isReadOnly: true,
        },
      ],
    },
    {
      id: "column-2",
      title: "",
      items: [
        {
          id: "item-last-modified-by",
          label: "Last Modified By",
          value: "Sample Text",
          columnId: "column-2",
          isRequired: false,
          isBlueIcon: false,
          isReadOnly: true,
        },
      ],
    },
  ];

  // Description Information section data
  const descriptionInfoItems = [
    {
      id: "column-1",
      title: "Description Information",
      items: [
        {
          id: "item-description",
          label: "Description",
          value: "Sample Text",
          columnId: "column-1",
          isRequired: false,
          isBlueIcon: false,
        },
      ],
    },
    {
      id: "column-2",
      title: "",
      items: [],
    },
  ];

  // Custom Links section data
  const customLinksItems = [
    {
      id: "column-1",
      title: "Custom Links",
      items: [
        {
          id: "item-delivery-status",
          label: "Delivery Status",
          value: "",
          columnId: "column-1",
          isRequired: false,
          isBlueIcon: false,
        },
      ],
    },
    {
      id: "column-2",
      title: "",
      items: [],
    },
  ];

  // Initialize state for each section
  const [opportunityInfoColumns, setOpportunityInfoColumns] = useState(
    initialItems || defaultItems
  );
  const [otherInfoColumns, setOtherInfoColumns] = useState(otherInfoItems);
  const [additionalInfoColumns, setAdditionalInfoColumns] =
    useState(additionalInfoItems);
  const [systemInfoColumns, setSystemInfoColumns] = useState(systemInfoItems);
  const [descriptionInfoColumns, setDescriptionInfoColumns] =
    useState(descriptionInfoItems);
  const [customLinksColumns, setCustomLinksColumns] =
    useState(customLinksItems);

  // State for selected items
  const [selectedItems, setSelectedItems] = useState([]);

  // Function to move an item between columns - using useCallback to prevent unnecessary re-renders
  const moveItem = useCallback(
    (itemId, sourceColumnId, targetColumnId, targetIndex, sectionType) => {
      // Determine which state updater to use based on section type
      const setColumns = (() => {
        switch (sectionType) {
          case "opportunity":
            return setOpportunityInfoColumns;
          case "other":
            return setOtherInfoColumns;
          case "additional":
            return setAdditionalInfoColumns;
          case "system":
            return setSystemInfoColumns;
          case "description":
            return setDescriptionInfoColumns;
          case "customLinks":
            return setCustomLinksColumns;
          default:
            return setOpportunityInfoColumns;
        }
      })();

      setColumns((prevColumns) => {
        // Create a deep copy of the columns
        const newColumns = JSON.parse(JSON.stringify(prevColumns));

        // Find the source column
        const sourceColumn = newColumns.find(
          (col) => col.id === sourceColumnId
        );

        // Find the target column
        const targetColumn = newColumns.find(
          (col) => col.id === targetColumnId
        );

        if (!sourceColumn || !targetColumn) return prevColumns;

        // Find the item in the source column
        const itemIndex = sourceColumn.items.findIndex(
          (item) => item.id === itemId
        );

        if (itemIndex === -1) return prevColumns;

        // Remove the item from the source column
        const [movedItem] = sourceColumn.items.splice(itemIndex, 1);

        // Update the item's columnId
        movedItem.columnId = targetColumnId;

        // Insert the item at the target index in the target column
        targetColumn.items.splice(targetIndex, 0, movedItem);

        return newColumns;
      });
    },
    []
  );

  // Toggle item selection - using useCallback to prevent unnecessary re-renders
  const toggleItemSelection = useCallback((itemId) => {
    setSelectedItems((prev) => {
      if (prev.includes(itemId)) {
        return prev.filter((id) => id !== itemId);
      } else {
        return [...prev, itemId];
      }
    });
  }, []);

  return (
    <DndProvider backend={HTML5Backend}>
      {/* Opportunity Information Section */}
      <div className="section-body">
        <table cellPadding="0" cellSpacing="0" style={{ width: "100%" }}>
          <tbody>
            <tr>
              {opportunityInfoColumns.map((column) => (
                <Column
                  key={column.id}
                  column={column}
                  moveItem={moveItem}
                  selectedItems={selectedItems}
                  toggleItemSelection={toggleItemSelection}
                  sectionType="opportunity"
                />
              ))}
            </tr>
          </tbody>
        </table>
      </div>

      {/* Other Information Section */}
      <div
        className="section fieldDrop"
        style={{
          margin: "0px",
          padding: "0px",
          paddingBottom: "3px",
          border: "none",
          marginTop: "10px",
        }}
      >
        <div
          className="section-header x-unselectable tertiaryPalette editOnly"
          style={{
            margin: "0px",
            padding: "3px 2px 2px 6px",
            fontWeight: "bold",
            background:
              'url("/assets/downloaded/dragaffordance.gif") 3px 3px no-repeat rgb(102, 102, 102)',
            cursor: "move",
            paddingLeft: "10px",
            borderColor: "rgb(142, 157, 190)",
            borderStyle: "solid dotted dotted",
            borderWidth: "2px 0px 0px",
            backgroundColor: "transparent",
            borderLeftColor: "rgb(204, 204, 204)",
            borderBottomColor: "rgb(204, 204, 204)",
            borderRightColor: "rgb(204, 204, 204)",
            borderTop: "2px solid rgb(204, 204, 204)",
            color: "rgb(153, 153, 153)",
          }}
        >
          <div
            className="x-tool x-tool-gear"
            style={{
              margin: "0px",
              padding: "0px",
              overflow: "hidden",
              background: "no-repeat transparent",
              marginLeft: "0px",
              visibility: "hidden",
              height: "18px",
              width: "20px",
              cursor: "pointer",
              cssFloat: "right",
              marginTop: "-2px",
              backgroundPosition: "2px 2px",
              backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
              backgroundRepeat: "no-repeat",
            }}
          ></div>
          <div
            className="x-tool x-tool-remove"
            style={{
              margin: "0px",
              padding: "0px",
              overflow: "hidden",
              background: "no-repeat transparent",
              marginLeft: "0px",
              visibility: "hidden",
              height: "18px",
              width: "20px",
              cursor: "pointer",
              cssFloat: "right",
              marginTop: "-2px",
              backgroundPosition: "2px -14px",
              backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
              backgroundRepeat: "no-repeat",
            }}
          ></div>
          <span className="section-header-text">
            Other Information
            <span
              className="headerInfo"
              style={{
                marginLeft: "0.5em",
                marginBottom: "2px",
                fontSize: "91%",
                fontWeight: "bold",
                color: "rgb(153, 153, 153)",
              }}
            >
              (Header visible on edit only)
            </span>
          </span>
        </div>
        <div
          className="section-bwrap"
          style={{ margin: "0px", padding: "0px" }}
        >
          <div
            className="section-body"
            style={{ margin: "0px", padding: "0px" }}
          >
            <table cellPadding="0" cellSpacing="0" style={{ width: "100%" }}>
              <tbody>
                <tr>
                  {otherInfoColumns.map((column) => (
                    <Column
                      key={column.id}
                      column={column}
                      moveItem={moveItem}
                      selectedItems={selectedItems}
                      toggleItemSelection={toggleItemSelection}
                      sectionType="other"
                    />
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Additional Information Section */}
      <div
        className="section fieldDrop"
        style={{
          margin: "0px",
          padding: "0px",
          paddingBottom: "3px",
          border: "none",
          marginTop: "10px",
        }}
      >
        <div
          className="section-header x-unselectable tertiaryPalette editOnly"
          style={{
            margin: "0px",
            padding: "3px 2px 2px 6px",
            fontWeight: "bold",
            background:
              'url("/assets/downloaded/dragaffordance.gif") 3px 3px no-repeat rgb(102, 102, 102)',
            cursor: "move",
            paddingLeft: "10px",
            borderColor: "rgb(142, 157, 190)",
            borderStyle: "solid dotted dotted",
            borderWidth: "2px 0px 0px",
            backgroundColor: "transparent",
            borderLeftColor: "rgb(204, 204, 204)",
            borderBottomColor: "rgb(204, 204, 204)",
            borderRightColor: "rgb(204, 204, 204)",
            borderTop: "2px solid rgb(204, 204, 204)",
            color: "rgb(153, 153, 153)",
          }}
        >
          <div
            className="x-tool x-tool-gear"
            style={{
              margin: "0px",
              padding: "0px",
              overflow: "hidden",
              background: "no-repeat transparent",
              marginLeft: "0px",
              visibility: "hidden",
              height: "18px",
              width: "20px",
              cursor: "pointer",
              cssFloat: "right",
              marginTop: "-2px",
              backgroundPosition: "2px 2px",
              backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
              backgroundRepeat: "no-repeat",
            }}
          ></div>
          <div
            className="x-tool x-tool-remove"
            style={{
              margin: "0px",
              padding: "0px",
              overflow: "hidden",
              background: "no-repeat transparent",
              marginLeft: "0px",
              visibility: "hidden",
              height: "18px",
              width: "20px",
              cursor: "pointer",
              cssFloat: "right",
              marginTop: "-2px",
              backgroundPosition: "2px -14px",
              backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
              backgroundRepeat: "no-repeat",
            }}
          ></div>
          <span className="section-header-text">
            Additional Information
            <span
              className="headerInfo"
              style={{
                marginLeft: "0.5em",
                marginBottom: "2px",
                fontSize: "91%",
                fontWeight: "bold",
                color: "rgb(153, 153, 153)",
              }}
            >
              (Header visible on edit only)
            </span>
          </span>
        </div>
        <div
          className="section-bwrap"
          style={{ margin: "0px", padding: "0px" }}
        >
          <div
            className="section-body"
            style={{ margin: "0px", padding: "0px" }}
          >
            <table cellPadding="0" cellSpacing="0" style={{ width: "100%" }}>
              <tbody>
                <tr>
                  {additionalInfoColumns.map((column) => (
                    <Column
                      key={column.id}
                      column={column}
                      moveItem={moveItem}
                      selectedItems={selectedItems}
                      toggleItemSelection={toggleItemSelection}
                      sectionType="additional"
                    />
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* System Information Section */}
      <div
        className="section fieldDrop"
        style={{
          margin: "0px",
          padding: "0px",
          paddingBottom: "3px",
          border: "none",
          marginTop: "10px",
        }}
      >
        <div
          className="section-header x-unselectable tertiaryPalette editOnly"
          style={{
            margin: "0px",
            padding: "3px 2px 2px 6px",
            fontWeight: "bold",
            background:
              'url("/assets/downloaded/dragaffordance.gif") 3px 3px no-repeat rgb(102, 102, 102)',
            cursor: "move",
            paddingLeft: "10px",
            borderColor: "rgb(142, 157, 190)",
            borderStyle: "solid dotted dotted",
            borderWidth: "2px 0px 0px",
            backgroundColor: "transparent",
            borderLeftColor: "rgb(204, 204, 204)",
            borderBottomColor: "rgb(204, 204, 204)",
            borderRightColor: "rgb(204, 204, 204)",
            borderTop: "2px solid rgb(204, 204, 204)",
            color: "rgb(153, 153, 153)",
          }}
        >
          <div
            className="x-tool x-tool-gear"
            style={{
              margin: "0px",
              padding: "0px",
              overflow: "hidden",
              background: "no-repeat transparent",
              marginLeft: "0px",
              visibility: "hidden",
              height: "18px",
              width: "20px",
              cursor: "pointer",
              cssFloat: "right",
              marginTop: "-2px",
              backgroundPosition: "2px 2px",
              backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
              backgroundRepeat: "no-repeat",
            }}
          ></div>
          <div
            className="x-tool x-tool-remove"
            style={{
              margin: "0px",
              padding: "0px",
              overflow: "hidden",
              background: "no-repeat transparent",
              marginLeft: "0px",
              visibility: "hidden",
              height: "18px",
              width: "20px",
              cursor: "pointer",
              cssFloat: "right",
              marginTop: "-2px",
              backgroundPosition: "2px -14px",
              backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
              backgroundRepeat: "no-repeat",
            }}
          ></div>
          <span className="section-header-text">
            System Information
            <span
              className="headerInfo"
              style={{
                marginLeft: "0.5em",
                marginBottom: "2px",
                fontSize: "91%",
                fontWeight: "bold",
                color: "rgb(153, 153, 153)",
              }}
            >
              (Header visible on edit only)
            </span>
          </span>
        </div>
        <div
          className="section-bwrap"
          style={{ margin: "0px", padding: "0px" }}
        >
          <div
            className="section-body"
            style={{ margin: "0px", padding: "0px" }}
          >
            <table cellPadding="0" cellSpacing="0" style={{ width: "100%" }}>
              <tbody>
                <tr>
                  {systemInfoColumns.map((column) => (
                    <Column
                      key={column.id}
                      column={column}
                      moveItem={moveItem}
                      selectedItems={selectedItems}
                      toggleItemSelection={toggleItemSelection}
                      sectionType="system"
                    />
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Description Information Section */}
      <div
        className="section fieldDrop"
        style={{
          margin: "0px",
          padding: "0px",
          paddingBottom: "3px",
          border: "none",
          marginTop: "10px",
        }}
      >
        <div
          className="section-header x-unselectable tertiaryPalette editOnly"
          style={{
            margin: "0px",
            padding: "3px 2px 2px 6px",
            fontWeight: "bold",
            background:
              'url("/assets/downloaded/dragaffordance.gif") 3px 3px no-repeat rgb(102, 102, 102)',
            cursor: "move",
            paddingLeft: "10px",
            borderColor: "rgb(142, 157, 190)",
            borderStyle: "solid dotted dotted",
            borderWidth: "2px 0px 0px",
            backgroundColor: "transparent",
            borderLeftColor: "rgb(204, 204, 204)",
            borderBottomColor: "rgb(204, 204, 204)",
            borderRightColor: "rgb(204, 204, 204)",
            borderTop: "2px solid rgb(204, 204, 204)",
            color: "rgb(153, 153, 153)",
          }}
        >
          <div
            className="x-tool x-tool-gear"
            style={{
              margin: "0px",
              padding: "0px",
              overflow: "hidden",
              background: "no-repeat transparent",
              marginLeft: "0px",
              visibility: "hidden",
              height: "18px",
              width: "20px",
              cursor: "pointer",
              cssFloat: "right",
              marginTop: "-2px",
              backgroundPosition: "2px 2px",
              backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
              backgroundRepeat: "no-repeat",
            }}
          ></div>
          <div
            className="x-tool x-tool-remove"
            style={{
              margin: "0px",
              padding: "0px",
              overflow: "hidden",
              background: "no-repeat transparent",
              marginLeft: "0px",
              visibility: "hidden",
              height: "18px",
              width: "20px",
              cursor: "pointer",
              cssFloat: "right",
              marginTop: "-2px",
              backgroundPosition: "2px -14px",
              backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
              backgroundRepeat: "no-repeat",
            }}
          ></div>
          <span className="section-header-text">
            Description Information
            <span
              className="headerInfo"
              style={{
                marginLeft: "0.5em",
                marginBottom: "2px",
                fontSize: "91%",
                fontWeight: "bold",
                color: "rgb(153, 153, 153)",
              }}
            >
              (Header visible on edit only)
            </span>
          </span>
        </div>
        <div
          className="section-bwrap"
          style={{ margin: "0px", padding: "0px" }}
        >
          <div
            className="section-body"
            style={{ margin: "0px", padding: "0px" }}
          >
            <table cellPadding="0" cellSpacing="0" style={{ width: "100%" }}>
              <tbody>
                <tr>
                  {descriptionInfoColumns.map((column) => (
                    <Column
                      key={column.id}
                      column={column}
                      moveItem={moveItem}
                      selectedItems={selectedItems}
                      toggleItemSelection={toggleItemSelection}
                      sectionType="description"
                    />
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Custom Links Section */}
      <div
        className="section fieldDrop"
        style={{
          margin: "0px",
          padding: "0px",
          paddingBottom: "3px",
          border: "none",
          marginTop: "10px",
        }}
      >
        <div
          className="section-header x-unselectable tertiaryPalette editOnly"
          style={{
            margin: "0px",
            padding: "3px 2px 2px 6px",
            fontWeight: "bold",
            background:
              'url("/assets/downloaded/dragaffordance.gif") 3px 3px no-repeat rgb(102, 102, 102)',
            cursor: "move",
            paddingLeft: "10px",
            borderColor: "rgb(142, 157, 190)",
            borderStyle: "solid dotted dotted",
            borderWidth: "2px 0px 0px",
            backgroundColor: "transparent",
            borderLeftColor: "rgb(204, 204, 204)",
            borderBottomColor: "rgb(204, 204, 204)",
            borderRightColor: "rgb(204, 204, 204)",
            borderTop: "2px solid rgb(204, 204, 204)",
            color: "rgb(153, 153, 153)",
          }}
        >
          <div
            className="x-tool x-tool-gear"
            style={{
              margin: "0px",
              padding: "0px",
              overflow: "hidden",
              background: "no-repeat transparent",
              marginLeft: "0px",
              visibility: "hidden",
              height: "18px",
              width: "20px",
              cursor: "pointer",
              cssFloat: "right",
              marginTop: "-2px",
              backgroundPosition: "2px 2px",
              backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
              backgroundRepeat: "no-repeat",
            }}
          ></div>
          <div
            className="x-tool x-tool-remove"
            style={{
              margin: "0px",
              padding: "0px",
              overflow: "hidden",
              background: "no-repeat transparent",
              marginLeft: "0px",
              visibility: "hidden",
              height: "18px",
              width: "20px",
              cursor: "pointer",
              cssFloat: "right",
              marginTop: "-2px",
              backgroundPosition: "2px -14px",
              backgroundImage: 'url("/assets/downloaded/wrenchRemove.png")',
              backgroundRepeat: "no-repeat",
            }}
          ></div>
          <span className="section-header-text">
            Custom Links
            <span
              className="headerInfo"
              style={{
                marginLeft: "0.5em",
                marginBottom: "2px",
                fontSize: "91%",
                fontWeight: "bold",
                color: "rgb(153, 153, 153)",
              }}
            >
              (Header not visible)
            </span>
          </span>
        </div>
        <div
          className="section-bwrap"
          style={{ margin: "0px", padding: "0px" }}
        >
          <div
            className="section-body"
            style={{ margin: "0px", padding: "0px" }}
          >
            <table cellPadding="0" cellSpacing="0" style={{ width: "100%" }}>
              <tbody>
                <tr>
                  {customLinksColumns.map((column) => (
                    <Column
                      key={column.id}
                      column={column}
                      moveItem={moveItem}
                      selectedItems={selectedItems}
                      toggleItemSelection={toggleItemSelection}
                      sectionType="customLinks"
                    />
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Mobile Cards Section */}
      <MobileCards />
      <h3
        style={{
          display: "inline",
          fontWeight: "bold",
          fontFamily: "Arial, Helvetica, sans-serif",
          margin: "0px",
          padding: "0px",
          fontSize: "1.2em",
          paddingLeft: "10px",
        }}
      >
        Related Lists
      </h3>

      {/* Related Lists Section */}
      <RelatedList1 />
      <RelatedList2 />
      <RelatedList3 />
      <RelatedList4 />
      <RelatedList5 />
      <RelatedList6 />
      <RelatedList7 />
      <RelatedList8 />
      <RelatedList9 />
    </DndProvider>
  );
}

// Column component - using React.memo to prevent unnecessary re-renders
const Column = React.memo(function Column({
  column,
  moveItem,
  selectedItems,
  toggleItemSelection,
  sectionType,
}) {
  const ref = useRef(null);
  const [dropIndicator, setDropIndicator] = useState({
    show: false,
    index: -1,
  });

  const [{ isOver }, drop] = useDrop({
    accept: "ITEM",
    hover: (_, monitor) => {
      if (!ref.current) return;

      // Get the mouse position
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;

      // Get the column's bounding rectangle
      const columnRect = ref.current.getBoundingClientRect();

      // Calculate the relative Y position within the column
      const relativeY = clientOffset.y - columnRect.top;

      // Find the item that the cursor is closest to
      let targetIndex = 0;
      let closestDistance = Number.MAX_VALUE;

      // Get all item elements in the column
      const itemElements = Array.from(
        ref.current.querySelectorAll(".draggable-item")
      );

      if (itemElements.length === 0) {
        setDropIndicator({ show: true, index: 0 });
        return;
      }

      itemElements.forEach((itemElement, index) => {
        const itemRect = itemElement.getBoundingClientRect();
        const itemMiddle = itemRect.top + itemRect.height / 2 - columnRect.top;

        // Calculate distance from cursor to the middle of this item
        const distance = Math.abs(relativeY - itemMiddle);

        // If this is closer than our previous closest, update
        if (distance < closestDistance) {
          closestDistance = distance;

          // If cursor is above the middle of the item, insert before it
          // If cursor is below the middle, insert after it
          targetIndex = relativeY < itemMiddle ? index : index + 1;
        }
      });

      setDropIndicator({ show: true, index: targetIndex });
    },
    drop: (item, monitor) => {
      if (!ref.current) return;

      // If the drop was handled by a nested target, don't do anything
      if (monitor.didDrop()) return;

      // Get the mouse position
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;

      // Get the column's bounding rectangle
      const columnRect = ref.current.getBoundingClientRect();

      // Calculate the relative Y position within the column
      const relativeY = clientOffset.y - columnRect.top;

      // Find the item that the cursor is closest to
      let targetIndex = 0;
      let closestDistance = Number.MAX_VALUE;

      // Get all item elements in the column
      const itemElements = Array.from(
        ref.current.querySelectorAll(".draggable-item")
      );

      if (itemElements.length === 0) {
        moveItem(item.id, item.columnId, column.id, 0, sectionType);
        setDropIndicator({ show: false, index: -1 });
        return;
      }

      itemElements.forEach((itemElement, index) => {
        const itemRect = itemElement.getBoundingClientRect();
        const itemMiddle = itemRect.top + itemRect.height / 2 - columnRect.top;

        // Calculate distance from cursor to the middle of this item
        const distance = Math.abs(relativeY - itemMiddle);

        // If this is closer than our previous closest, update
        if (distance < closestDistance) {
          closestDistance = distance;

          // If cursor is above the middle of the item, insert before it
          // If cursor is below the middle, insert after it
          targetIndex = relativeY < itemMiddle ? index : index + 1;
        }
      });

      // Move the item to the calculated position
      moveItem(item.id, item.columnId, column.id, targetIndex, sectionType);
      setDropIndicator({ show: false, index: -1 });
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
    }),
  });

  // Reset drop indicator when not dragging over
  useEffect(() => {
    if (!isOver) {
      setDropIndicator({ show: false, index: -1 });
    }
  }, [isOver]);

  drop(ref);

  // Prepare the items with drop indicators
  const itemsWithIndicators = [];

  // Only add indicators if we're dragging over
  if (isOver && dropIndicator.show) {
    column.items.forEach((item, index) => {
      // Add drop indicator before item if needed
      if (dropIndicator.index === index) {
        itemsWithIndicators.push({
          id: `indicator-${index}`,
          type: "indicator",
          position: index,
        });
      }

      // Add the actual item
      itemsWithIndicators.push({
        id: item.id,
        type: "item",
        data: item,
        position: index,
      });
    });

    // Add indicator at the end if needed
    if (dropIndicator.index === column.items.length) {
      itemsWithIndicators.push({
        id: `indicator-${column.items.length}`,
        type: "indicator",
        position: column.items.length,
      });
    }
  } else {
    // Just add the items without indicators
    column.items.forEach((item, index) => {
      itemsWithIndicators.push({
        id: item.id,
        type: "item",
        data: item,
        position: index,
      });
    });
  }

  return (
    <td
      ref={ref}
      className={`entryCell ${column.id === "column-1" ? "col1" : "col2"} ${
        isOver ? "drag-over" : ""
      }`}
      style={{
        fontFamily: "Arial, Helvetica, sans-serif",
        color: "rgb(0, 0, 0)",
        borderStyle: "solid",
        borderColor: "rgb(221, 221, 221)",
        borderImage: "initial",
        borderWidth: "0px 1px 1px",
        margin: "0px",
        width: "50%",
        border: "0px none",
        padding: "1px 0px",
        verticalAlign: "top",
        position: "relative",
      }}
    >
      <div style={{ margin: "0px", padding: "0px", position: "relative" }}>
        {itemsWithIndicators.map((item) => {
          if (item.type === "indicator") {
            return (
              <div
                key={item.id}
                className="drop-indicator"
                style={{
                  top: `${item.position * 30}px`,
                }}
              />
            );
          } else {
            return (
              <DraggableItem
                key={item.id}
                item={item.data}
                index={item.position}
                isSelected={selectedItems.includes(item.id)}
                toggleSelection={toggleItemSelection}
              />
            );
          }
        })}
      </div>
    </td>
  );
});

// DraggableItem component - using React.memo to prevent unnecessary re-renders
const DraggableItem = React.memo(function DraggableItem({
  item,
  index,
  isSelected,
  toggleSelection,
}) {
  const ref = useRef(null);
  const [{ isDragging }, drag] = useDrag({
    type: "ITEM",
    item: () => {
      document.body.classList.add("dragging-active");
      return { id: item.id, columnId: item.columnId, index };
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: () => {
      document.body.classList.remove("dragging-active");
    },
  });

  drag(ref);

  return (
    <div
      ref={ref}
      className={`entry draggable-item ${isSelected ? "selected" : ""} ${
        isDragging ? "dragging" : ""
      }`}
      style={{
        margin: "0px",
        position: "relative",
        padding: "2px",
        cursor: "move",
        verticalAlign: "top",
        height: "26px", // Fixed height for consistent positioning
      }}
      onClick={() => toggleSelection(item.id)}
    >
      <img
        className="dragHandle"
        src="/assets/downloaded/dragaffordance.gif"
        style={{
          border: "0px",
          cssFloat: "left",
          visibility: "visible",
        }}
        alt="Drag handle"
      />
      <div
        className="itemLabel"
        style={{
          margin: "0px",
          padding: "0px",
          fontWeight: "bold",
          cssFloat: "left",
          width: "130px",
          textAlign: "right",
          fontSize: "91%",
          color: "rgb(51, 51, 51)",
        }}
      >
        <span className="labelIcons">
          {item.isRequired && (
            <img
              className="Required"
              height={12}
              width={12}
              src="/assets/downloaded/required12.gif"
              style={{ border: "0px" }}
              alt="Required"
            />
          )}
          {item.isBlueIcon && (
            <img
              className="AlwaysOnLayout"
              height={12}
              width={12}
              src="/assets/downloaded/alwaysdisplay12.png"
              style={{ border: "0px" }}
              alt="Always on layout"
            />
          )}
        </span>
        <span className="labelText">{item.label}</span>
      </div>
      <div
        className="sampleData"
        style={{
          margin: "0px",
          padding: "0px",
          paddingLeft: "150px",
        }}
      >
        {item.value}
      </div>
    </div>
  );
});

// PropTypes for type checking
DragDropSalesforce.propTypes = {
  sectionTitle: PropTypes.string,
  initialItems: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
      items: PropTypes.arrayOf(
        PropTypes.shape({
          id: PropTypes.string.isRequired,
          label: PropTypes.string.isRequired,
          value: PropTypes.string.isRequired,
          columnId: PropTypes.string.isRequired,
          isRequired: PropTypes.bool,
          isBlueIcon: PropTypes.bool,
        })
      ).isRequired,
    })
  ),
};

Column.propTypes = {
  column: PropTypes.shape({
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    items: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string.isRequired,
        label: PropTypes.string.isRequired,
        value: PropTypes.string.isRequired,
        columnId: PropTypes.string.isRequired,
        isRequired: PropTypes.bool,
        isBlueIcon: PropTypes.bool,
      })
    ).isRequired,
  }).isRequired,
  moveItem: PropTypes.func.isRequired,
  selectedItems: PropTypes.array.isRequired,
  toggleItemSelection: PropTypes.func.isRequired,
  sectionType: PropTypes.string.isRequired,
};

DraggableItem.propTypes = {
  item: PropTypes.shape({
    id: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    value: PropTypes.string.isRequired,
    columnId: PropTypes.string.isRequired,
    isRequired: PropTypes.bool,
    isBlueIcon: PropTypes.bool,
  }).isRequired,
  index: PropTypes.number.isRequired,
  isSelected: PropTypes.bool.isRequired,
  toggleSelection: PropTypes.func.isRequired,
};
