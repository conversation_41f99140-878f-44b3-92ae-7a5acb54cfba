import React, { useState, useEffect, useContext } from "react";
import { useNavigate, useParams } from "react-router-dom";
import "./CreatedPageLayouts.css";
import DragDropSalesforce from "./DragDrop";
import SavingLayoutPopup from "./SavingLayoutPopup";
import { GlobalContext } from "../../../../context/GlobalContext";

export default function Component() {
  const [showSavingPopup, setShowSavingPopup] = useState(false);
  const [progress, setProgress] = useState(0);
  const navigate = useNavigate();
  const { apiName, layoutId } = useParams();
  const { allVariableData, setAllVariableData, saveToLocalStorage } =
    useContext(GlobalContext);

  const savePageLayout = () => {
    // This function would save the current page layout data
    // For now, we'll just log that the save is happening
    console.log("Saving page layout...");
    // In a real implementation, this would save the layout data to the backend
  };

  const handleSave = (isQuickSave = false) => {
    // Show the saving popup
    setShowSavingPopup(true);
    setProgress(0);

    savePageLayout();

    // Different timing for quick save vs regular save
    const incrementAmount = isQuickSave ? 20 : 10;
    const intervalTime = isQuickSave ? 200 : 300;

    // Simulate progress with a timer
    const interval = setInterval(() => {
      setProgress((prevProgress) => {
        const newProgress = prevProgress + incrementAmount;

        // When progress reaches 100%, navigate to PageLayouts route
        if (newProgress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            setShowSavingPopup(false);
            // Navigate back to the PageLayouts route for the current object
            navigate(`/setup/objectManager/${apiName}/page-layouts`);
          }, 500);
          return 100;
        }

        return newProgress;
      });
    }, intervalTime);
  };

  useEffect(() => {
    // Add event listeners to Save and Quick Save buttons
    const saveButton = document.querySelector("#ext-gen60"); // Save button
    const quickSaveButton = document.querySelector("#ext-gen62"); // Quick Save button

    if (saveButton) {
      saveButton.addEventListener("click", () => handleSave(false));
    }

    if (quickSaveButton) {
      quickSaveButton.addEventListener("click", () => handleSave(true));
    }

    // Cleanup function to remove event listeners
    return () => {
      if (saveButton) {
        saveButton.removeEventListener("click", () => handleSave(false));
      }
      if (quickSaveButton) {
        quickSaveButton.removeEventListener("click", () => handleSave(true));
      }
    };
  }, []); // Empty dependency array since we only want to run this once

  return (
    <div
      className="page-layout-editor-container"
      style={{ width: "100%", overflowX: "hidden" }}
    >
      {showSavingPopup && <SavingLayoutPopup progress={progress} />}
      <div
        id="troughPanel"
        className="trough x-theme-aloha-esque trough-noborder"
        style={{
          margin: "0px",
          backgroundPosition: "0px 0px",
          padding: "6px 6px 0px",
          borderWidth: "1px 1px 2px",
          borderStyle: "solid",
          borderColor:
            "rgb(149, 139, 139) rgb(149, 139, 139) rgb(116, 115, 115)",
          borderImage: "initial",
          maxWidth: "100%",
          overflowX: "hidden",
        }}
      >
        <table
          id="j_id0:headerBar"
          className="headerBar"
          style={{
            backgroundColor: "rgb(106, 120, 152)",
            height: "24px",
            borderTopLeftRadius: "5px",
            borderTopRightRadius: "5px",
          }}
        >
          <tbody>
            <tr>
              <td
                className="layoutName"
                style={{
                  fontFamily: "Arial, Helvetica, sans-serif",
                  color: "rgb(34, 34, 34)",
                  margin: "0px",
                  padding: "0px",
                  width: "30%",
                  paddingLeft: "2px",
                }}
              >
                <span id="j_id0:j_id43">
                  <div
                    id="layoutName"
                    style={{ margin: "0px", padding: "0px" }}
                  >
                    <table
                      id="otherLayoutsBtn"
                      className="x-btn x-btn-noicon"
                      cellSpacing="0"
                      style={{
                        whiteSpace: "nowrap",
                        backgroundPosition: "left top",
                        fontFamily: "Verdana, Geneva, sans-serif",
                        lineHeight: "12px",
                        cursor: "pointer",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderColor:
                          "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                        borderImage: "initial",
                        borderRadius: "3px",
                        color: "rgb(51, 51, 51)",
                        fontWeight: "bold",
                        fontSize: "0.9em",
                        background: "none",
                        border: "0px",
                        backgroundImage: "none",
                        backgroundRepeat: "initial",
                        width: "auto",
                      }}
                    >
                      <tbody className="x-btn-small x-btn-icon-small-left">
                        <tr>
                          <td
                            className="x-btn-tl"
                            style={{
                              fontFamily: "Arial, Helvetica, sans-serif",
                              color: "rgb(34, 34, 34)",
                              margin: "0px",
                              width: "3px",
                              height: "3px",
                              display: "none",
                              background: "none",
                              border: "0px",
                              backgroundImage: "none",
                              padding: "0px",
                            }}
                          >
                            <i
                              style={{
                                overflow: "hidden",
                                fontSize: "1px",
                                lineHeight: "1px",
                                width: "3px",
                                display: "block",
                                height: "3px",
                              }}
                            ></i>
                          </td>
                          <td
                            className="x-btn-tc"
                            style={{
                              fontFamily: "Arial, Helvetica, sans-serif",
                              color: "rgb(34, 34, 34)",
                              margin: "0px",
                              height: "3px",
                              display: "none",
                              background: "none",
                              border: "0px",
                              backgroundImage: "none",
                              padding: "0px",
                            }}
                          />
                          <td
                            className="x-btn-tr"
                            style={{
                              fontFamily: "Arial, Helvetica, sans-serif",
                              color: "rgb(34, 34, 34)",
                              margin: "0px",
                              width: "3px",
                              height: "3px",
                              display: "none",
                              background: "none",
                              border: "0px",
                              backgroundImage: "none",
                              padding: "0px",
                            }}
                          >
                            <i
                              style={{
                                overflow: "hidden",
                                fontSize: "1px",
                                lineHeight: "1px",
                                width: "3px",
                                display: "block",
                                height: "3px",
                              }}
                            ></i>
                          </td>
                        </tr>
                        <tr>
                          <td
                            className="x-btn-ml"
                            style={{
                              fontFamily: "Arial, Helvetica, sans-serif",
                              color: "rgb(34, 34, 34)",
                              margin: "0px",
                              width: "3px",
                              display: "none",
                              background: "none",
                              border: "0px",
                              backgroundImage: "none",
                              padding: "0px",
                            }}
                          >
                            <i
                              style={{
                                overflow: "hidden",
                                fontSize: "1px",
                                lineHeight: "1px",
                                width: "3px",
                                display: "block",
                              }}
                            ></i>
                          </td>
                          <td
                            className="x-btn-mc"
                            style={{
                              fontFamily: "Arial, Helvetica, sans-serif",
                              color: "rgb(34, 34, 34)",
                              margin: "0px",
                              whiteSpace: "nowrap",
                              verticalAlign: "middle",
                              textAlign: "center",
                              cursor: "pointer",
                              display: "table-cell",
                              background: "none",
                              border: "0px",
                              backgroundImage: "none",
                              padding: "0px",
                            }}
                          >
                            <em
                              className="x-btn-arrow"
                              unselectable="on"
                              style={{
                                fontStyle: "normal",
                                fontWeight: "normal",
                                background:
                                  "right center no-repeat transparent",
                                backgroundImage:
                                  'url("/assets/downloaded/header_menuArrows.gif")',
                                display: "block",
                                backgroundRepeat: "no-repeat",
                                backgroundPosition: "right 1px",
                                paddingRight: "18px",
                              }}
                            >
                              <button
                                id="ext-gen20"
                                className="x-btn-text"
                                type="button"
                                style={{
                                  borderWidth: "1px",
                                  borderStyle: "solid",
                                  borderColor:
                                    "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                  borderImage: "initial",
                                  borderRadius: "3px",
                                  whiteSpace: "nowrap",
                                  border: "0px none",
                                  background: "transparent",
                                  margin: "0px",
                                  overflow: "visible",
                                  outline: "none 0px",
                                  cursor: "pointer",
                                  width: "auto",
                                  padding: "1px 3px",
                                  paddingLeft: "0px",
                                  paddingRight: "0px",
                                  color: "rgb(255, 255, 255)",
                                  fontSize: "1.2em",
                                  fontWeight: "bold",
                                  fontFamily: "Arial, Helvetica, sans-serif",
                                  marginLeft: "4px",
                                  lineHeight: "1.2em",
                                  height: "auto",
                                }}
                              >
                                {(() => {
                                  const windowData =
                                    window.allVariableData?.objectManager
                                      ?.customObjectData;
                                  const contextData =
                                    allVariableData?.objectManager
                                      ?.customObjectData;

                                  const customObjectData =
                                    windowData || contextData;

                                  if (
                                    customObjectData &&
                                    Array.isArray(customObjectData) &&
                                    customObjectData.length > 0
                                  ) {
                                    return `${
                                      customObjectData.slice(-1)[0].MasterLabel
                                    } Layout`;
                                  }

                                  return "Opportunity (Sales) Layout";
                                })()}
                              </button>
                            </em>
                          </td>
                          <td
                            className="x-btn-mr"
                            style={{
                              fontFamily: "Arial, Helvetica, sans-serif",
                              color: "rgb(34, 34, 34)",
                              margin: "0px",
                              width: "3px",
                              display: "none",
                              background: "none",
                              border: "0px",
                              backgroundImage: "none",
                              padding: "0px",
                            }}
                          >
                            <i
                              style={{
                                overflow: "hidden",
                                fontSize: "1px",
                                lineHeight: "1px",
                                width: "3px",
                                display: "block",
                              }}
                            ></i>
                          </td>
                        </tr>
                        <tr>
                          <td
                            className="x-btn-bl"
                            style={{
                              fontFamily: "Arial, Helvetica, sans-serif",
                              color: "rgb(34, 34, 34)",
                              margin: "0px",
                              width: "3px",
                              height: "3px",
                              display: "none",
                              background: "none",
                              border: "0px",
                              backgroundImage: "none",
                              padding: "0px",
                            }}
                          >
                            <i
                              style={{
                                overflow: "hidden",
                                fontSize: "1px",
                                lineHeight: "1px",
                                width: "3px",
                                display: "block",
                                height: "3px",
                              }}
                            ></i>
                          </td>
                          <td
                            className="x-btn-bc"
                            style={{
                              fontFamily: "Arial, Helvetica, sans-serif",
                              color: "rgb(34, 34, 34)",
                              margin: "0px",
                              height: "3px",
                              display: "none",
                              background: "none",
                              border: "0px",
                              backgroundImage: "none",
                              padding: "0px",
                            }}
                          />
                          <td
                            className="x-btn-br"
                            style={{
                              fontFamily: "Arial, Helvetica, sans-serif",
                              color: "rgb(34, 34, 34)",
                              margin: "0px",
                              width: "3px",
                              height: "3px",
                              display: "none",
                              background: "none",
                              border: "0px",
                              backgroundImage: "none",
                              padding: "0px",
                            }}
                          >
                            <i
                              style={{
                                overflow: "hidden",
                                fontSize: "1px",
                                lineHeight: "1px",
                                width: "3px",
                                display: "block",
                                height: "3px",
                              }}
                            ></i>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </span>
              </td>
              <td
                className="headerButtons"
                style={{
                  fontFamily: "Arial, Helvetica, sans-serif",
                  color: "rgb(34, 34, 34)",
                  margin: "0px",
                  padding: "0px",
                  paddingLeft: "5px",
                }}
              >
                <span id="j_id0:j_id45">
                  <div
                    id="headerButtons"
                    style={{ margin: "0px", padding: "0px" }}
                  />
                </span>
              </td>
              <td
                className="headerLinks"
                style={{
                  fontFamily: "Arial, Helvetica, sans-serif",
                  color: "rgb(34, 34, 34)",
                  margin: "0px",
                  padding: "0px",
                  width: "60%",
                  fontSize: "95%",
                  textAlign: "right",
                  verticalAlign: "middle",
                }}
              >
                <span id="j_id0:j_id47">
                  <div
                    id="headerLinks"
                    style={{
                      margin: "0px",
                      padding: "0px",
                      textDecoration: "underline",
                      color: "white",
                    }}
                  >
                    <a
                      href="#"
                      style={{
                        whiteSpace: "nowrap",
                        marginLeft: "8px",
                        color: "rgb(255, 255, 255)",
                      }}
                    >
                      Mini Page Layout
                    </a>
                    <a
                      href="#"
                      style={{
                        whiteSpace: "nowrap",
                        marginLeft: "8px",
                        color: "rgb(255, 255, 255)",
                      }}
                    >
                      Mini Console View
                    </a>
                    <span
                      id="j_id0:j_id61"
                      className="seperator"
                      style={{ color: "rgb(255, 255, 255)", marginLeft: "8px" }}
                    >
                      |
                    </span>
                    <a
                      href="#"
                      style={{
                        whiteSpace: "nowrap",
                        marginLeft: "8px",
                        color: "rgb(255, 255, 255)",
                      }}
                    >
                      Video Tutorial
                    </a>
                    <a
                      className="helpLink"
                      href="/#"
                      title="Help (New Window)"
                      style={{
                        whiteSpace: "nowrap",
                        marginLeft: "8px",
                        color: "rgb(255, 255, 255)",
                        textDecoration: "none",
                      }}
                    >
                      <span
                        className="text"
                        style={{
                          textDecoration: "underline",
                          marginRight: "0.8em",
                        }}
                      >
                        Help for this Page
                      </span>
                      <img
                        className="helpIcon"
                        src="/assets/downloaded/s.gif"
                        title="Help"
                        style={{
                          border: "0px",
                          backgroundPosition: "0px -142px",
                          width: "16px",
                          height: "16px",
                          background:
                            'url("/assets/downloaded/help_orange.png") no-repeat transparent',
                          backgroundImage:
                            'url("/assets/downloaded/help_orange.png")',
                          verticalAlign: "middle",
                        }}
                      />
                    </a>
                  </div>
                </span>
              </td>
              <td
                className="nonce"
                style={{
                  fontFamily: "Arial, Helvetica, sans-serif",
                  color: "rgb(34, 34, 34)",
                  margin: "0px",
                  padding: "0px",
                }}
              >
                <form
                  id="j_id0:f"
                  name="j_id0:f"
                  action=""
                  encType="application/x-www-form-urlencoded"
                  method="post"
                  style={{ margin: "0px", padding: "0px" }}
                >
                  <input
                    name="j_id0:f"
                    type="hidden"
                    defaultValue="j_id0:f"
                    style={{ margin: "0px", padding: "0px", paddingTop: "0px" }}
                  />
                  <div
                    style={{ margin: "0px", padding: "0px", display: "none" }}
                  >
                    <input
                      id="j_id0:f:quickSave"
                      className="btn"
                      name="j_id0:f:quickSave"
                      type="button"
                      defaultValue="Quick Save"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        display: "inline",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderColor:
                          "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                        borderImage: "initial",
                        background:
                          'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                        borderRadius: "3px",
                        color: "rgb(51, 51, 51)",
                        backgroundImage:
                          'url("/assets/downloaded/btn_sprite.png")',
                        backgroundRepeat: "repeat-x",
                        fontWeight: "bold",
                        fontSize: "0.9em",
                        padding: "4px 3px",
                        paddingTop: "4px",
                      }}
                    />
                    <input
                      id="j_id0:f:save"
                      className="btn"
                      name="j_id0:f:save"
                      type="button"
                      defaultValue="Save"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        display: "inline",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderColor:
                          "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                        borderImage: "initial",
                        background:
                          'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                        borderRadius: "3px",
                        color: "rgb(51, 51, 51)",
                        backgroundImage:
                          'url("/assets/downloaded/btn_sprite.png")',
                        backgroundRepeat: "repeat-x",
                        fontWeight: "bold",
                        fontSize: "0.9em",
                        padding: "4px 3px",
                        paddingTop: "4px",
                      }}
                    />
                    <input
                      id="j_id0:f:saveAs"
                      className="btn"
                      name="j_id0:f:saveAs"
                      type="button"
                      defaultValue="Save As"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        display: "inline",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderColor:
                          "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                        borderImage: "initial",
                        background:
                          'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                        borderRadius: "3px",
                        color: "rgb(51, 51, 51)",
                        backgroundImage:
                          'url("/assets/downloaded/btn_sprite.png")',
                        backgroundRepeat: "repeat-x",
                        fontWeight: "bold",
                        fontSize: "0.9em",
                        padding: "4px 3px",
                        paddingTop: "4px",
                      }}
                    />
                    <input
                      id="j_id0:f:cancel"
                      className="btn"
                      name="j_id0:f:cancel"
                      type="submit"
                      defaultValue="Cancel"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        display: "inline",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderColor:
                          "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                        borderImage: "initial",
                        background:
                          'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                        borderRadius: "3px",
                        color: "rgb(51, 51, 51)",
                        backgroundImage:
                          'url("/assets/downloaded/btn_sprite.png")',
                        backgroundRepeat: "repeat-x",
                        fontWeight: "bold",
                        fontSize: "0.9em",
                        padding: "4px 3px",
                        paddingTop: "4px",
                      }}
                    />
                    <input
                      id="j_id0:f:jsonLayout"
                      name="j_id0:f:jsonLayout"
                      type="hidden"
                      style={{
                        margin: "0px",
                        padding: "0px",
                        paddingTop: "0px",
                      }}
                    />
                    <input
                      id="j_id0:f:clearUserLists"
                      name="j_id0:f:clearUserLists"
                      type="hidden"
                      defaultValue="false"
                      style={{
                        margin: "0px",
                        padding: "0px",
                        paddingTop: "0px",
                      }}
                    />
                  </div>
                  <div
                    id="j_id0:f:j_id89"
                    style={{ margin: "0px", padding: "0px" }}
                  />
                </form>
                <span
                  id="ajax-view-state-page-container"
                  style={{ display: "none" }}
                >
                  <span id="ajax-view-state" style={{ display: "none" }}>
                    <input
                      id="com.salesforce.visualforce.ViewState"
                      name="com.salesforce.visualforce.ViewState"
                      type="hidden"
                      defaultValue="i: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"
                      style={{
                        margin: "0px",
                        padding: "0px",
                        paddingTop: "0px",
                      }}
                    />
                    <input
                      id="com.salesforce.visualforce.ViewStateVersion"
                      name="com.salesforce.visualforce.ViewStateVersion"
                      type="hidden"
                      defaultValue="202504101726344882"
                      style={{
                        margin: "0px",
                        padding: "0px",
                        paddingTop: "0px",
                      }}
                    />
                    <input
                      id="com.salesforce.visualforce.ViewStateMAC"
                      name="com.salesforce.visualforce.ViewStateMAC"
                      type="hidden"
                      defaultValue="AWV5SnViMjVqWlNJNkluTllSMUI1YjBzeGJYZEhkMnAwZVhoSmRqUnhkVFZCV0ZRdFNXRlNVVmRFYjFwaE4wZGtSblpYVEZWY2RUQXdNMlFpTENKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUlzSW10cFpDSTZJbnRjSW5SY0lqcGNJakF3UkdkTU1EQXdNREF3UkhkRlRsd2lMRndpZGx3aU9sd2lNREpIWjB3d01EQXdNREF6TlRaUlhDSXNYQ0poWENJNlhDSjJabk5wWjI1cGJtZHJaWGxjSWl4Y0luVmNJanBjSWpBd05XZE1NREF3TURBd1QweEZZMXdpZlNJc0ltTnlhWFFpT2xzaWFXRjBJbDBzSW1saGRDSTZNVGMwTkRrMU1Ea3dOalV3TVN3aVpYaHdJam93ZlE9PS4ubHc5dWJkOTV2Q0FpRFphYUJmaUFVVzJvWDdwLVFyNmpOUk1Edl9odW9Jdz0="
                      style={{
                        margin: "0px",
                        padding: "0px",
                        paddingTop: "0px",
                      }}
                    />
                    <input
                      id="com.salesforce.visualforce.ViewStateCSRF"
                      name="com.salesforce.visualforce.ViewStateCSRF"
                      type="hidden"
                      defaultValue="VmpFPSxNakF5TlMwd05DMHlNVlF3TkRvek5Ub3dOaTQxTURGYSxPdkNYUmJZZ19uQXVWRlVTZXRvYlZKME8wMFJKdU0wVzhDbDJSdzNBNDNJPSxOelkzTjJFMA=="
                      style={{
                        margin: "0px",
                        padding: "0px",
                        paddingTop: "0px",
                      }}
                    />
                  </span>
                </span>
              </td>
            </tr>
          </tbody>
        </table>

        <div
          id="ext-gen32"
          className="trough-bwrap"
          style={{ margin: "0px", padding: "0px" }}
        >
          <div
            id="ext-gen33"
            className="trough-tbar trough-tbar-noheader trough-tbar-noborder"
            style={{ margin: "0px", padding: "0px", width: "1485px" }}
          >
            <div
              id="ext-comp-1084"
              className="x-toolbar x-small-editor subbtn x-toolbar-layout-ct"
              style={{
                margin: "0px",
                borderStyle: "solid",
                borderWidth: "0px 0px 1px",
                overflow: "hidden",
                display: "block",
                position: "relative",
                left: "0px",
                top: "0px",
                zoom: 1,
                borderColor: "rgb(208, 208, 208)",
                background: "none",
                border: "none",
                padding: "0px 0px 2px",
                backgroundImage: "none",
                backgroundColor: "#c1c3c5",
                width: "1485px",
              }}
            >
              <table
                className="x-toolbar-ct"
                cellSpacing="0"
                style={{ width: "20%" }}
              >
                <tbody>
                  <tr>
                    <td
                      className="x-toolbar-left"
                      style={{
                        color: "rgb(34, 34, 34)",
                        margin: "0px",
                        padding: "0px",
                        width: "100%",
                        verticalAlign: "middle",
                        whiteSpace: "nowrap",
                        font: "11px Arial, Helvetica, sans-serif",
                        fontFamily: "Arial, Helvetica, sans-serif",
                      }}
                    >
                      <table cellSpacing="0">
                        <tbody>
                          <tr className="x-toolbar-left-row">
                            <td
                              id="ext-gen59"
                              className="x-toolbar-cell"
                              style={{
                                color: "rgb(34, 34, 34)",
                                margin: "0px",
                                padding: "0px",
                                whiteSpace: "nowrap",
                                font: "11px Arial, Helvetica, sans-serif",
                                fontFamily: "Arial, Helvetica, sans-serif",
                                verticalAlign: "middle",
                              }}
                            >
                              <table
                                id="saveBtn"
                                className="x-btn x-btn-noicon"
                                cellSpacing="0"
                                style={{
                                  whiteSpace: "nowrap",
                                  backgroundPosition: "left top",
                                  fontFamily: "Verdana, Geneva, sans-serif",
                                  lineHeight: "12px",
                                  cursor: "pointer",
                                  padding: "2px 3px",
                                  fontWeight: "bold",
                                  fontSize: "0.9em",
                                  borderWidth: "1px",
                                  borderStyle: "solid",
                                  borderColor:
                                    "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                  borderImage: "initial",
                                  background:
                                    'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                  borderRadius: "3px",
                                  margin: "0px 1px",
                                  color: "rgb(51, 51, 51)",
                                  backgroundImage:
                                    'url("/assets/downloaded/btn_sprite.png")',
                                  backgroundRepeat: "repeat-x",
                                  width: "auto",
                                }}
                              >
                                <tbody className="x-btn-small x-btn-icon-small-left">
                                  <tr>
                                    <td
                                      className="x-btn-tl"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-tc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -6px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-tr"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      className="x-btn-ml"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-mc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -1096px repeat-x",
                                        textAlign: "center",
                                        cursor: "pointer",
                                        backgroundImage: "none",
                                        display: "table-cell",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <em
                                        className="x-btn-split"
                                        unselectable="on"
                                        style={{
                                          fontStyle: "normal",
                                          fontWeight: "normal",
                                          background:
                                            "right center no-repeat transparent",
                                          display: "block",
                                          paddingRight: "14px",
                                          backgroundImage:
                                            'url("/assets/downloaded/split_mutton_arrow.png")',
                                        }}
                                      >
                                        <button
                                          id="ext-gen60"
                                          className="x-btn-text"
                                          type="button"
                                          onClick={handleSave}
                                          style={{
                                            borderWidth: "1px",
                                            borderStyle: "solid",
                                            borderColor:
                                              "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                            borderImage: "initial",
                                            borderRadius: "3px",
                                            whiteSpace: "nowrap",
                                            border: "0px none",
                                            background: "transparent",
                                            margin: "0px",
                                            overflow: "visible",
                                            outline: "none 0px",
                                            cursor: "pointer",
                                            width: "auto",
                                            padding: "1px 3px",
                                            fontWeight: "bold",
                                            lineHeight: "12px",
                                            fontFamily:
                                              "Arial, Helvetica, sans-serif",
                                            fontSize: "1em",
                                            paddingLeft: "2px",
                                            paddingRight: "2px",
                                            color: "rgb(51, 51, 51)",
                                            height: "auto",
                                          }}
                                        >
                                          Save
                                        </button>
                                      </em>
                                    </td>
                                    <td
                                      className="x-btn-mr"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      className="x-btn-bl"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-bc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -15px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-br"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                            <td
                              id="ext-gen61"
                              className="x-toolbar-cell"
                              style={{
                                color: "rgb(34, 34, 34)",
                                margin: "0px",
                                padding: "0px",
                                whiteSpace: "nowrap",
                                font: "11px Arial, Helvetica, sans-serif",
                                fontFamily: "Arial, Helvetica, sans-serif",
                                verticalAlign: "middle",
                              }}
                            >
                              <table
                                id="quickSaveBtn"
                                className="x-btn x-btn-noicon"
                                cellSpacing="0"
                                style={{
                                  whiteSpace: "nowrap",
                                  backgroundPosition: "left top",
                                  fontFamily: "Verdana, Geneva, sans-serif",
                                  lineHeight: "12px",
                                  cursor: "pointer",
                                  padding: "2px 3px",
                                  fontWeight: "bold",
                                  fontSize: "0.9em",
                                  borderWidth: "1px",
                                  borderStyle: "solid",
                                  borderColor:
                                    "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                  borderImage: "initial",
                                  background:
                                    'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                  borderRadius: "3px",
                                  margin: "0px 1px",
                                  color: "rgb(51, 51, 51)",
                                  backgroundImage:
                                    'url("/assets/downloaded/btn_sprite.png")',
                                  backgroundRepeat: "repeat-x",
                                  width: "auto",
                                }}
                              >
                                <tbody className="x-btn-small x-btn-icon-small-left">
                                  <tr>
                                    <td
                                      className="x-btn-tl"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-tc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -6px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-tr"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      className="x-btn-ml"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-mc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -1096px repeat-x",
                                        textAlign: "center",
                                        cursor: "pointer",
                                        backgroundImage: "none",
                                        display: "table-cell",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <em
                                        unselectable="on"
                                        style={{
                                          fontStyle: "normal",
                                          fontWeight: "normal",
                                        }}
                                      >
                                        <button
                                          id="ext-gen62"
                                          className="x-btn-text"
                                          type="button"
                                          style={{
                                            borderWidth: "1px",
                                            borderStyle: "solid",
                                            borderColor:
                                              "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                            borderImage: "initial",
                                            borderRadius: "3px",
                                            whiteSpace: "nowrap",
                                            border: "0px none",
                                            background: "transparent",
                                            margin: "0px",
                                            overflow: "visible",
                                            outline: "none 0px",
                                            cursor: "pointer",
                                            width: "auto",
                                            padding: "3px 3px",
                                            fontWeight: "bold",
                                            lineHeight: "12px",
                                            fontFamily:
                                              "Arial, Helvetica, sans-serif",
                                            fontSize: "1em",
                                            paddingLeft: "0px",
                                            paddingRight: "0px",
                                            color: "rgb(51, 51, 51)",
                                            height: "auto",
                                          }}
                                        >
                                          Quick Save
                                        </button>
                                      </em>
                                    </td>
                                    <td
                                      className="x-btn-mr"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      className="x-btn-bl"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-bc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -15px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-br"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                            <td
                              id="ext-gen63"
                              className="x-toolbar-cell"
                              style={{
                                color: "rgb(34, 34, 34)",
                                margin: "0px",
                                padding: "0px",
                                whiteSpace: "nowrap",
                                font: "11px Arial, Helvetica, sans-serif",
                                fontFamily: "Arial, Helvetica, sans-serif",
                                verticalAlign: "middle",
                              }}
                            >
                              <table
                                id="ext-comp-1043"
                                className="x-btn x-btn-noicon"
                                cellSpacing="0"
                                style={{
                                  whiteSpace: "nowrap",
                                  backgroundPosition: "left top",
                                  fontFamily: "Verdana, Geneva, sans-serif",
                                  lineHeight: "12px",
                                  cursor: "pointer",
                                  padding: "2px 3px",
                                  fontWeight: "bold",
                                  fontSize: "0.9em",
                                  borderWidth: "1px",
                                  borderStyle: "solid",
                                  borderColor:
                                    "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                  borderImage: "initial",
                                  background:
                                    'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                  borderRadius: "3px",
                                  margin: "0px 1px",
                                  color: "rgb(51, 51, 51)",
                                  backgroundImage:
                                    'url("/assets/downloaded/btn_sprite.png")',
                                  backgroundRepeat: "repeat-x",
                                  width: "auto",
                                }}
                              >
                                <tbody className="x-btn-small x-btn-icon-small-left">
                                  <tr>
                                    <td
                                      className="x-btn-tl"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-tc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -6px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-tr"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      className="x-btn-ml"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-mc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -1096px repeat-x",
                                        textAlign: "center",
                                        cursor: "pointer",
                                        backgroundImage: "none",
                                        display: "table-cell",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <em
                                        className="x-btn-arrow"
                                        unselectable="on"
                                        style={{
                                          fontStyle: "normal",
                                          fontWeight: "normal",
                                          background:
                                            "right center no-repeat transparent",
                                          display: "block",
                                          paddingRight: "10px",
                                          backgroundImage:
                                            'url("/assets/downloaded/split_mutton_arrow.png")',
                                        }}
                                      >
                                        <button
                                          id="ext-gen64"
                                          className="x-btn-text"
                                          type="button"
                                          style={{
                                            borderWidth: "1px",
                                            borderStyle: "solid",
                                            borderColor:
                                              "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                            borderImage: "initial",
                                            borderRadius: "3px",
                                            whiteSpace: "nowrap",
                                            border: "0px none",
                                            background: "transparent",
                                            margin: "0px",
                                            overflow: "visible",
                                            outline: "none 0px",
                                            cursor: "pointer",
                                            width: "auto",
                                            padding: "3px 3px",
                                            fontWeight: "bold",
                                            lineHeight: "12px",
                                            fontFamily:
                                              "Arial, Helvetica, sans-serif",
                                            fontSize: "1em",
                                            paddingLeft: "0px",
                                            paddingRight: "0px",
                                            color: "rgb(51, 51, 51)",
                                            height: "auto",
                                          }}
                                        >
                                          Preview As...
                                        </button>
                                      </em>
                                    </td>
                                    <td
                                      className="x-btn-mr"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      className="x-btn-bl"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-bc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -15px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-br"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                            <td
                              id="ext-gen65"
                              className="x-toolbar-cell"
                              style={{
                                color: "rgb(34, 34, 34)",
                                margin: "0px",
                                padding: "0px",
                                whiteSpace: "nowrap",
                                font: "11px Arial, Helvetica, sans-serif",
                                fontFamily: "Arial, Helvetica, sans-serif",
                                verticalAlign: "middle",
                              }}
                            >
                              <table
                                id="cancelBtn"
                                className="x-btn x-btn-noicon"
                                cellSpacing="0"
                                style={{
                                  whiteSpace: "nowrap",
                                  backgroundPosition: "left top",
                                  fontFamily: "Verdana, Geneva, sans-serif",
                                  lineHeight: "12px",
                                  cursor: "pointer",
                                  padding: "2px 3px",
                                  fontWeight: "bold",
                                  fontSize: "0.9em",
                                  borderWidth: "1px",
                                  borderStyle: "solid",
                                  borderColor:
                                    "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                  borderImage: "initial",
                                  background:
                                    'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                  borderRadius: "3px",
                                  margin: "0px 1px",
                                  color: "rgb(51, 51, 51)",
                                  backgroundImage:
                                    'url("/assets/downloaded/btn_sprite.png")',
                                  backgroundRepeat: "repeat-x",
                                  width: "auto",
                                }}
                              >
                                <tbody className="x-btn-small x-btn-icon-small-left">
                                  <tr>
                                    <td
                                      className="x-btn-tl"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-tc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -6px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-tr"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      className="x-btn-ml"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-mc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -1096px repeat-x",
                                        textAlign: "center",
                                        cursor: "pointer",
                                        backgroundImage: "none",
                                        display: "table-cell",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <em
                                        unselectable="on"
                                        style={{
                                          fontStyle: "normal",
                                          fontWeight: "normal",
                                        }}
                                      >
                                        <button
                                          id="ext-gen66"
                                          className="x-btn-text"
                                          type="button"
                                          style={{
                                            borderWidth: "1px",
                                            borderStyle: "solid",
                                            borderColor:
                                              "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                            borderImage: "initial",
                                            borderRadius: "3px",
                                            whiteSpace: "nowrap",
                                            border: "0px none",
                                            background: "transparent",
                                            margin: "0px",
                                            overflow: "visible",
                                            outline: "none 0px",
                                            cursor: "pointer",
                                            width: "auto",
                                            padding: "3px 3px",
                                            fontWeight: "bold",
                                            lineHeight: "12px",
                                            fontFamily:
                                              "Arial, Helvetica, sans-serif",
                                            fontSize: "1em",
                                            paddingLeft: "0px",
                                            paddingRight: "0px",
                                            color: "rgb(51, 51, 51)",
                                            height: "auto",
                                          }}
                                        >
                                          Cancel
                                        </button>
                                      </em>
                                    </td>
                                    <td
                                      className="x-btn-mr"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      className="x-btn-bl"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-bc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -15px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-br"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                            <td
                              id="ext-gen67"
                              className="x-toolbar-cell"
                              style={{
                                color: "rgb(34, 34, 34)",
                                margin: "0px",
                                padding: "0px",
                                whiteSpace: "nowrap",
                                font: "11px Arial, Helvetica, sans-serif",
                                fontFamily: "Arial, Helvetica, sans-serif",
                                verticalAlign: "middle",
                              }}
                            >
                              <span
                                id="ext-gen68"
                                className="xtb-sep"
                                style={{
                                  whiteSpace: "nowrap",
                                  font: "11px Arial, Helvetica, sans-serif",
                                  backgroundPosition: "center center",
                                  overflow: "hidden",
                                  margin: "0px 2px",
                                  border: "0px",
                                  backgroundRepeat: "no-repeat",
                                  display: "block",
                                  fontSize: "1px",
                                  height: "16px",
                                  width: "4px",
                                  cursor: "default",
                                  backgroundImage:
                                    'url("/assets/downloaded/grid-blue-split.gif")',
                                }}
                              />
                            </td>
                            <td
                              id="ext-gen69"
                              className="x-toolbar-cell"
                              style={{
                                color: "rgb(34, 34, 34)",
                                margin: "0px",
                                padding: "0px",
                                whiteSpace: "nowrap",
                                font: "11px Arial, Helvetica, sans-serif",
                                fontFamily: "Arial, Helvetica, sans-serif",
                                verticalAlign: "middle",
                              }}
                            >
                              <table
                                id="undoBtn"
                                className="x-btn x-btn-text-icon x-item-disabled"
                                cellSpacing="0"
                                style={{
                                  whiteSpace: "nowrap",
                                  backgroundPosition: "left top",
                                  fontFamily: "Verdana, Geneva, sans-serif",
                                  lineHeight: "12px",
                                  padding: "2px 3px",
                                  fontWeight: "bold",
                                  fontSize: "0.9em",
                                  cursor: "default",
                                  opacity: 0.6,
                                  borderWidth: "1px",
                                  borderStyle: "solid",
                                  borderColor:
                                    "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                  borderImage: "initial",
                                  background:
                                    'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                  borderRadius: "3px",
                                  margin: "0px 1px",
                                  color: "rgb(51, 51, 51)",
                                  backgroundImage:
                                    'url("/assets/downloaded/btn_sprite.png")',
                                  backgroundRepeat: "repeat-x",
                                  width: "auto",
                                }}
                              >
                                <tbody
                                  className="x-btn-small x-btn-icon-small-left"
                                  style={{ cursor: "default", color: "gray" }}
                                >
                                  <tr
                                    style={{ cursor: "default", color: "gray" }}
                                  >
                                    <td
                                      className="x-btn-tl"
                                      style={{
                                        margin: "0px",
                                        background: "0px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-tc"
                                      style={{
                                        margin: "0px",
                                        background: "0px -6px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-tr"
                                      style={{
                                        margin: "0px",
                                        background: "-3px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr
                                    style={{ cursor: "default", color: "gray" }}
                                  >
                                    <td
                                      className="x-btn-ml"
                                      style={{
                                        margin: "0px",
                                        background: "0px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-mc"
                                      style={{
                                        margin: "0px",
                                        background: "0px -1096px repeat-x",
                                        textAlign: "center",
                                        backgroundImage: "none",
                                        display: "table-cell",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <em
                                        unselectable="on"
                                        style={{
                                          fontStyle: "normal",
                                          fontWeight: "normal",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      >
                                        <button
                                          id="ext-gen70"
                                          className="x-btn-text undo-btn-icon"
                                          type="button"
                                          style={{
                                            borderWidth: "1px",
                                            borderStyle: "solid",
                                            borderColor:
                                              "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                            borderImage: "initial",
                                            borderRadius: "3px",
                                            whiteSpace: "nowrap",
                                            border: "0px none",
                                            background: "transparent",
                                            margin: "0px",
                                            overflow: "visible",
                                            outline: "none 0px",
                                            padding: "1px 3px",
                                            fontWeight: "bold",
                                            lineHeight: "12px",
                                            fontFamily:
                                              "Arial, Helvetica, sans-serif",
                                            fontSize: "1em",
                                            paddingRight: "0px",
                                            paddingLeft: "18px",
                                            backgroundImage:
                                              'url("/assets/downloaded/sprite.png")',
                                            backgroundColor: "transparent",
                                            backgroundRepeat: "no-repeat",
                                            height: "auto",
                                            width: "auto",
                                            backgroundPosition: "0px -48px",
                                            cursor: "default",
                                            color: "gray",
                                          }}
                                        >
                                          Undo
                                        </button>
                                      </em>
                                    </td>
                                    <td
                                      className="x-btn-mr"
                                      style={{
                                        margin: "0px",
                                        background: "-3px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr
                                    style={{ cursor: "default", color: "gray" }}
                                  >
                                    <td
                                      className="x-btn-bl"
                                      style={{
                                        margin: "0px",
                                        background: "0px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-bc"
                                      style={{
                                        margin: "0px",
                                        background: "0px -15px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-br"
                                      style={{
                                        margin: "0px",
                                        background: "-3px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                            <td
                              id="ext-gen71"
                              className="x-toolbar-cell"
                              style={{
                                color: "rgb(34, 34, 34)",
                                margin: "0px",
                                padding: "0px",
                                whiteSpace: "nowrap",
                                font: "11px Arial, Helvetica, sans-serif",
                                fontFamily: "Arial, Helvetica, sans-serif",
                                verticalAlign: "middle",
                              }}
                            >
                              <table
                                id="redoBtn"
                                className="x-btn x-btn-text-icon x-item-disabled"
                                cellSpacing="0"
                                style={{
                                  whiteSpace: "nowrap",
                                  backgroundPosition: "left top",
                                  fontFamily: "Verdana, Geneva, sans-serif",
                                  lineHeight: "12px",
                                  padding: "2px 3px",
                                  fontWeight: "bold",
                                  fontSize: "0.9em",
                                  cursor: "default",
                                  opacity: 0.6,
                                  borderWidth: "1px",
                                  borderStyle: "solid",
                                  borderColor:
                                    "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                  borderImage: "initial",
                                  background:
                                    'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                  borderRadius: "3px",
                                  margin: "0px 1px",
                                  color: "rgb(51, 51, 51)",
                                  backgroundImage:
                                    'url("/assets/downloaded/btn_sprite.png")',
                                  backgroundRepeat: "repeat-x",
                                  width: "auto",
                                }}
                              >
                                <tbody
                                  className="x-btn-small x-btn-icon-small-left"
                                  style={{ cursor: "default", color: "gray" }}
                                >
                                  <tr
                                    style={{ cursor: "default", color: "gray" }}
                                  >
                                    <td
                                      className="x-btn-tl"
                                      style={{
                                        margin: "0px",
                                        background: "0px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-tc"
                                      style={{
                                        margin: "0px",
                                        background: "0px -6px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-tr"
                                      style={{
                                        margin: "0px",
                                        background: "-3px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr
                                    style={{ cursor: "default", color: "gray" }}
                                  >
                                    <td
                                      className="x-btn-ml"
                                      style={{
                                        margin: "0px",
                                        background: "0px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-mc"
                                      style={{
                                        margin: "0px",
                                        background: "0px -1096px repeat-x",
                                        textAlign: "center",
                                        backgroundImage: "none",
                                        display: "table-cell",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <em
                                        unselectable="on"
                                        style={{
                                          fontStyle: "normal",
                                          fontWeight: "normal",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      >
                                        <button
                                          id="ext-gen72"
                                          className="x-btn-text redo-btn-icon"
                                          type="button"
                                          style={{
                                            borderWidth: "1px",
                                            borderStyle: "solid",
                                            borderColor:
                                              "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                            borderImage: "initial",
                                            borderRadius: "3px",
                                            whiteSpace: "nowrap",
                                            border: "0px none",
                                            background: "transparent",
                                            margin: "0px",
                                            overflow: "visible",
                                            outline: "none 0px",
                                            padding: "1px 3px",
                                            fontWeight: "bold",
                                            lineHeight: "12px",
                                            fontFamily:
                                              "Arial, Helvetica, sans-serif",
                                            fontSize: "1em",
                                            paddingRight: "0px",
                                            paddingLeft: "18px",
                                            backgroundImage:
                                              'url("/assets/downloaded/sprite.png")',
                                            backgroundColor: "transparent",
                                            backgroundRepeat: "no-repeat",
                                            height: "auto",
                                            width: "auto",
                                            backgroundPosition: "0px -80px",
                                            cursor: "default",
                                            color: "gray",
                                          }}
                                        >
                                          Redo
                                        </button>
                                      </em>
                                    </td>
                                    <td
                                      className="x-btn-mr"
                                      style={{
                                        margin: "0px",
                                        background: "-3px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr
                                    style={{ cursor: "default", color: "gray" }}
                                  >
                                    <td
                                      className="x-btn-bl"
                                      style={{
                                        margin: "0px",
                                        background: "0px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-bc"
                                      style={{
                                        margin: "0px",
                                        background: "0px -15px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-br"
                                      style={{
                                        margin: "0px",
                                        background: "-3px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        cursor: "default",
                                        color: "gray",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                          cursor: "default",
                                          color: "gray",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                            <td
                              id="ext-gen73"
                              className="x-toolbar-cell"
                              style={{
                                color: "rgb(34, 34, 34)",
                                margin: "0px",
                                padding: "0px",
                                whiteSpace: "nowrap",
                                font: "11px Arial, Helvetica, sans-serif",
                                fontFamily: "Arial, Helvetica, sans-serif",
                                verticalAlign: "middle",
                              }}
                            >
                              <span
                                id="ext-gen74"
                                className="xtb-sep"
                                style={{
                                  whiteSpace: "nowrap",
                                  font: "11px Arial, Helvetica, sans-serif",
                                  backgroundPosition: "center center",
                                  overflow: "hidden",
                                  margin: "0px 2px",
                                  border: "0px",
                                  backgroundRepeat: "no-repeat",
                                  display: "block",
                                  fontSize: "1px",
                                  height: "16px",
                                  width: "4px",
                                  cursor: "default",
                                  backgroundImage:
                                    'url("/assets/downloaded/grid-blue-split.gif")',
                                }}
                              />
                            </td>
                            <td
                              id="ext-gen75"
                              className="x-toolbar-cell"
                              style={{
                                color: "rgb(34, 34, 34)",
                                margin: "0px",
                                padding: "0px",
                                whiteSpace: "nowrap",
                                font: "11px Arial, Helvetica, sans-serif",
                                fontFamily: "Arial, Helvetica, sans-serif",
                                verticalAlign: "middle",
                              }}
                            >
                              <table
                                id="pageProps"
                                className="x-btn x-btn-text-icon"
                                cellSpacing="0"
                                style={{
                                  whiteSpace: "nowrap",
                                  backgroundPosition: "left top",
                                  fontFamily: "Verdana, Geneva, sans-serif",
                                  lineHeight: "12px",
                                  cursor: "pointer",
                                  padding: "2px 3px",
                                  fontWeight: "bold",
                                  fontSize: "0.9em",
                                  borderWidth: "1px",
                                  borderStyle: "solid",
                                  borderColor:
                                    "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                  borderImage: "initial",
                                  background:
                                    'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                  borderRadius: "3px",
                                  margin: "0px 1px",
                                  color: "rgb(51, 51, 51)",
                                  backgroundImage:
                                    'url("/assets/downloaded/btn_sprite.png")',
                                  backgroundRepeat: "repeat-x",
                                  width: "auto",
                                }}
                              >
                                <tbody className="x-btn-small x-btn-icon-small-left">
                                  <tr>
                                    <td
                                      className="x-btn-tl"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-tc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -6px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-tr"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px 0px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      className="x-btn-ml"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-mc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -1096px repeat-x",
                                        textAlign: "center",
                                        cursor: "pointer",
                                        backgroundImage: "none",
                                        display: "table-cell",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <em
                                        unselectable="on"
                                        style={{
                                          fontStyle: "normal",
                                          fontWeight: "normal",
                                        }}
                                      >
                                        <button
                                          id="ext-gen76"
                                          className="x-btn-text pagePropsBtn"
                                          type="button"
                                          style={{
                                            borderWidth: "1px",
                                            borderStyle: "solid",
                                            borderColor:
                                              "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                            borderImage: "initial",
                                            borderRadius: "3px",
                                            whiteSpace: "nowrap",
                                            border: "0px none",
                                            background: "transparent",
                                            margin: "0px",
                                            overflow: "visible",
                                            outline: "none 0px",
                                            cursor: "pointer",
                                            padding: "1px 3px",
                                            fontWeight: "bold",
                                            lineHeight: "12px",
                                            fontFamily:
                                              "Arial, Helvetica, sans-serif",
                                            fontSize: "1em",
                                            paddingRight: "0px",
                                            color: "rgb(51, 51, 51)",
                                            paddingLeft: "18px",
                                            backgroundColor: "transparent",
                                            backgroundRepeat: "no-repeat",
                                            height: "auto",
                                            width: "auto",
                                            backgroundPosition: "2px 2px",
                                            backgroundImage:
                                              'url("/assets/downloaded/pageprop12.gif")',
                                          }}
                                        >
                                          Layout Properties
                                        </button>
                                      </em>
                                    </td>
                                    <td
                                      className="x-btn-mr"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px -24px no-repeat",
                                        width: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      className="x-btn-bl"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                    <td
                                      className="x-btn-bc"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "0px -15px repeat-x",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    />
                                    <td
                                      className="x-btn-br"
                                      style={{
                                        color: "rgb(34, 34, 34)",
                                        margin: "0px",
                                        background: "-3px -3px no-repeat",
                                        width: "3px",
                                        height: "3px",
                                        backgroundImage: "none",
                                        display: "none",
                                        verticalAlign: "middle",
                                        whiteSpace: "nowrap",
                                        font: "11px Arial, Helvetica, sans-serif",
                                        fontFamily:
                                          "Arial, Helvetica, sans-serif",
                                        backgroundPosition: "500px 500px",
                                        padding: "0px",
                                      }}
                                    >
                                      <i
                                        style={{
                                          overflow: "hidden",
                                          fontSize: "1px",
                                          lineHeight: "1px",
                                          width: "3px",
                                          display: "block",
                                          height: "3px",
                                        }}
                                      ></i>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                    <td
                      className="x-toolbar-right"
                      style={{
                        color: "rgb(34, 34, 34)",
                        margin: "0px",
                        padding: "0px",
                        verticalAlign: "middle",
                        whiteSpace: "nowrap",
                        font: "11px Arial, Helvetica, sans-serif",
                        fontFamily: "Arial, Helvetica, sans-serif",
                      }}
                    >
                      <table className="x-toolbar-right-ct" cellSpacing="0">
                        <tbody>
                          <tr>
                            <td
                              style={{
                                color: "rgb(34, 34, 34)",
                                margin: "0px",
                                padding: "0px",
                                verticalAlign: "middle",
                                whiteSpace: "nowrap",
                                textAlign: "center",
                                font: "11px Arial, Helvetica, sans-serif",
                                fontFamily: "Arial, Helvetica, sans-serif",
                              }}
                            >
                              <table cellSpacing="0">
                                <tbody>
                                  <tr className="x-toolbar-right-row" />
                                </tbody>
                              </table>
                            </td>
                            <td
                              style={{
                                color: "rgb(34, 34, 34)",
                                margin: "0px",
                                padding: "0px",
                                verticalAlign: "middle",
                                whiteSpace: "nowrap",
                                textAlign: "center",
                                font: "11px Arial, Helvetica, sans-serif",
                                fontFamily: "Arial, Helvetica, sans-serif",
                              }}
                            >
                              <table cellSpacing="0">
                                <tbody>
                                  <tr className="x-toolbar-extras-row" />
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div
            id="ext-gen34"
            className="trough-body trough-body-noheader trough-body-noborder"
            style={{ margin: "0px", padding: "0px", width: "1485px" }}
          >
            <div
              id="collapser"
              className="x-panel x-panel-noborder"
              style={{
                margin: "0px",
                padding: "0px",
                borderStyle: "solid",
                borderWidth: "0px",
                borderColor: "rgb(208, 208, 208)",
              }}
            >
              <div
                id="ext-gen37"
                className="x-panel-bwrap"
                style={{
                  margin: "0px",
                  padding: "0px",
                  overflow: "hidden",
                  zoom: 1,
                  left: "0px",
                  top: "0px",
                }}
              >
                <div
                  id="ext-gen38"
                  className="x-panel-body x-panel-body-noheader x-panel-body-noborder x-border-layout-ct"
                  style={{
                    margin: "0px",
                    padding: "0px",
                    borderStyle: "none solid solid",
                    borderImage: "initial",
                    borderTop: "1px solid",
                    overflow: "hidden",
                    zoom: 1,
                    position: "relative",
                    borderColor: "rgb(208, 208, 208)",
                    borderTopColor: "rgb(208, 208, 208)",
                    borderWidth: "0px",
                    backgroundPosition: "0px 0px",
                    backgroundColor: "rgb(241, 245, 247)",
                    backgroundImage: 'url("/assets/downloaded/troughbg.gif")',
                    backgroundRepeat: "repeat-x",
                    height: "142px",
                  }}
                >
                  <div
                    id="troughSelectorWrapper"
                    className="selector selector-noborder x-border-panel"
                    style={{
                      margin: "0px",
                      position: "absolute",
                      borderRight: "4px solid rgb(191, 192, 198)",
                      backgroundPosition: "0px 0px",
                      display: "block",
                      backgroundColor: "rgb(241, 245, 247)",
                      backgroundImage: 'url("/assets/downloaded/troughbg.gif")',
                      backgroundRepeat: "repeat-x",
                      padding: "7px 10px 5px 0px",
                      width: "148px",
                      left: "0px",
                      top: "0px",
                    }}
                  >
                    <div
                      id="ext-gen43"
                      className="selector-bwrap"
                      style={{ margin: "0px", padding: "0px" }}
                    >
                      <div
                        id="ext-gen44"
                        className="selector-body selector-body-noheader selector-body-noborder"
                        style={{
                          margin: "0px",
                          padding: "0px",
                          width: "148px",
                          height: "130px",
                        }}
                      >
                        <div
                          id="troughSelector"
                          style={{
                            margin: "0px",
                            padding: "0px",
                            overflow: "auto",
                            width: "148px",
                            height: "130px",
                          }}
                        >
                          <ul
                            style={{
                              padding: "6px 0px 0px",
                              listStyle: "none",
                              margin: "0px",
                            }}
                          >
                            <li
                              className="selected"
                              style={{
                                textDecoration: "none",
                                padding: "2px",
                                margin: "0px",
                                display: "block",
                                cursor: "pointer",
                                paddingLeft: "2px",
                                marginLeft: "0px",
                                textIndent: "2px",
                                color: "black",
                                fontWeight: "bold",
                              }}
                            >
                              <div
                                id="troughCategory__Field"
                                className="selectorItem"
                                style={{
                                  margin: "0px",
                                  padding: "0px",
                                  color: "rgb(0, 0, 0)",
                                  fontWeight: "bold",
                                  backgroundColor: "rgb(174, 224, 250)",
                                }}
                              >
                                Fields
                              </div>
                            </li>
                            <li
                              style={{
                                textDecoration: "none",
                                padding: "2px",
                                margin: "0px",
                                display: "block",
                                color: "rgb(51, 51, 51)",
                                cursor: "pointer",
                                paddingLeft: "2px",
                                marginLeft: "0px",
                                textIndent: "2px",
                              }}
                            >
                              <div
                                id="troughCategory__Button"
                                className="selectorItem"
                                style={{ margin: "0px", padding: "0px" }}
                              >
                                Buttons
                              </div>
                            </li>
                            <li
                              style={{
                                textDecoration: "none",
                                padding: "2px",
                                margin: "0px",
                                display: "block",
                                color: "rgb(51, 51, 51)",
                                cursor: "pointer",
                                paddingLeft: "2px",
                                marginLeft: "0px",
                                textIndent: "2px",
                              }}
                            >
                              <div
                                id="troughCategory__QuickAction"
                                className="selectorItem"
                                style={{ margin: "0px", padding: "0px" }}
                              >
                                Quick Actions
                              </div>
                            </li>
                            <li
                              style={{
                                textDecoration: "none",
                                padding: "2px",
                                margin: "0px",
                                display: "block",
                                color: "rgb(51, 51, 51)",
                                cursor: "pointer",
                                paddingLeft: "2px",
                                marginLeft: "0px",
                                textIndent: "2px",
                              }}
                            >
                              <div
                                id="troughCategory__PlatformAction"
                                className="selectorItem"
                                style={{ margin: "0px", padding: "0px" }}
                              >
                                Mobile & Lightning Actions
                              </div>
                            </li>
                            <li
                              style={{
                                textDecoration: "none",
                                padding: "2px",
                                margin: "0px",
                                display: "block",
                                color: "rgb(51, 51, 51)",
                                cursor: "pointer",
                                paddingLeft: "2px",
                                marginLeft: "0px",
                                textIndent: "2px",
                              }}
                            >
                              <div
                                id="troughCategory__RelatedLookup"
                                className="selectorItem"
                                style={{ margin: "0px", padding: "0px" }}
                              >
                                Expanded Lookups
                              </div>
                            </li>
                            <li
                              style={{
                                textDecoration: "none",
                                padding: "2px",
                                margin: "0px",
                                display: "block",
                                color: "rgb(51, 51, 51)",
                                cursor: "pointer",
                                paddingLeft: "2px",
                                marginLeft: "0px",
                                textIndent: "2px",
                              }}
                            >
                              <div
                                id="troughCategory__RelatedList"
                                className="selectorItem"
                                style={{ margin: "0px", padding: "0px" }}
                              >
                                Related Lists
                              </div>
                            </li>
                            <li
                              style={{
                                textDecoration: "none",
                                padding: "2px",
                                margin: "0px",
                                display: "block",
                                color: "rgb(51, 51, 51)",
                                cursor: "pointer",
                                paddingLeft: "2px",
                                marginLeft: "0px",
                                textIndent: "2px",
                              }}
                            >
                              <div
                                id="troughCategory__Analytics"
                                className="selectorItem"
                                style={{ margin: "0px", padding: "0px" }}
                              >
                                Report Charts
                              </div>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    id="troughRightPane"
                    className="nonce nonce-noborder x-border-panel"
                    style={{
                      margin: "0px",
                      padding: "0px",
                      position: "absolute",
                      left: "172px",
                      top: "0px",
                      width: "1308px",
                    }}
                  >
                    <div
                      id="ext-gen46"
                      className="nonce-bwrap"
                      style={{ margin: "0px", padding: "0px" }}
                    >
                      <div
                        id="ext-gen47"
                        className="nonce-body nonce-body-noheader nonce-body-noborder x-border-layout-ct"
                        style={{
                          margin: "0px",
                          padding: "0px",
                          position: "relative",
                          backgroundPosition: "0px 0px",
                          backgroundColor: "rgb(241, 245, 247)",
                          backgroundImage:
                            'url("/assets/downloaded/troughbg.gif")',
                          backgroundRepeat: "repeat-x",
                          width: "1308px",
                          height: "127px",
                        }}
                      >
                        <div
                          id="ext-comp-1088"
                          className="tools tools-noborder x-border-panel"
                          style={{
                            margin: "0px",
                            padding: "0px",
                            position: "absolute",
                            backgroundPosition: "0px 0px",
                            backgroundColor: "rgb(241, 245, 247)",
                            backgroundImage:
                              'url("/assets/downloaded/troughbg.gif")',
                            backgroundRepeat: "repeat-x",
                            paddingTop: "6px",
                            left: "0px",
                            top: "0px",
                            width: "1308px",
                          }}
                        >
                          <div
                            id="ext-gen49"
                            className="tools-bwrap"
                            style={{ margin: "0px", padding: "0px" }}
                          >
                            <div
                              id="ext-gen50"
                              className="tools-body tools-body-noheader tools-body-noborder"
                              style={{
                                margin: "0px",
                                padding: "0px",
                                height: "24px",
                                width: "1308px",
                              }}
                            >
                              <div
                                id="ext-gen55"
                                className="toolsInner"
                                style={{
                                  margin: "0px",
                                  padding: "0px",
                                  backgroundPosition: "left bottom",
                                  backgroundImage:
                                    'url("/assets/downloaded/quickfindbg.gif")',
                                  backgroundRepeat: "repeat-x",
                                  color: "rgb(51, 51, 51)",
                                  height: "26px",
                                }}
                              >
                                <img
                                  height={20}
                                  width={20}
                                  src="/assets/downloaded/search20.gif"
                                  style={{
                                    border: "0px",

                                    verticalAlign: "middle",
                                  }}
                                />
                                <label
                                  id="ext-comp-1090"
                                  htmlFor="quickfind"
                                  style={{
                                    fontWeight: "bold",
                                    marginRight: "2px",
                                    fontSize: "95%",
                                  }}
                                >
                                  Quick Find
                                </label>
                                <input
                                  id="quickfind"
                                  className="x-form-text x-form-field x-form-empty-field"
                                  name="quickfind"
                                  type="text"
                                  autoComplete="off"
                                  size={20}
                                  style={{
                                    margin: "0px",
                                    padding: "1px 3px",
                                    background: "0px 0px repeat-x",
                                    paddingTop: "1px",
                                    verticalAlign: "middle",
                                    font: "11px Arial, Helvetica, sans-serif",
                                    lineHeight: "normal",
                                    borderColor: "rgb(181, 184, 200)",
                                    backgroundColor: "rgb(255, 255, 255)",
                                    backgroundImage:
                                      'url("/assets/downloaded/text-bg.gif")',
                                    color: "gray",
                                    fontFamily: "Arial, Helvetica, sans-serif",
                                    fontSize: "100%",
                                    border: "1px solid rgb(153, 153, 153)",
                                    width: "140px",
                                    height: "16px",
                                  }}
                                />
                                <div
                                  id="quickfind_clear"
                                  className="simpleButton"
                                  style={{
                                    padding: "0px",
                                    backgroundPosition: "left top",
                                    margin: "4px 0px 0px",
                                    display: "inline",
                                    height: "14px",
                                    width: "14px",
                                    backgroundImage:
                                      'url("/assets/downloaded/clear28.png")',
                                    backgroundRepeat: "no-repeat",
                                    position: "absolute",
                                    zIndex: 10,
                                    cursor: "pointer",
                                    left: "227px",
                                  }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          id="ext-comp-1089"
                          className="draggables x-border-panel"
                          style={{
                            margin: "0px",
                            padding: "0px",
                            position: "absolute",
                            display: "block",
                            backgroundColor: "rgb(241, 245, 247)",
                            fontSize: "91%",
                            overflow: "auto hidden",
                            height: "9.5em",
                            left: "0px",
                            top: "30px",
                            width: "1308px",
                          }}
                        >
                          <div
                            id="ext-gen52"
                            className="draggables-bwrap"
                            style={{ margin: "0px", padding: "0px" }}
                          >
                            <div
                              id="ext-gen53"
                              className="draggables-body draggables-body-noheader"
                              style={{
                                margin: "0px",
                                padding: "0px",
                                width: "1308px",
                                height: "97px",
                              }}
                            >
                              <div
                                id="fieldTrough"
                                style={{
                                  margin: "0px",
                                  padding: "0px",
                                  width: "249px",
                                  height: "97px",
                                }}
                              >
                                <table className="troughItems">
                                  <tbody>
                                    <tr>
                                      <td
                                        style={{
                                          fontFamily:
                                            "Arial, Helvetica, sans-serif",
                                          color: "rgb(34, 34, 34)",
                                          margin: "0px",
                                          padding: "0px",
                                          verticalAlign: "top",
                                        }}
                                      >
                                        <div
                                          style={{
                                            margin: "0px",
                                            padding: "0px",
                                          }}
                                        >
                                          <div
                                            id="__SECTION"
                                            className="item special"
                                            style={{
                                              padding: "0px",
                                              margin: "1px 0px 0px",
                                              whiteSpace: "nowrap",
                                              overflow: "hidden",
                                              fontSize: "95%",
                                              lineHeight: "14px",
                                              backgroundPosition: "left center",
                                              border:
                                                "1px solid rgb(157, 170, 178)",
                                              backgroundImage:
                                                'url("/assets/downloaded/blankcompbg.gif")',
                                              backgroundRepeat: "repeat-x",
                                              color: "rgb(51, 51, 51)",
                                              cursor: "move",
                                              fontWeight: "bold",
                                              marginTop: "1px",
                                              height: "16px",
                                              width: "120px",
                                              textIndent: "2px",
                                            }}
                                          >
                                            <img
                                              className="icon"
                                              src="/assets/downloaded/newSection.gif"
                                              style={{
                                                border: "0px",
                                                margin: "2px 2px 0px 0px",
                                                verticalAlign: "text-top",
                                              }}
                                            />
                                            <span className="label">
                                              Section
                                            </span>
                                          </div>
                                        </div>
                                        <div
                                          style={{
                                            margin: "0px",
                                            padding: "0px",
                                          }}
                                        >
                                          <div
                                            id="__BLANK"
                                            className="item special"
                                            style={{
                                              padding: "0px",
                                              margin: "1px 0px 0px",
                                              whiteSpace: "nowrap",
                                              overflow: "hidden",
                                              fontSize: "95%",
                                              lineHeight: "14px",
                                              backgroundPosition: "left center",
                                              border:
                                                "1px solid rgb(157, 170, 178)",
                                              backgroundImage:
                                                'url("/assets/downloaded/blankcompbg.gif")',
                                              backgroundRepeat: "repeat-x",
                                              color: "rgb(51, 51, 51)",
                                              cursor: "move",
                                              fontWeight: "bold",
                                              marginTop: "1px",
                                              height: "16px",
                                              width: "120px",
                                              textIndent: "2px",
                                            }}
                                          >
                                            <img
                                              className="icon"
                                              src="/assets/downloaded/blankSpace.gif"
                                              style={{
                                                border: "0px",
                                                margin: "2px 2px 0px 0px",
                                                verticalAlign: "text-top",
                                              }}
                                            />
                                            <span className="label">
                                              Blank Space
                                            </span>
                                          </div>
                                        </div>
                                        <div
                                          style={{
                                            margin: "0px",
                                            padding: "0px",
                                          }}
                                        >
                                          <div
                                            id="CreatedBy"
                                            className="item used"
                                            style={{
                                              padding: "0px",
                                              margin: "1px 0px 0px",
                                              whiteSpace: "nowrap",
                                              overflow: "hidden",
                                              fontSize: "95%",
                                              height: "16px",
                                              fontWeight: "bold",
                                              lineHeight: "14px",
                                              border:
                                                "1px solid rgb(207, 215, 219)",
                                              color: "rgb(119, 119, 119)",
                                              width: "120px",
                                              textIndent: "4px",
                                              cursor: "default",
                                            }}
                                          >
                                            <span className="label">
                                              Created By
                                            </span>
                                          </div>
                                        </div>
                                        <div
                                          style={{
                                            margin: "0px",
                                            padding: "0px",
                                          }}
                                        >
                                          <div
                                            id="Name"
                                            className="item used"
                                            style={{
                                              padding: "0px",
                                              margin: "1px 0px 0px",
                                              whiteSpace: "nowrap",
                                              overflow: "hidden",
                                              fontSize: "95%",
                                              height: "16px",
                                              fontWeight: "bold",
                                              lineHeight: "14px",
                                              border:
                                                "1px solid rgb(207, 215, 219)",
                                              color: "rgb(119, 119, 119)",
                                              width: "120px",
                                              textIndent: "4px",
                                              cursor: "default",
                                            }}
                                          >
                                            <span className="label"></span>
                                          </div>
                                        </div>
                                      </td>
                                      <td
                                        style={{
                                          fontFamily:
                                            "Arial, Helvetica, sans-serif",
                                          color: "rgb(34, 34, 34)",
                                          margin: "0px",
                                          padding: "0px",
                                          verticalAlign: "top",
                                        }}
                                      >
                                        <div
                                          style={{
                                            margin: "0px",
                                            padding: "0px",
                                          }}
                                        >
                                          <div
                                            id="LastModifiedBy"
                                            className="item used"
                                            style={{
                                              padding: "0px",
                                              margin: "1px 0px 0px",
                                              whiteSpace: "nowrap",
                                              overflow: "hidden",
                                              fontSize: "95%",
                                              height: "16px",
                                              fontWeight: "bold",
                                              lineHeight: "14px",
                                              border:
                                                "1px solid rgb(207, 215, 219)",
                                              color: "rgb(119, 119, 119)",
                                              width: "120px",
                                              textIndent: "4px",
                                              cursor: "default",
                                            }}
                                          >
                                            <span className="label">
                                              Last Modified By
                                            </span>
                                          </div>
                                        </div>
                                        <div
                                          style={{
                                            margin: "0px",
                                            padding: "0px",
                                          }}
                                        >
                                          <div
                                            id="Owner"
                                            className="item used"
                                            style={{
                                              padding: "0px",
                                              margin: "1px 0px 0px",
                                              whiteSpace: "nowrap",
                                              overflow: "hidden",
                                              fontSize: "95%",
                                              height: "16px",
                                              fontWeight: "bold",
                                              lineHeight: "14px",
                                              border:
                                                "1px solid rgb(207, 215, 219)",
                                              color: "rgb(119, 119, 119)",
                                              width: "120px",
                                              textIndent: "4px",
                                              cursor: "default",
                                            }}
                                          >
                                            <span className="label">Owner</span>
                                          </div>
                                        </div>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              id="collapserControl"
              className="x-panel collapseTarget x-panel-noborder"
              style={{
                margin: "0px",
                padding: "0px",
                borderStyle: "solid",
                borderWidth: "0px",
                borderColor: "rgb(208, 208, 208)",
              }}
            >
              <div
                id="ext-gen40"
                className="x-panel-bwrap"
                style={{
                  margin: "0px",
                  padding: "0px",
                  overflow: "hidden",
                  zoom: 1,
                  left: "0px",
                  top: "0px",
                }}
              >
                <div
                  id="ext-gen41"
                  className="x-panel-body x-panel-body-noheader x-panel-body-noborder"
                  style={{
                    margin: "0px",
                    padding: "0px",
                    borderStyle: "none solid solid",
                    borderImage: "initial",
                    position: "relative",
                    borderTop: "1px solid",
                    overflow: "hidden",
                    zoom: 1,
                    borderColor: "rgb(208, 208, 208)",
                    borderTopColor: "rgb(208, 208, 208)",
                    borderWidth: "0px",
                    backgroundPosition: "center top",
                    backgroundImage:
                      'url("/assets/downloaded/collapseTargetbg.gif")',
                    backgroundRepeat: "no-repeat",
                    backgroundColor: "rgb(149, 148, 148)",
                    height: "8px",
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        <div
          id="ext-comp-1091"
          className="bPageTitle headerSection"
          style={{
            margin: "0px",
            marginBottom: "15px",
            padding: "15px 0px 0px",
          }}
        >
          <div
            className="secondaryPalette"
            style={{
              margin: "0px",
              padding: "0px",
              color: "rgb(255, 255, 255)",
              borderColor: "rgb(229, 193, 48)",
              backgroundColor: "rgb(229, 193, 48)",
              borderRadius: "4px",
            }}
          >
            <h1
              className="title"
              style={{
                fontWeight: "bold",
                fontFamily: "Arial, Helvetica, sans-serif",
                margin: "0px",
                fontSize: "100%",
                display: "block",
                padding: "5px 4px",
              }}
            >
              Opportunity Sample
            </h1>
          </div>
        </div>

        <div
          id="ext-gen79"
          className="bPageBlock secondaryPalette hpPageBlock"
          style={{
            backgroundPosition: "left bottom",
            backgroundRepeat: "no-repeat",
            clear: "both",
            margin: "0px",
            backgroundImage: "none",
            padding: "0px",
            paddingBottom: "0px",
            borderTop: "3px solid rgb(116, 126, 150)",
            borderColor: "rgb(116, 126, 150)",
            borderBottom: "1px solid rgb(234, 234, 234)",
            borderLeft: "1px solid rgb(234, 234, 234)",
            borderRight: "1px solid rgb(234, 234, 234)",
            borderRadius: "4px",
            border: "0px none transparent",
            backgroundColor: "transparent",
            borderTopWidth: "0px",
            borderRightColor: "transparent",
            borderBottomColor: "transparent",
            borderLeftColor: "transparent",
            marginBottom: "2px",
          }}
        >
          <div
            id="ext-gen80"
            className="pbHeader"
            style={{
              lineHeight: "17px",
              borderBottom: "1px solid rgb(255, 255, 255)",
              padding: "0px",
              margin: "0px",
              backgroundColor: "transparent",
              paddingBottom: "0px",
              border: "0px",
            }}
          >
            <table style={{ borderSpacing: "0px", width: "100%" }}>
              <tbody>
                <tr>
                  <td
                    className="pbTitle"
                    style={{
                      fontFamily: "Arial, Helvetica, sans-serif",
                      margin: "0px",
                      color: "rgb(34, 34, 34)",
                      fontSize: "91%",
                      width: "30%",
                      padding: "5px 12px",
                      verticalAlign: "top",
                    }}
                  >
                    <h3
                      style={{
                        fontWeight: "bold",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        padding: "0px",
                        display: "block",
                        margin: "0px",
                        color: "rgb(0, 0, 0)",
                        fontSize: "1.3em",
                      }}
                    >
                      Highlights Panel
                    </h3>
                  </td>
                  <td
                    className="pbButtons"
                    style={{
                      fontFamily: "Arial, Helvetica, sans-serif",
                      color: "rgb(34, 34, 34)",
                      margin: "0px",
                      padding: "5px 12px",
                    }}
                  />
                </tr>
              </tbody>
            </table>
          </div>
          <div
            id="ext-gen81"
            className="pbBody"
            style={{
              color: "rgb(0, 0, 0)",
              margin: "0px",
              backgroundColor: "transparent",
              backgroundImage: "none",
              marginRight: "0px",
              padding: "0px 6px 6px",
            }}
          />
          <div
            id="ext-gen82"
            className="pbFooter secondaryPalette"
            style={{
              margin: "0px",
              padding: "0px",
              backgroundPosition: "right bottom",
              height: "9px",
              width: "9px",
              cssFloat: "right",
              backgroundImage: 'url("/assets/downloaded/bgPageBlockRight.gif")',
              backgroundRepeat: "repeat-x",
              display: "none",
              borderColor: "rgb(116, 126, 150)",
              backgroundColor: "rgb(116, 126, 150)",
            }}
          >
            <div className="bg" style={{ margin: "0px", padding: "0px" }} />
          </div>
        </div>

        <div
          id="ext-comp-1046"
          className="x-panel summaryLayout x-panel-noborder"
          style={{
            margin: "0px",
            padding: "0px",
            borderStyle: "solid",
            borderWidth: "0px",
            borderColor: "rgb(208, 208, 208)",
            marginBottom: "5px",
            marginTop: "-10px",
          }}
        >
          <div
            id="ext-gen84"
            className="x-panel-bwrap"
            style={{
              margin: "0px",
              padding: "0px",
              overflow: "hidden",
              zoom: 1,
              left: "0px",
              top: "0px",
            }}
          >
            <div
              id="ext-gen85"
              className="x-panel-body x-panel-body-noheader x-panel-body-noborder x-unselectable"
              style={{
                margin: "0px",
                padding: "0px",
                borderStyle: "none solid solid",
                borderImage: "initial",
                position: "relative",
                borderTop: "1px solid",
                overflow: "hidden",
                zoom: 1,
                borderColor: "rgb(208, 208, 208)",
                backgroundColor: "rgb(255, 255, 255)",
                borderTopColor: "rgb(208, 208, 208)",
                borderWidth: "0px",
                height: "43px",
              }}
            >
              <table
                id="summaryItemsTable"
                cellSpacing="10"
                style={{
                  backgroundColor: "rgb(243, 243, 236)",
                  width: "100%",
                  tableLayout: "fixed",
                  height: "40px",
                }}
              >
                <tbody>
                  <tr>
                    <td
                      style={{
                        fontFamily: "Arial, Helvetica, sans-serif",
                        color: "rgb(34, 34, 34)",
                        margin: "0px",
                        padding: "5px",
                        whiteSpace: "nowrap",
                        backgroundColor: "rgb(255, 255, 255)",
                        width: "100%",
                      }}
                    >
                      <div
                        id="ext-gen202"
                        className="slItemWrapBackground"
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflow: "hidden",
                          backgroundColor: "rgb(255, 255, 255)",
                        }}
                      >
                        <div
                          id="ext-gen203"
                          className="slEmptyText"
                          style={{
                            margin: "0px",
                            padding: "0px",
                            color: "rgb(153, 153, 153)",
                          }}
                        >
                          Customize the highlights panel for this page layout...
                        </div>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div
                id="slHoverGearDiv"
                className="summaryHoverGear"
                style={{
                  margin: "0px",
                  padding: "0px",
                  visibility: "hidden",
                  cssFloat: "right",
                  width: "38px",
                  height: "38px",
                  backgroundImage: 'url("/assets/downloaded/hp_wrench.png")',
                  zIndex: 2,
                  cursor: "pointer",
                  position: "relative",
                  left: "0px",
                  top: "-43px",
                }}
              />
              <div
                id="slHoverDiv"
                className="summaryHover"
                style={{
                  margin: "0px",
                  padding: "0px",
                  opacity: 0.55,
                  backgroundColor: "rgb(225, 246, 255)",
                  zIndex: -1,
                  height: "43px",
                  width: "1496px",
                  position: "relative",
                  left: "0px",
                  top: "-43px",
                }}
              />
            </div>
          </div>
        </div>

        <div
          id="ext-comp-1044"
          className="canvasBodyPanel"
          style={{ margin: "0px", padding: "0px", clear: "both" }}
        >
          <div
            id="ext-gen89"
            className="canvasBodyPanel-bwrap"
            style={{ margin: "0px", padding: "0px" }}
          >
            <div
              id="ext-gen90"
              className="canvasBodyPanel-body canvasBodyPanel-body-noheader"
              style={{ margin: "0px", padding: "0px" }}
            >
              <div
                id="ext-gen93"
                className="bPageBlock secondaryPalette"
                style={{
                  backgroundPosition: "left bottom",
                  backgroundRepeat: "no-repeat",
                  clear: "both",
                  margin: "0px",
                  backgroundImage: "none",
                  padding: "0px",
                  paddingBottom: "0px",
                  marginBottom: "10px",
                  borderTop: "3px solid rgb(116, 126, 150)",
                  borderColor: "rgb(116, 126, 150)",
                  borderBottom: "1px solid rgb(234, 234, 234)",
                  borderLeft: "1px solid rgb(234, 234, 234)",
                  borderRight: "1px solid rgb(234, 234, 234)",
                  borderRadius: "4px",
                  border: "0px none transparent",
                  borderTopWidth: "0px",
                  borderRightColor: "transparent",
                  borderBottomColor: "transparent",
                  borderLeftColor: "transparent",
                  backgroundColor: "transparent",
                }}
              >
                <div
                  id="__QUICK_ACTION"
                  className="buttonBar quickActionBar"
                  style={{
                    margin: "0px",
                    padding: "0px",
                    lineHeight: "210%",
                    paddingBottom: "10px",
                  }}
                >
                  <div
                    id="ext-gen120"
                    className="pbTitle"
                    style={{
                      margin: "0px",
                      color: "rgb(34, 34, 34)",
                      backgroundColor: "rgb(255, 255, 255)",
                      fontSize: "91%",
                      width: "30%",
                      padding: "5px 12px",
                      paddingTop: "1px",
                      paddingBottom: "1px",
                      verticalAlign: "top",
                    }}
                  >
                    <h3
                      id="ext-gen121"
                      style={{
                        display: "inline",
                        fontWeight: "bold",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "0px",
                        padding: "0px",
                        color: "rgb(0, 0, 0)",
                        backgroundColor: "rgb(255, 255, 255)",
                        fontSize: "1.3em",
                      }}
                    >
                      Quick Actions in the Salesforce Classic Publisher
                    </h3>

                    <fieldset
                      id="ext-gen182"
                      className="customButtons qaDrop hover"
                      style={{
                        margin: "2px 2px 0px 0px",
                        display: "inline",
                        width: "100%",
                        backgroundColor: "rgb(255, 255, 255)",
                        border: "1px dotted rgb(204, 204, 204)",
                        padding: "10px 0px",
                        paddingLeft: "0px",
                        font: "11.36px / 23.856px Helvetica",
                        lineHeight: "23.856px",
                        whiteSpace: "nowrap",
                      }}
                    >
                      <legend
                        style={{
                          textAlign: "left",
                          fontSize: "91%",
                          fontWeight: "normal",
                          lineHeight: "10px",
                          color: "rgb(153, 153, 153)",
                          margin: "0px 3px",
                          padding: "0px",
                        }}
                      />
                      <div
                        className="firstSpace"
                        style={{
                          margin: "0px",
                          padding: "0px",
                          marginLeft: "10px",
                          display: "inline",
                        }}
                      />
                      <div
                        id="item_QA__FeedItem.TextPost"
                        className="btn customButton"
                        style={{
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          fontWeight: "bold",
                          whiteSpace: "nowrap",
                          background: "transparent",
                          borderColor: "rgb(165, 177, 184)",
                          backgroundImage: "initial",
                          backgroundRepeat: "initial",
                          borderRadius: "2px",
                          border: "1px solid rgb(226, 226, 226)",
                          paddingLeft: "10px",
                          paddingRight: "30px",
                          marginLeft: "3px",
                          backgroundColor: "rgb(248, 248, 248)",
                          color: "rgb(34, 34, 34)",
                          fontSize: "1em",
                          display: "inline-block",
                          lineHeight: "1em",
                        }}
                      >
                        Post
                      </div>
                      <span> </span>
                      <div
                        id="item_QA__FeedItem.ContentPost"
                        className="btn customButton"
                        style={{
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          fontWeight: "bold",
                          whiteSpace: "nowrap",
                          background: "transparent",
                          borderColor: "rgb(165, 177, 184)",
                          backgroundImage: "initial",
                          backgroundRepeat: "initial",
                          borderRadius: "2px",
                          border: "1px solid rgb(226, 226, 226)",
                          paddingLeft: "10px",
                          paddingRight: "30px",
                          marginLeft: "3px",
                          backgroundColor: "rgb(248, 248, 248)",
                          color: "rgb(34, 34, 34)",
                          fontSize: "1em",
                          display: "inline-block",
                          lineHeight: "1em",
                        }}
                      >
                        File
                      </div>
                      <span> </span>
                      <div
                        id="item_QA__09Daj00000Ce8rB"
                        className="btn customButton"
                        style={{
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          fontWeight: "bold",
                          whiteSpace: "nowrap",
                          background: "transparent",
                          borderColor: "rgb(165, 177, 184)",
                          backgroundImage: "initial",
                          backgroundRepeat: "initial",
                          borderRadius: "2px",
                          border: "1px solid rgb(226, 226, 226)",
                          paddingLeft: "10px",
                          paddingRight: "30px",
                          marginLeft: "3px",
                          backgroundColor: "rgb(248, 248, 248)",
                          color: "rgb(34, 34, 34)",
                          fontSize: "1em",
                          display: "inline-block",
                          lineHeight: "1em",
                        }}
                      >
                        New Task
                      </div>
                      <span> </span>
                      <div
                        id="item_QA__09Daj00000Ce8rC"
                        className="btn customButton"
                        style={{
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          fontWeight: "bold",
                          whiteSpace: "nowrap",
                          background: "transparent",
                          borderColor: "rgb(165, 177, 184)",
                          backgroundImage: "initial",
                          backgroundRepeat: "initial",
                          borderRadius: "2px",
                          border: "1px solid rgb(226, 226, 226)",
                          paddingLeft: "10px",
                          paddingRight: "30px",
                          marginLeft: "3px",
                          backgroundColor: "rgb(248, 248, 248)",
                          color: "rgb(34, 34, 34)",
                          fontSize: "1em",
                          display: "inline-block",
                          lineHeight: "1em",
                        }}
                      >
                        Log a Call
                      </div>
                      <span> </span>
                      <div
                        id="item_QA__09Daj00000Ce8rK"
                        className="btn customButton"
                        style={{
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          fontWeight: "bold",
                          whiteSpace: "nowrap",
                          background: "transparent",
                          borderColor: "rgb(165, 177, 184)",
                          backgroundImage: "initial",
                          backgroundRepeat: "initial",
                          borderRadius: "2px",
                          border: "1px solid rgb(226, 226, 226)",
                          paddingLeft: "10px",
                          paddingRight: "30px",
                          marginLeft: "3px",
                          backgroundColor: "rgb(248, 248, 248)",
                          color: "rgb(34, 34, 34)",
                          fontSize: "1em",
                          display: "inline-block",
                          lineHeight: "1em",
                        }}
                      >
                        New Case
                      </div>
                      <span> </span>
                      <div
                        id="item_QA__09Daj00000Ce8rN"
                        className="btn customButton"
                        style={{
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          fontWeight: "bold",
                          whiteSpace: "nowrap",
                          background: "transparent",
                          borderColor: "rgb(165, 177, 184)",
                          backgroundImage: "initial",
                          backgroundRepeat: "initial",
                          borderRadius: "2px",
                          border: "1px solid rgb(226, 226, 226)",
                          paddingLeft: "10px",
                          paddingRight: "30px",
                          marginLeft: "3px",
                          backgroundColor: "rgb(248, 248, 248)",
                          color: "rgb(34, 34, 34)",
                          fontSize: "1em",
                          display: "inline-block",
                          lineHeight: "1em",
                        }}
                      >
                        New Note
                      </div>
                      <span> </span>
                      <div
                        id="item_QA__09Daj00000Ce8rD"
                        className="btn customButton"
                        style={{
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          fontWeight: "bold",
                          whiteSpace: "nowrap",
                          background: "transparent",
                          borderColor: "rgb(165, 177, 184)",
                          backgroundImage: "initial",
                          backgroundRepeat: "initial",
                          borderRadius: "2px",
                          border: "1px solid rgb(226, 226, 226)",
                          paddingLeft: "10px",
                          paddingRight: "30px",
                          marginLeft: "3px",
                          backgroundColor: "rgb(248, 248, 248)",
                          color: "rgb(34, 34, 34)",
                          fontSize: "1em",
                          display: "inline-block",
                          lineHeight: "1em",
                        }}
                      >
                        New Event
                      </div>
                      <span> </span>
                      <div
                        id="item_QA__FeedItem.LinkPost"
                        className="btn customButton"
                        style={{
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          fontWeight: "bold",
                          whiteSpace: "nowrap",
                          background: "transparent",
                          borderColor: "rgb(165, 177, 184)",
                          backgroundImage: "initial",
                          backgroundRepeat: "initial",
                          borderRadius: "2px",
                          border: "1px solid rgb(226, 226, 226)",
                          paddingLeft: "10px",
                          paddingRight: "30px",
                          marginLeft: "3px",
                          backgroundColor: "rgb(248, 248, 248)",
                          color: "rgb(34, 34, 34)",
                          fontSize: "1em",
                          display: "inline-block",
                          lineHeight: "1em",
                        }}
                      >
                        Link
                      </div>
                      <span> </span>
                      <div
                        id="item_QA__FeedItem.PollPost"
                        className="btn customButton"
                        style={{
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          fontWeight: "bold",
                          whiteSpace: "nowrap",
                          background: "transparent",
                          borderColor: "rgb(165, 177, 184)",
                          backgroundImage: "initial",
                          backgroundRepeat: "initial",
                          borderRadius: "2px",
                          border: "1px solid rgb(226, 226, 226)",
                          paddingLeft: "10px",
                          paddingRight: "30px",
                          marginLeft: "3px",
                          backgroundColor: "rgb(248, 248, 248)",
                          color: "rgb(34, 34, 34)",
                          fontSize: "1em",
                          display: "inline-block",
                          lineHeight: "1em",
                        }}
                      >
                        Poll
                      </div>
                      <span> </span>
                      <div
                        id="item_QA__FeedItem.QuestionPost"
                        className="btn customButton"
                        style={{
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          fontWeight: "bold",
                          whiteSpace: "nowrap",
                          background: "transparent",
                          borderColor: "rgb(165, 177, 184)",
                          backgroundImage: "initial",
                          backgroundRepeat: "initial",
                          borderRadius: "2px",
                          border: "1px solid rgb(226, 226, 226)",
                          paddingLeft: "10px",
                          paddingRight: "30px",
                          marginLeft: "3px",
                          backgroundColor: "rgb(248, 248, 248)",
                          color: "rgb(34, 34, 34)",
                          fontSize: "1em",
                          display: "inline-block",
                          lineHeight: "1em",
                        }}
                      >
                        Question
                      </div>
                      <span> </span>
                      <div
                        id="item_QA__09Daj00000Ce8rM"
                        className="btn customButton"
                        style={{
                          backgroundPosition: "left top",
                          borderRight: "1px solid rgb(92, 93, 97)",
                          borderBottom: "1px solid rgb(92, 93, 97)",
                          borderTop: "none",
                          borderLeft: "none",
                          cursor: "pointer",
                          fontFamily: "Arial, Helvetica, sans-serif",
                          margin: "1px",
                          padding: "2px 3px",
                          borderWidth: "1px",
                          borderStyle: "solid",
                          borderImage: "initial",
                          fontWeight: "bold",
                          whiteSpace: "nowrap",
                          background: "transparent",
                          borderColor: "rgb(165, 177, 184)",
                          backgroundImage: "initial",
                          backgroundRepeat: "initial",
                          borderRadius: "2px",
                          border: "1px solid rgb(226, 226, 226)",
                          paddingLeft: "10px",
                          paddingRight: "30px",
                          marginLeft: "3px",
                          backgroundColor: "rgb(255, 255, 255)",
                          color: "rgb(34, 34, 34)",
                          fontSize: "1em",
                          display: "inline-block",
                          lineHeight: "1em",
                        }}
                      >
                        Email
                      </div>
                    </fieldset>
                  </div>
                  <div
                    id="ext-gen115"
                    className="buttonBar-bwrap"
                    style={{ margin: "0px", padding: "0px" }}
                  >
                    <div
                      id="ext-gen116"
                      className="buttonBar-body buttonBar-body-noheader"
                      style={{ margin: "0px", padding: "0px" }}
                    >
                      <fieldset
                        id="ext-gen123"
                        className="customButtons inheritSettings qaDrop"
                        style={{
                          margin: "2px 2px 0px 0px",
                          display: "inline",
                          backgroundColor: "rgb(243, 243, 236)",
                          width: "100%",
                          border: "1px dotted rgb(204, 204, 204)",
                          padding: "10px 0px",
                          paddingLeft: "0px",
                        }}
                      >
                        <legend
                          style={{
                            textAlign: "left",
                            fontSize: "91%",
                            fontWeight: "normal",
                            lineHeight: "10px",
                            color: "rgb(153, 153, 153)",
                            margin: "0px 3px",
                            padding: "0px",
                          }}
                        />
                      </fieldset>
                    </div>
                  </div>
                </div>
                <div
                  id="__PLATFORM_ACTION"
                  className="buttonBar platformActionCust"
                  style={{
                    margin: "0px",
                    padding: "0px",
                    lineHeight: "210%",
                    paddingBottom: "10px",
                  }}
                >
                  <div
                    id="ext-gen106"
                    className="pbTitle"
                    style={{
                      margin: "0px",
                      color: "rgb(34, 34, 34)",
                      fontSize: "91%",
                      width: "30%",
                      padding: "5px 12px",
                      paddingTop: "1px",
                      paddingBottom: "1px",
                      verticalAlign: "top",
                      whiteSpace: "nowrap",
                    }}
                  >
                    <h3
                      id="ext-gen107"
                      style={{
                        display: "inline",
                        fontWeight: "bold",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "0px",
                        padding: "0px",
                        color: "rgb(0, 0, 0)",
                        fontSize: "1.3em",
                      }}
                    >
                      Salesforce Mobile and Lightning Experience Actions
                    </h3>
                    <div
                      id="ext-gen108"
                      className="mouseOverInfoOuter"
                      style={{
                        margin: "0px",
                        padding: "0px",
                        textDecoration: "none",
                        position: "relative",
                        display: "inline",
                        lineHeight: "1.2em",
                        marginLeft: "3px",
                      }}
                    >
                      <img
                        id="publisherActionsInfoIcon"
                        className="infoIcon"
                        src="/assets/downloaded/s.gif"
                        style={{
                          border: "0px",
                          width: "16px",
                          backgroundPosition: "left top",
                          backgroundImage:
                            'url("/assets/downloaded/info_sprite.png")',
                          height: "15px",
                        }}
                      />
                      <div
                        id="publisherActionsInfo"
                        className="mouseOverInfo"
                        style={{
                          margin: "0px",
                          border: "1px solid black",
                          whiteSpace: "normal",
                          position: "absolute",
                          display: "none",
                          left: "22px",
                          bottom: "20px",
                          width: "20em",
                          zIndex: 11,
                          opacity: 0,
                          fontWeight: "normal",
                          color: "rgb(0, 0, 0)",
                          borderColor: "rgb(51, 51, 51)",
                          padding: "8px 10px",
                          borderRadius: "3px",
                          backgroundColor: "rgb(255, 255, 208)",
                          fontSize: "1em",
                        }}
                      >
                        Feed tracking is disabled for this object, but you can
                        still customize actions for Lightning Experience and the
                        mobile app action bar. Actions in this section appear
                        only in Lightning Experience and the mobile app, and may
                        appear in third party apps that use this page layout.
                      </div>
                    </div>
                  </div>
                  <div
                    id="ext-gen101"
                    className="buttonBar-bwrap"
                    style={{ margin: "0px", padding: "0px" }}
                  ></div>
                  <fieldset
                    id="ext-gen171"
                    className="customButtons paDrop hover"
                    style={{
                      margin: "2px 2px 0px 0px",
                      display: "inline",
                      width: "100%",
                      backgroundColor: "rgb(255, 255, 255)",
                      border: "1px dotted rgb(204, 204, 204)",
                      padding: "10px 0px",
                      paddingLeft: "0px",
                      font: "11.36px / 23.856px Helvetica",
                      lineHeight: "23.856px",
                    }}
                  >
                    <legend
                      style={{
                        textAlign: "left",
                        fontSize: "91%",
                        fontWeight: "normal",
                        lineHeight: "10px",
                        color: "rgb(153, 153, 153)",
                        margin: "0px 3px",
                        padding: "0px",
                      }}
                    />
                    <div
                      className="firstSpace"
                      style={{
                        margin: "0px",
                        padding: "0px",
                        marginLeft: "10px",
                        display: "inline",
                      }}
                    />
                    <div
                      id="item_QuickAction__Post__FeedItem.TextPost"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Post
                    </div>
                    <span> </span>
                    <div
                      id="item_QuickAction__Post__FeedItem.ContentPost"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      File
                    </div>
                    <span> </span>
                    <div
                      id="item_QuickAction__Create__NewTask"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      New Task
                    </div>
                    <span> </span>
                    <div
                      id="item_QuickAction__LogACall__LogACall"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Log a Call
                    </div>
                    <span> </span>
                    <div
                      id="item_QuickAction__Create__NewCase"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      New Case
                    </div>
                    <span> </span>
                    <div
                      id="item_QuickAction__Create__NewNote"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      New Note
                    </div>
                    <span> </span>
                    <div
                      id="item_QuickAction__Create__NewEvent"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      New Event
                    </div>
                    <span> </span>
                    <div
                      id="item_QuickAction__Post__FeedItem.LinkPost"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Link
                    </div>
                    <span> </span>
                    <div
                      id="item_QuickAction__Post__FeedItem.PollPost"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Poll
                    </div>
                    <span> </span>
                    <div
                      id="item_QuickAction__Post__FeedItem.QuestionPost"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Question
                    </div>
                    <span> </span>
                    <div
                      id="item_QuickAction__SendEmail__SendEmail"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Email
                    </div>
                    <span> </span>
                    <div
                      id="item_StandardButton__null__Submit"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Submit for Approval
                    </div>
                    <span> </span>
                    <div
                      id="item_StandardButton__null__ChangeRecordType"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Change Record Type
                    </div>
                    <span> </span>
                    <div
                      id="item_StandardButton__null__Clone"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Clone
                    </div>
                    <span> </span>
                    <div
                      id="item_StandardButton__null__Share"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Sharing
                    </div>
                    <span> </span>
                    <div
                      id="item_StandardButton__null__Edit"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Edit
                    </div>
                    <span> </span>
                    <div
                      id="item_StandardButton__null__Delete"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Delete
                    </div>
                    <span> </span>
                    <div
                      id="item_StandardButton__null__ChangeOwnerOne"
                      className="btn customButton"
                      style={{
                        backgroundPosition: "left top",
                        borderRight: "1px solid rgb(92, 93, 97)",
                        borderBottom: "1px solid rgb(92, 93, 97)",
                        borderTop: "none",
                        borderLeft: "none",
                        cursor: "pointer",
                        fontFamily: "Arial, Helvetica, sans-serif",
                        margin: "1px",
                        padding: "2px 3px",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        borderImage: "initial",
                        fontWeight: "bold",
                        whiteSpace: "nowrap",
                        background: "transparent",
                        borderColor: "rgb(165, 177, 184)",
                        backgroundImage: "initial",
                        backgroundRepeat: "initial",
                        borderRadius: "2px",
                        border: "1px solid rgb(226, 226, 226)",
                        paddingLeft: "10px",
                        paddingRight: "30px",
                        marginLeft: "3px",
                        backgroundColor: "rgb(248, 248, 248)",
                        color: "rgb(34, 34, 34)",
                        fontSize: "1em",
                        display: "inline-block",
                        lineHeight: "1em",
                      }}
                    >
                      Change Owner
                    </div>
                    <span> </span>
                    <div
                      id="ext-gen172"
                      className="revertPlatformActionIconContainer"
                      title="Revert to Mobile Action Defaults"
                      style={{
                        margin: "0px",
                        padding: "0px",
                        display: "inline",
                      }}
                    >
                      <div
                        className="revertPlatformActionIcon"
                        style={{
                          margin: "0px",
                          padding: "0px",
                          cssFloat: "right",
                          width: "16px",
                          height: "16px",
                          marginRight: "8px",
                          backgroundImage:
                            'url("/assets/downloaded/quickActions_revert.png")',
                          cursor: "pointer",
                          visibility: "visible",
                        }}
                      />
                    </div>
                  </fieldset>
                </div>
                <div
                  id="ext-gen94"
                  className="pbHeader"
                  style={{
                    lineHeight: "17px",
                    borderBottom: "1px solid rgb(255, 255, 255)",
                    padding: "0px",
                    margin: "0px",
                    backgroundColor: "transparent",
                    paddingBottom: "0px",
                    border: "0px",
                  }}
                >
                  <table style={{ borderSpacing: "0px", width: "100%" }}>
                    <tbody>
                      <tr>
                        <td
                          className="pbTitle"
                          style={{
                            fontFamily: "Arial, Helvetica, sans-serif",
                            margin: "0px",
                            color: "rgb(34, 34, 34)",
                            fontSize: "91%",
                            width: "30%",
                            padding: "5px 12px",
                            verticalAlign: "top",
                          }}
                        >
                          <h3
                            style={{
                              fontWeight: "bold",
                              fontFamily: "Arial, Helvetica, sans-serif",
                              padding: "0px",
                              display: "block",
                              margin: "0px",
                              color: "rgb(0, 0, 0)",
                              fontSize: "1.3em",
                            }}
                          >
                            {(() => {
                              // Get the most recent custom object name
                              const windowData =
                                window.allVariableData?.objectManager
                                  ?.customObjectData;
                              const contextData =
                                allVariableData?.objectManager
                                  ?.customObjectData;

                              // Use whichever data source is available
                              const customObjectData =
                                windowData || contextData;

                              if (
                                customObjectData &&
                                Array.isArray(customObjectData) &&
                                customObjectData.length > 0
                              ) {
                                return `${
                                  customObjectData.slice(-1)[0].MasterLabel
                                } Detail`;
                              }

                              return "Opportunity Detail";
                            })()}
                          </h3>
                        </td>
                        <td
                          id="ext-gen129"
                          className="pbButtons"
                          style={{
                            fontFamily: "Arial, Helvetica, sans-serif",
                            color: "rgb(34, 34, 34)",
                            margin: "0px",
                            padding: "5px 12px",
                          }}
                        >
                          <div
                            id="__BUTTON"
                            className="buttonBar"
                            style={{
                              margin: "0px",
                              padding: "0px",
                              lineHeight: "210%",
                            }}
                          >
                            <div
                              id="ext-gen130"
                              className="buttonBar-bwrap"
                              style={{ margin: "0px", padding: "0px" }}
                            >
                              <div
                                id="ext-gen131"
                                className="buttonBar-body buttonBar-body-noheader"
                                style={{ margin: "0px", padding: "0px" }}
                              >
                                <fieldset
                                  className="stdButtons sButtonDrop"
                                  style={{
                                    margin: "2px 2px 0px 0px",
                                    display: "inline",
                                    border: "1px dotted rgb(204, 204, 204)",
                                    padding: "0px 5px 3px",
                                    paddingLeft: "5px",
                                  }}
                                >
                                  <legend
                                    style={{
                                      textAlign: "left",
                                      fontSize: "91%",
                                      fontWeight: "normal",
                                      lineHeight: "10px",
                                      color: "rgb(153, 153, 153)",
                                    }}
                                  >
                                    Standard Buttons
                                  </legend>
                                  <div
                                    className="firstSpace"
                                    style={{ margin: "0px", padding: "0px" }}
                                  />
                                  <div
                                    id="item_BTN__Edit"
                                    className="btn stdButton"
                                    style={{
                                      backgroundPosition: "left top",
                                      borderRight: "1px solid rgb(92, 93, 97)",
                                      borderBottom: "1px solid rgb(92, 93, 97)",
                                      borderTop: "none",
                                      borderLeft: "none",
                                      cursor: "pointer",
                                      display: "inline",
                                      fontFamily:
                                        "Arial, Helvetica, sans-serif",
                                      padding: "2px 3px",
                                      borderWidth: "1px",
                                      borderStyle: "solid",
                                      borderColor:
                                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                      borderImage: "initial",
                                      background:
                                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                      borderRadius: "3px",
                                      color: "rgb(51, 51, 51)",
                                      backgroundImage:
                                        'url("/assets/downloaded/btn_sprite.png")',
                                      backgroundRepeat: "repeat-x",
                                      fontWeight: "bold",
                                      fontSize: "0.9em",
                                      margin: "0px",
                                      whiteSpace: "nowrap",
                                      marginRight: "0px",
                                      marginLeft: "0px",
                                    }}
                                  >
                                    Edit
                                  </div>

                                  <div
                                    id="item_BTN__Delete"
                                    className="btn stdButton"
                                    style={{
                                      backgroundPosition: "left top",
                                      borderRight: "1px solid rgb(92, 93, 97)",
                                      borderBottom: "1px solid rgb(92, 93, 97)",
                                      borderTop: "none",
                                      borderLeft: "none",
                                      cursor: "pointer",
                                      display: "inline",
                                      fontFamily:
                                        "Arial, Helvetica, sans-serif",
                                      padding: "2px 3px",
                                      borderWidth: "1px",
                                      borderStyle: "solid",
                                      borderColor:
                                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                      borderImage: "initial",
                                      background:
                                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                      borderRadius: "3px",
                                      color: "rgb(51, 51, 51)",
                                      backgroundImage:
                                        'url("/assets/downloaded/btn_sprite.png")',
                                      backgroundRepeat: "repeat-x",
                                      fontWeight: "bold",
                                      fontSize: "0.9em",
                                      margin: "0px",
                                      whiteSpace: "nowrap",
                                      marginRight: "0px",
                                      marginLeft: "0px",
                                    }}
                                  >
                                    Delete
                                  </div>

                                  <div
                                    id="item_BTN__Clone"
                                    className="btn stdButton"
                                    style={{
                                      backgroundPosition: "left top",
                                      borderRight: "1px solid rgb(92, 93, 97)",
                                      borderBottom: "1px solid rgb(92, 93, 97)",
                                      borderTop: "none",
                                      borderLeft: "none",
                                      cursor: "pointer",
                                      display: "inline",
                                      fontFamily:
                                        "Arial, Helvetica, sans-serif",
                                      padding: "2px 3px",
                                      borderWidth: "1px",
                                      borderStyle: "solid",
                                      borderColor:
                                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                      borderImage: "initial",
                                      background:
                                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                      borderRadius: "3px",
                                      color: "rgb(51, 51, 51)",
                                      backgroundImage:
                                        'url("/assets/downloaded/btn_sprite.png")',
                                      backgroundRepeat: "repeat-x",
                                      fontWeight: "bold",
                                      fontSize: "0.9em",
                                      margin: "0px",
                                      whiteSpace: "nowrap",
                                      marginRight: "0px",
                                      marginLeft: "0px",
                                    }}
                                  >
                                    Clone
                                  </div>

                                  <div
                                    id="item_BTN__ChangeOwnerOne"
                                    className="btn stdButton"
                                    style={{
                                      backgroundPosition: "left top",
                                      borderRight: "1px solid rgb(92, 93, 97)",
                                      borderBottom: "1px solid rgb(92, 93, 97)",
                                      borderTop: "none",
                                      borderLeft: "none",
                                      cursor: "pointer",
                                      display: "inline",
                                      fontFamily:
                                        "Arial, Helvetica, sans-serif",
                                      padding: "2px 3px",
                                      borderWidth: "1px",
                                      borderStyle: "solid",
                                      borderColor:
                                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                      borderImage: "initial",
                                      background:
                                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                      borderRadius: "3px",
                                      color: "rgb(51, 51, 51)",
                                      backgroundImage:
                                        'url("/assets/downloaded/btn_sprite.png")',
                                      backgroundRepeat: "repeat-x",
                                      fontWeight: "bold",
                                      fontSize: "0.9em",
                                      margin: "0px",
                                      whiteSpace: "nowrap",
                                      marginRight: "0px",
                                      marginLeft: "0px",
                                    }}
                                  >
                                    Change Owner
                                  </div>

                                  <div
                                    id="item_BTN__ChangeRecordType"
                                    className="btn stdButton"
                                    style={{
                                      backgroundPosition: "left top",
                                      borderRight: "1px solid rgb(92, 93, 97)",
                                      borderBottom: "1px solid rgb(92, 93, 97)",
                                      borderTop: "none",
                                      borderLeft: "none",
                                      cursor: "pointer",
                                      display: "inline",
                                      fontFamily:
                                        "Arial, Helvetica, sans-serif",
                                      padding: "2px 3px",
                                      borderWidth: "1px",
                                      borderStyle: "solid",
                                      borderColor:
                                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                      borderImage: "initial",
                                      background:
                                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                      borderRadius: "3px",
                                      color: "rgb(51, 51, 51)",
                                      backgroundImage:
                                        'url("/assets/downloaded/btn_sprite.png")',
                                      backgroundRepeat: "repeat-x",
                                      fontWeight: "bold",
                                      fontSize: "0.9em",
                                      margin: "0px",
                                      whiteSpace: "nowrap",
                                      marginRight: "0px",
                                      marginLeft: "0px",
                                    }}
                                  >
                                    Change Record Type
                                  </div>

                                  <div
                                    id="item_BTN__PrintableView"
                                    className="btn stdButton"
                                    style={{
                                      backgroundPosition: "left top",
                                      borderRight: "1px solid rgb(92, 93, 97)",
                                      borderBottom: "1px solid rgb(92, 93, 97)",
                                      borderTop: "none",
                                      borderLeft: "none",
                                      cursor: "pointer",
                                      display: "inline",
                                      fontFamily:
                                        "Arial, Helvetica, sans-serif",
                                      padding: "2px 3px",
                                      borderWidth: "1px",
                                      borderStyle: "solid",
                                      borderColor:
                                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                      borderImage: "initial",
                                      background:
                                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                      borderRadius: "3px",
                                      color: "rgb(51, 51, 51)",
                                      backgroundImage:
                                        'url("/assets/downloaded/btn_sprite.png")',
                                      backgroundRepeat: "repeat-x",
                                      fontWeight: "bold",
                                      fontSize: "0.9em",
                                      margin: "0px",
                                      whiteSpace: "nowrap",
                                      marginRight: "0px",
                                      marginLeft: "0px",
                                    }}
                                  >
                                    Printable View
                                  </div>

                                  <div
                                    id="item_BTN__SendSurveyInvitation"
                                    className="btn stdButton"
                                    style={{
                                      backgroundPosition: "left top",
                                      borderRight: "1px solid rgb(92, 93, 97)",
                                      borderBottom: "1px solid rgb(92, 93, 97)",
                                      borderTop: "none",
                                      borderLeft: "none",
                                      cursor: "pointer",
                                      fontFamily:
                                        "Arial, Helvetica, sans-serif",
                                      padding: "2px 3px",
                                      borderWidth: "1px",
                                      borderStyle: "solid",
                                      borderColor:
                                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                      borderImage: "initial",
                                      background:
                                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                      borderRadius: "3px",
                                      color: "rgb(51, 51, 51)",
                                      backgroundImage:
                                        'url("/assets/downloaded/btn_sprite.png")',
                                      backgroundRepeat: "repeat-x",
                                      fontWeight: "bold",
                                      fontSize: "0.9em",
                                      margin: "0px",
                                      whiteSpace: "nowrap",
                                      marginRight: "0px",
                                      marginLeft: "0px",
                                      display: "none",
                                    }}
                                  >
                                    Send Survey
                                  </div>
                                  <div
                                    id="item_BTN__Share"
                                    className="btn stdButton"
                                    style={{
                                      backgroundPosition: "left top",
                                      borderRight: "1px solid rgb(92, 93, 97)",
                                      borderBottom: "1px solid rgb(92, 93, 97)",
                                      borderTop: "none",
                                      borderLeft: "none",
                                      cursor: "pointer",
                                      display: "inline",
                                      fontFamily:
                                        "Arial, Helvetica, sans-serif",
                                      padding: "2px 3px",
                                      borderWidth: "1px",
                                      borderStyle: "solid",
                                      borderColor:
                                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                      borderImage: "initial",
                                      background:
                                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                      borderRadius: "3px",
                                      color: "rgb(51, 51, 51)",
                                      backgroundImage:
                                        'url("/assets/downloaded/btn_sprite.png")',
                                      backgroundRepeat: "repeat-x",
                                      fontWeight: "bold",
                                      fontSize: "0.9em",
                                      margin: "0px",
                                      whiteSpace: "nowrap",
                                      marginRight: "0px",
                                      marginLeft: "0px",
                                    }}
                                  >
                                    Sharing
                                  </div>

                                  <div
                                    id="item_BTN__RecordShareHierarchy"
                                    className="btn stdButton"
                                    style={{
                                      backgroundPosition: "left top",
                                      borderRight: "1px solid rgb(92, 93, 97)",
                                      borderBottom: "1px solid rgb(92, 93, 97)",
                                      borderTop: "none",
                                      borderLeft: "none",
                                      cursor: "pointer",
                                      display: "inline",
                                      fontFamily:
                                        "Arial, Helvetica, sans-serif",
                                      padding: "2px 3px",
                                      borderWidth: "1px",
                                      borderStyle: "solid",
                                      borderColor:
                                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                      borderImage: "initial",
                                      background:
                                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                      borderRadius: "3px",
                                      color: "rgb(51, 51, 51)",
                                      backgroundImage:
                                        'url("/assets/downloaded/btn_sprite.png")',
                                      backgroundRepeat: "repeat-x",
                                      fontWeight: "bold",
                                      fontSize: "0.9em",
                                      margin: "0px",
                                      whiteSpace: "nowrap",
                                      marginRight: "0px",
                                      marginLeft: "0px",
                                    }}
                                  >
                                    Sharing Hierarchy
                                  </div>

                                  <div
                                    id="item_BTN__Submit"
                                    className="btn stdButton"
                                    style={{
                                      backgroundPosition: "left top",
                                      borderRight: "1px solid rgb(92, 93, 97)",
                                      borderBottom: "1px solid rgb(92, 93, 97)",
                                      borderTop: "none",
                                      borderLeft: "none",
                                      cursor: "pointer",
                                      fontFamily:
                                        "Arial, Helvetica, sans-serif",
                                      padding: "2px 3px",
                                      borderWidth: "1px",
                                      borderStyle: "solid",
                                      borderColor:
                                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                      borderImage: "initial",
                                      background:
                                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                      borderRadius: "3px",
                                      color: "rgb(51, 51, 51)",
                                      backgroundImage:
                                        'url("/assets/downloaded/btn_sprite.png")',
                                      backgroundRepeat: "repeat-x",
                                      fontWeight: "bold",
                                      fontSize: "0.9em",
                                      margin: "0px",
                                      whiteSpace: "nowrap",
                                      marginRight: "0px",
                                      marginLeft: "0px",
                                      display: "none",
                                    }}
                                  >
                                    Submit for Approval
                                  </div>
                                  <div
                                    id="item_BTN__AssignRecordLabel"
                                    className="btn stdButton"
                                    style={{
                                      backgroundPosition: "left top",
                                      borderRight: "1px solid rgb(92, 93, 97)",
                                      borderBottom: "1px solid rgb(92, 93, 97)",
                                      borderTop: "none",
                                      borderLeft: "none",
                                      cursor: "pointer",
                                      display: "inline",
                                      fontFamily:
                                        "Arial, Helvetica, sans-serif",
                                      padding: "2px 3px",
                                      borderWidth: "1px",
                                      borderStyle: "solid",
                                      borderColor:
                                        "rgb(181, 181, 181) rgb(181, 181, 181) rgb(127, 127, 127)",
                                      borderImage: "initial",
                                      background:
                                        'url("/assets/downloaded/btn_sprite.png") right top repeat-x rgb(232, 232, 233)',
                                      borderRadius: "3px",
                                      color: "rgb(51, 51, 51)",
                                      backgroundImage:
                                        'url("/assets/downloaded/btn_sprite.png")',
                                      backgroundRepeat: "repeat-x",
                                      fontWeight: "bold",
                                      fontSize: "0.9em",
                                      margin: "0px",
                                      whiteSpace: "nowrap",
                                      marginRight: "0px",
                                      marginLeft: "0px",
                                    }}
                                  >
                                    Edit Labels
                                  </div>
                                </fieldset>
                                <fieldset
                                  id="ext-gen135"
                                  className="customButtons cButtonDrop"
                                  style={{
                                    margin: "2px 2px 0px 0px",
                                    display: "inline",
                                    border: "1px dotted rgb(204, 204, 204)",
                                    padding: "0px 5px 3px",
                                    paddingLeft: "5px",
                                  }}
                                >
                                  <legend
                                    style={{
                                      textAlign: "left",
                                      fontSize: "91%",
                                      fontWeight: "normal",
                                      lineHeight: "10px",
                                      color: "rgb(153, 153, 153)",
                                    }}
                                  >
                                    Custom Buttons
                                  </legend>
                                  <div
                                    id="ext-gen136"
                                    className="firstSpace"
                                    style={{ margin: "0px", padding: "0px" }}
                                  >
                                    <span id="ext-gen137"> </span>
                                  </div>
                                </fieldset>
                              </div>
                              <div
                                id="ext-gen132"
                                className="buttonBar-footer"
                                style={{ margin: "0px", padding: "0px" }}
                              />
                            </div>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div
                  id="ext-gen95"
                  className="pbBody canvasDrop"
                  style={{
                    color: "rgb(0, 0, 0)",
                    margin: "0px",
                    backgroundColor: "transparent",
                    backgroundImage: "none",
                    marginRight: "0px",
                    padding: "0px 6px 6px",
                  }}
                >
                  <div
                    id="01BgL000004FOkT"
                    className="section fieldDrop"
                    style={{
                      margin: "0px",
                      padding: "0px",
                      paddingBottom: "3px",
                      border: "none",
                      marginTop: "10px",
                    }}
                  >
                    <div
                      id="ext-gen146"
                      className="section-header x-unselectable tertiaryPalette editOnly"
                      style={{
                        margin: "0px",
                        padding: "3px 2px 2px 6px",
                        fontWeight: "bold",
                        background:
                          'url("/assets/downloaded/dragaffordance.gif") 3px 3px no-repeat rgb(102, 102, 102)',
                        cursor: "move",
                        paddingLeft: "10px",
                        borderColor: "rgb(142, 157, 190)",
                        borderStyle: "solid dotted dotted",
                        borderWidth: "2px 0px 0px",
                        backgroundColor: "transparent",
                        borderLeftColor: "rgb(204, 204, 204)",
                        borderBottomColor: "rgb(204, 204, 204)",
                        borderRightColor: "rgb(204, 204, 204)",
                        borderTop: "2px solid rgb(204, 204, 204)",
                        color: "rgb(153, 153, 153)",
                      }}
                    >
                      <div
                        id="ext-gen150"
                        className="x-tool x-tool-gear"
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflow: "hidden",
                          background: "no-repeat transparent",
                          marginLeft: "0px",
                          visibility: "hidden",
                          height: "18px",
                          width: "20px",
                          cursor: "pointer",
                          cssFloat: "right",
                          marginTop: "-2px",
                          backgroundPosition: "2px 2px",
                          backgroundImage:
                            'url("/assets/downloaded/wrenchRemove.png")',
                          backgroundRepeat: "no-repeat",
                        }}
                      ></div>
                      <div
                        id="ext-gen149"
                        className="x-tool x-tool-remove"
                        style={{
                          margin: "0px",
                          padding: "0px",
                          overflow: "hidden",
                          background: "no-repeat transparent",
                          marginLeft: "0px",
                          visibility: "hidden",
                          height: "18px",
                          width: "20px",
                          cursor: "pointer",
                          cssFloat: "right",
                          marginTop: "-2px",
                          backgroundPosition: "2px -14px",
                          backgroundImage:
                            'url("/assets/downloaded/wrenchRemove.png")',
                          backgroundRepeat: "no-repeat",
                          display: "none",
                        }}
                      ></div>
                      <span id="ext-gen159" className="section-header-text">
                        Information
                        <span
                          className="headerInfo"
                          style={{
                            marginLeft: "0.5em",
                            marginBottom: "2px",
                            fontSize: "91%",
                            fontWeight: "bold",
                            color: "rgb(153, 153, 153)",
                          }}
                        >
                          (Header visible on edit only)
                        </span>
                      </span>
                    </div>
                    <div
                      id="ext-gen147"
                      className="section-bwrap"
                      style={{ margin: "0px", padding: "0px" }}
                    >
                      <div className="section-bwrap">
                        <div className="section-body">
                          <DragDropSalesforce sectionTitle="Information" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    id="01BgL000004FOkU"
                    className="section fieldDrop"
                    style={{
                      margin: "0px",
                      padding: "0px",
                      paddingBottom: "3px",
                      border: "none",
                      marginTop: "10px",
                    }}
                  >
                    <div
                      id="ext-gen160"
                      className="section-header x-unselectable tertiaryPalette editOnly"
                      style={{
                        margin: "0px",
                        padding: "3px 2px 2px 6px",
                        fontWeight: "bold",
                        background:
                          'url("/assets/downloaded/dragaffordance.gif") 3px 3px no-repeat rgb(102, 102, 102)',
                        cursor: "move",
                        paddingLeft: "10px",
                        borderColor: "rgb(142, 157, 190)",
                        borderStyle: "solid dotted dotted",
                        borderWidth: "2px 0px 0px",
                        backgroundColor: "transparent",
                        borderLeftColor: "rgb(204, 204, 204)",
                        borderBottomColor: "rgb(204, 204, 204)",
                        borderRightColor: "rgb(204, 204, 204)",
                        borderTop: "2px solid rgb(204, 204, 204)",
                        color: "rgb(153, 153, 153)",
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
