/* -----------------------------------------------------------------------
   custom-object/CustomObjectContainer.jsx
------------------------------------------------------------------------ */
import { useParams, useNavigate, useOutletContext } from "react-router-dom";
import { useObjectManager } from "../../../context/ObjectManagerContext";
import { getMostRecentCustomObject } from "./FieldsAndRelationships/hooks/getRecentCustomObject";

/* ✓ icon – keeps a fixed-width box so the column never shifts */
const CheckMark = ({ show = false }) => (
  <span
    style={{
      display: "inline-block",
      width: "0.875rem",
      height: "0.875rem",
      lineHeight: 1,
    }}
  >
    {show && (
      <svg
        viewBox="0 0 520 520"
        style={{ width: "100%", height: "100%", fill: "currentColor" }}
        aria-hidden
      >
        <path d="M191 425 26 259c-6-6-6-16 0-22l22-22c6-6 16-6 22 0l124 125a10 10 0 0 0 15 0L452 95c6-6 16-6 22 0l22 22c6 6 6 16 0 22L213 425c-6 7-16 7-22 0z" />
      </svg>
    )}
  </span>
);

/* merge wizard aliases + defaults */
const normalise = (raw = {}) => ({
  ...raw,
  SortAPIName: raw.SortAPIName ?? raw.DeveloperName ?? raw.apiName ?? "",
  DeveloperName: raw.DeveloperName ?? raw.SortAPIName ?? raw.apiName ?? "",
  SortLabelSortedAscending: raw.MasterLabel ?? raw.label ?? "",
  SortDescription: raw.SortDescription ?? raw.Description ?? "",
  PluralLabel: raw.PluralLabel ?? raw.pluralLabel ?? "",
  deploymentStatus: raw.deploymentStatus ?? "Deployed",
  helpSettings: raw.helpSettings ?? "Standard salesforce.com Help Window",
  /* flags coming from wizard check-boxes or seed rows */
  IsCustom: raw.IsCustom ?? raw.Type === "Custom Object",
  EnableReports:
    raw.EnableReports ?? raw.allowReports ?? raw.options_0 ?? false,
  TrackActivities:
    raw.TrackActivities ?? raw.allowActivities ?? raw.options_3 ?? false,
  TrackFieldHistory:
    raw.TrackFieldHistory ?? raw.trackFieldHistory ?? raw.options_6 ?? false,
});

export default function CustomObjectContainer() {
  const { apiName } = useParams();
  const { findObjectByApiName } = useObjectManager();
  const navigate = useNavigate();
  const { selectedObject } = useOutletContext() || {};

  // Try to find the object by context first, then by API name
  let foundObject = selectedObject || findObjectByApiName(apiName);

  // If not found, try to get the most recent object
  if (!foundObject) {
    const recentObject = getMostRecentCustomObject();

    // If we found a recent object and we're on the details page
    if (recentObject) {
      // If we're on the "custom-object" path, redirect to the specific object
      if (apiName === "custom-object") {
        setTimeout(() => {
          navigate(`/setup/objectManager/${recentObject.apiName}`);
        }, 0);

        return (
          <p style={{ padding: "1rem" }}>
            Redirecting to most recent object...
          </p>
        );
      }
      // Otherwise, use the most recent object directly
      else {
        // Get the full object data from the context
        const fullObjectData =
          window.allVariableData?.objectManager?.customObjectData;
        if (
          fullObjectData &&
          Array.isArray(fullObjectData) &&
          fullObjectData.length > 0
        ) {
          // Find the matching object by apiName
          const matchingObject = fullObjectData.find(
            (obj) =>
              obj.SortAPIName === recentObject.apiName ||
              obj.DeveloperName === recentObject.apiName
          );

          if (matchingObject) {
            foundObject = matchingObject;
          } else {
            foundObject = fullObjectData.slice(-1)[0]; // Use the most recent object
          }
        } else {
          // If we can't find the full object data, create a minimal object with the required properties
          foundObject = {
            SortAPIName: recentObject.apiName,
            SortLabelSortedAscending: recentObject.label,
            MasterLabel: recentObject.label,
            DeveloperName: recentObject.id,
          };
        }
      }
    }
  }

  // Check if the object was found before normalizing
  const rec = foundObject ? normalise(foundObject) : {};

  if (!rec.SortAPIName) {
    // Try one more time with the most recent object
    const recentObject = getMostRecentCustomObject();

    if (recentObject) {
      // Get the full object data from the context
      const fullObjectData =
        window.allVariableData?.objectManager?.customObjectData;
      if (
        fullObjectData &&
        Array.isArray(fullObjectData) &&
        fullObjectData.length > 0
      ) {
        // Find the matching object by apiName
        const matchingObject = fullObjectData.find(
          (obj) =>
            obj.SortAPIName === recentObject.apiName ||
            obj.DeveloperName === recentObject.apiName
        );

        if (matchingObject) {
          foundObject = matchingObject;
        } else {
          // Sort by SortLastModified in descending order (newest first)

          foundObject = fullObjectData.slice(-1)[0]; // Use the most recent object
        }
      } else {
        // If we can't find the full object data, create a minimal object with the required properties
        foundObject = {
          SortAPIName: recentObject.apiName,
          SortLabelSortedAscending: recentObject.label,
          MasterLabel: recentObject.label,
          DeveloperName: recentObject.id,
          // Add other required properties with default values
          SortDescription: "",
          PluralLabel: recentObject.label + "s",
          deploymentStatus: "Deployed",
          IsCustom: true,
        };
      }

      // Update rec with the normalized recent object
      Object.assign(rec, normalise(foundObject));
    } else {
      // If no object found at all, show the message
      return (
        <p style={{ padding: "1rem" }}>
          No object found. Please create a custom object first.
        </p>
      );
    }
  }

  // Extract properties from rec, using SortAPIName as fallback for DeveloperName
  const {
    SortAPIName,
    DeveloperName,
    MasterLabel: singular,
    PluralLabel: plural,
    Description: description,
    SortDescription,
    deploymentStatus,
    IsCustom,
    EnableReports,
    TrackActivities,
    TrackFieldHistory,
    helpSettings,
  } = rec;

  // Use fallbacks for any missing properties
  const api = DeveloperName || SortAPIName;
  const desc = description || SortDescription || "";
  const deployStatus = deploymentStatus || "Deployed";
  const helpSettingsText = helpSettings || "Standard salesforce.com Help Window";

  /* ---------- rendered HTML: styles & layout UNCHANGED ---------- */
  return (
    <>
      <div
        className="slds-container--fluid object-manager-content-wrapper"
        style={{ boxSizing: "border-box", width: "100%", position: "relative" }}
      >
        <div
          className="object-manager-content-position-wrapper"
          style={{ boxSizing: "border-box", inset: 0, position: "absolute" }}
        >
          <div
            id="setupComponent"
            className="component onesetupSetupComponent objectManagerObjectDetailComponent objectManagerObjectDetail"
            style={{
              flex: "1 1 auto",
              display: "flex",
              flexDirection: "column",
              overflow: "auto",
              background: "var(--lwc-colorBackgroundAlt,#fff)",
            }}
          >
            {/* ---------- HEADER ---------- */}
            <div
              className="object-detail-header slds-grid"
              style={{
                display: "flex",
                padding: "var(--lwc-spacingMedium,1rem)",
              }}
            >
              <div className="slds-col" style={{ flex: "1 1 auto" }}>
                <h2
                  className="slds-page-header__title"
                  style={{
                    margin: 0,
                    fontSize: "var(--lwc-pageHeaderTitleFontSize,1.125rem)",
                    fontWeight: "var(--lwc-pageHeaderTitleFontWeight,700)",
                  }}
                >
                  Details
                </h2>
              </div>
              <div className="slds-col slds-no-flex slds-grid slds-align-top">
                <div className="slds-button-group" role="group">
                  <button className="slds-button slds-button_neutral">
                    Edit
                  </button>
                  <button className="slds-button slds-button_neutral">
                    Delete
                  </button>
                </div>
              </div>
            </div>

            {/* ---------- DESCRIPTION PANEL ---------- */}
            <div
              className="setupcontent"
              style={{ flex: "1 1 auto", display: "flex" }}
            >
              <div
                className="object-container"
                style={{ flex: "1 1 auto", display: "flex" }}
              >
                <div
                  className="object-content"
                  style={{ flex: "1 1 auto", overflow: "auto" }}
                >
                  {/* description panel */}
                  <div
                    className="slds-panel slds-grid slds-grid--vertical slds-nowrap"
                    style={{
                      background: "var(--lwc-colorBackgroundAlt,#fff)",
                      borderRadius: "var(--lwc-borderRadiusMedium,0.25rem)",
                      flex: "1 1 auto",
                    }}
                  >
                    <div
                      className="slds-panel__section"
                      style={{ padding: "1rem" }}
                    >
                      <ul style={{ listStyle: "none", margin: 0, padding: 0 }}>
                        <li className="slds-form-element slds-hint-parent">
                          <span className="slds-form-element__label">
                            Description
                          </span>
                          <div className="slds-form-element__control">
                            <span className="slds-form-element__static">
                              {desc}
                            </span>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>

                  {/* ---------- TWO COLUMNS ---------- */}
                  <div style={{ display: "flex" }}>
                    {/* LEFT COLUMN */}
                    <div style={{ width: "50%" }}>
                      <div
                        className="slds-panel"
                        style={{
                          background: "var(--lwc-colorBackgroundAlt,#fff)",
                          borderRadius: "0.25rem",
                        }}
                      >
                        <div
                          className="slds-panel__section"
                          style={{ padding: "1rem" }}
                        >
                          <ul
                            style={{ listStyle: "none", margin: 0, padding: 0 }}
                          >
                            <li className="slds-form-element slds-has-divider--top slds-has-divider--bottom">
                              <span className="slds-form-element__label">
                                API Name
                              </span>
                              <div className="slds-form-element__control">
                                <span className="slds-form-element__static">
                                  {api}
                                </span>
                              </div>
                            </li>
                            <li className="slds-form-element slds-has-divider--bottom">
                              <span className="slds-form-element__label">
                                Custom
                              </span>
                              <div className="slds-form-element__control">
                                <CheckMark show={IsCustom} />
                              </div>
                            </li>
                            <li className="slds-form-element slds-has-divider--bottom">
                              <span className="slds-form-element__label">
                                Singular Label
                              </span>
                              <div className="slds-form-element__control">
                                <span className="slds-form-element__static">
                                  {singular}
                                </span>
                              </div>
                            </li>
                            <li className="slds-form-element slds-hint-parent slds-has-divider--bottom">
                              <span className="slds-form-element__label">
                                Plural Label
                              </span>
                              <div className="slds-form-element__control">
                                <span className="slds-form-element__static">
                                  {plural}
                                </span>
                              </div>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* RIGHT COLUMN */}
                    <div style={{ width: "50%" }}>
                      <div
                        className="slds-panel"
                        style={{
                          background: "var(--lwc-colorBackgroundAlt,#fff)",
                          borderRadius: "0.25rem",
                        }}
                      >
                        <div
                          className="slds-panel__section"
                          style={{ padding: "1rem" }}
                        >
                          <ul
                            style={{ listStyle: "none", margin: 0, padding: 0 }}
                          >
                            <li className="slds-form-element slds-has-divider--top slds-has-divider--bottom">
                              <span className="slds-form-element__label">
                                Enable Reports
                              </span>
                              <div className="slds-form-element__control">
                                <CheckMark show={EnableReports} />
                              </div>
                            </li>
                            <li className="slds-form-element slds-has-divider--bottom">
                              <span className="slds-form-element__label">
                                Track Activities
                              </span>
                              <div className="slds-form-element__control">
                                <CheckMark show={TrackActivities} />
                              </div>
                            </li>
                            <li className="slds-form-element slds-has-divider--bottom">
                              <span className="slds-form-element__label">
                                Track Field History
                              </span>
                              <div className="slds-form-element__control">
                                <CheckMark show={TrackFieldHistory} />
                              </div>
                            </li>
                            <li className="slds-form-element slds-has-divider--bottom">
                              <span className="slds-form-element__label">
                                Deployment Status
                              </span>
                              <div className="slds-form-element__control">
                                <span className="slds-form-element__static">
                                  {deployStatus}
                                </span>
                              </div>
                            </li>
                            <li className="slds-form-element slds-has-divider--bottom">
                              <span className="slds-form-element__label">
                                Help Settings
                              </span>
                              <div className="slds-form-element__control">
                                <span className="slds-form-element__static">
                                  {helpSettingsText}
                                </span>
                              </div>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* end flex */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Baseline fonts / colours (unchanged) */}
      <style>{`
          html{box-sizing:border-box;background:var(--lwc-brandBackgroundPrimary,rgba(176,196,223,1));font-family:var(--lwc-fontFamily,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif);}
          body{margin:0;background:transparent;font-size:var(--lwc-fontSize3,0.8125rem);}
        `}</style>
    </>
  );
}
