import React from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";

/* Helper function to get the most recent custom object */
const getMostRecentCustomObject = () => {
  const windowData = window.allVariableData?.objectManager?.customObjectData;

  if (windowData && Array.isArray(windowData) && windowData.length > 0) {
    // Sort by SortLastModified in descending order (newest first)
    const sortedObjects = [...windowData].sort((a, b) => {
      const dateA = new Date(a.SortLastModified || 0);
      const dateB = new Date(b.SortLastModified || 0);
      return dateB - dateA;
    });

    return sortedObjects[0];
  }

  return null;
};

/* ------------------------------------------------------------------ */
/* grab the :apiName segment once so every list item shares the same  */
/*                                                                 -- */
const useBasePath = () => {
  const { apiName } = useParams(); // <Route path=":apiName/*" …>

  // If apiName is "custom-object", try to get the most recent custom object
  if (apiName === "custom-object") {
    const recentObject = getMostRecentCustomObject();
    if (recentObject && recentObject.SortAPIName) {
      return `/setup/objectManager/${recentObject.SortAPIName}`;
    }
  }

  // Check if this is one of our opportunity objects
  const isOpportunityObject = apiName === "Opportunity" ||
                             apiName === "OpportunityContactRole" ||
                             apiName === "OpportunityLineItem";

  return `/setup/objectManager/${apiName || "custom-object"}`;
};

/* plain CSS constants (updated to match existing variable names) --- */
const baseLiStyle = { position: "relative", display: "block" };
const baseAStyle = {
  background: "transparent",
  textDecoration: "none",
  padding: "0.5rem 1.5rem 0.5rem 2rem",
  display: "flex",
  alignItems: "center",
  width: "100%",
  color: "var(--lwc-colorTextDefault,rgb(24,24,24))",
  cursor: "pointer",
};
const activeBoxShadow =
  "inset 4px 0 0 var(--lwc-brandPrimary,rgba(65,148,249,1))";
const inactiveBoxShadow =
  "inset 0 0 0 var(--lwc-brandPrimary,rgba(65,148,249,1))";

/* build the navigation table using the basePath and object type -------------------- */
const makeNavItems = (base, apiName) => {
  // Common items for all object types
  const commonItems = [
    { label: "Details", path: `${base}` },
    {
      label: "Fields & Relationships",
      path: `${base}/FieldsAndRelationships`,
      fuzzy: true,
    },
    { label: "Page Layouts", path: `${base}/page-layouts`, fuzzy: true },
    { label: "Lightning Record Pages", path: `${base}/LightningRecordPages` },
    { label: "Compact Layouts", path: `${base}/CompactLayouts` },
    { label: "Field Sets", path: `${base}/FieldSets` },
    { label: "Object Limits", path: `${base}/ObjectLimits` },
    { label: "Triggers", path: `${base}/Triggers` },
    { label: "Validation Rules", path: `${base}/ValidationRules` },
  ];

  // Opportunity-specific items
  if (apiName === "Opportunity") {
    return [
      ...commonItems,
      { label: "Record Types", path: `${base}/RecordTypes` },
      { label: "Related Lookup Filters", path: `${base}/RelatedLookupFilters` },
      { label: "Flow Triggers", path: `${base}/FlowTriggers` },
    ];
  }

  // Opportunity Contact Role-specific items
  if (apiName === "OpportunityContactRole") {
    return [
      ...commonItems,
      { label: "Buttons, Links, and Actions", path: `${base}/ButtonsLinksActions` },
    ];
  }

  // Opportunity Product-specific items
  if (apiName === "OpportunityLineItem") {
    return [
      ...commonItems,
      { label: "Buttons, Links, and Actions", path: `${base}/ButtonsLinksActions` },
      { label: "Related Lookup Filters", path: `${base}/RelatedLookupFilters` },
    ];
  }

  // Default items for other object types
  return [
    ...commonItems,
    { label: "Buttons, Links, and Actions", path: `${base}/ButtonsLinksActions` },
    { label: "Record Types", path: `${base}/RecordTypes` },
    { label: "Related Lookup Filters", path: `${base}/RelatedLookupFilters` },
    { label: "Restriction Rules", path: `${base}/RestrictionRules` },
    { label: "Scoping Rules", path: `${base}/ScopingRules` },
    { label: "Object Access", path: `${base}/ObjectAccess` },
    { label: "Flow Triggers", path: `${base}/FlowTriggers` },
    { label: "URL Slugs", path: `${base}/UrlSlugs` },
    {
      label: "Conditional Field Formatting",
      path: `${base}/ConditionalFormatting`,
    },
  ];
};

export default function CustomObjectSideBar({ selectedObject }) {
  const navigate = useNavigate();
  const location = useLocation();
  const { apiName } = useParams();
  const basePath = useBasePath();

  // Get the current object's API name, either from props, params, or default
  const currentApiName = selectedObject?.SortAPIName || apiName;

  // Generate navigation items based on the current object type
  const navItems = makeNavItems(basePath, currentApiName);

  // Handle navigation with special case for Details
  const handleNavigation = (e, path, label) => {
    e.preventDefault();

    // Special handling for Details link when on the generic "custom-object" path
    if (label === "Details" && apiName === "custom-object") {
      const recentObject = getMostRecentCustomObject();

      if (recentObject && recentObject.apiName) {
        // Navigate to the most recent object's details page
        navigate(`/setup/objectManager/${recentObject.apiName}`);
        return;
      }
    }

    // Default navigation
    navigate(path);
  };

  return (
    <div
      className="slds-grid slds-grid--vertical objectManagerLeftNav"
      style={{
        width: "15rem",
        borderRight: "1px solid var(--lwc-colorBorder,rgb(229,229,229))",
        overflowY: "auto",
        background: "var(--lwc-colorBackgroundAlt,#fff)",
      }}
    >
      <ul
        style={{ margin: 0, padding: 0, listStyle: "none", marginTop: "1rem" }}
      >
        {navItems.map(({ label, path, fuzzy }) => {
          // Determine if this item is active based on current location
          let isActive = false;

          if (label === "Details") {
            // Details is active when on the base path or view page, but NOT on sub-pages
            isActive = location.pathname === path ||
                      location.pathname === basePath ||
                      location.pathname.endsWith("/view");
          } else if (fuzzy) {
            // For fuzzy matching items, check if current path starts with the item path
            isActive = location.pathname.startsWith(path);
          } else {
            // For exact matching items
            isActive = location.pathname === path;
          }

          // Special case: If we're on a page-layouts sub-path, only Page Layouts should be active
          if (location.pathname.includes("/page-layouts")) {
            isActive = label === "Page Layouts";
          }

          // Special case: If we're on a FieldsAndRelationships sub-path, only that should be active
          if (location.pathname.includes("/FieldsAndRelationships")) {
            isActive = label === "Fields & Relationships";
          }

          return (
            <li key={path} style={baseLiStyle}>
              <a
                href={path}
                role="tab"
                aria-selected={isActive}
                onClick={(e) => handleNavigation(e, path, label)}
                style={{
                  ...baseAStyle,
                  fontWeight: isActive ? "bold" : "normal",
                  boxShadow: isActive ? activeBoxShadow : inactiveBoxShadow,
                  backgroundColor: isActive ? "#EDF4FF" : "transparent",
                }}
              >
                {label}
              </a>
            </li>
          );
        })}
      </ul>
    </div>
  );
}
