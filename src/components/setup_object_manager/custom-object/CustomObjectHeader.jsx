/* -----------------------------------------------------------------------
   CustomObjectHeader.jsx
   • Shows the "Object Manager › {Object Label}" header.
   • Pulls the current object from outlet context or deep-link
     (/setup/objectManager/:apiName) so the label is always correct.
   • If no specific object is selected, shows the most recent custom object
     from allVariableData.objectManager.customObjectData.
------------------------------------------------------------------------ */
import React from "react";
import { useOutletContext, useParams, useLocation } from "react-router-dom";
import { useObjectManager } from "../../../context/ObjectManagerContext";

const IMAGE_ROOT = "/assets/images";




/* helper: get the active object record */
const useCurrentObject = () => {
  const { selectedObject: contextObject } = useOutletContext() ?? {};
  const { apiName: paramApiName } = useParams();
  const location = useLocation();
  const { findObjectByApiName } = useObjectManager();

  // --- Prioritize finding the object ---
  // 1. Use context if available (most reliable when inside the main layout)
  if (contextObject && contextObject.MasterLabel) {
    // console.log("[useCurrentObject] Using contextObject:", contextObject);
    return contextObject;
  }

  // 2. Try URL Parameter (fallback for direct access or if context is missing)
  if (paramApiName) {
    const objFromParam = findObjectByApiName(paramApiName);
    if (objFromParam) {
      // console.log("[useCurrentObject] Using object from URL param:", objFromParam);
      return objFromParam;
    }
  }

  // 3. Fallback: use location.state.apiName
  const stateApiName = location.state?.apiName;
  if (stateApiName) {
    const objFromState = findObjectByApiName(stateApiName);
    if (objFromState) return objFromState;
  }

  // 4. Try to get the most recent custom object
  const mostRecentObject = getMostRecentCustomObject();
  if (mostRecentObject) {
    return mostRecentObject;
  }

  // 5. Default fallback
  // console.log("[useCurrentObject] Falling back to default empty object.");
  return {}; // Return empty object if nothing found
};

export default function CustomObjectHeader() {
  const obj = useCurrentObject();

  /* fall-backs keep UI usable even while data is loading */
  const label =
    obj.SortLabelSortedAscending ||
    obj.MasterLabel ||
    obj.SortAPIName ||
    "Object";

  return (
    <>
      <div
        className="slds-page-header branding-setup onesetupSetupHeader"
        style={{
          padding: "1rem",
          border: "1px solid var(--lwc-pageHeaderColorBorder,rgb(201,201,201))",
          borderRadius: "0.25rem",
          boxShadow: "0 2px 2px 0 rgba(0,0,0,0.1)",
          marginBottom: "0.75rem",
        }}
      >
        <div style={{ overflow: "visible" }}>
          {/* -------- breadcrumbs + label -------- */}
          <div style={{ float: "left" }}>
            <div style={{ display: "inline-block", paddingRight: "0.5rem" }}>
              {/* (icon kept invisible exactly like original) */}
              <img
                src={`${IMAGE_ROOT}/gifs/s.gif`}
                alt=""
                style={{ display: "none", width: 32, height: 32 }}
              />
            </div>

            <div style={{ display: "inline-block", paddingRight: "0.5rem" }}>
              <div>
                <a
                  className="breadcrumb"
                  href="#"
                  style={{
                    textDecoration: "none",
                    fontSize: "0.75rem",
                    textTransform: "uppercase",
                    color: "var(--lwc-setupHeaderColorTextLink,#2574A9)",
                  }}
                >
                  Setup
                </a>
                <span style={{ padding: "0 0.2rem" }}>&gt;</span>
                <a
                  className="breadcrumb"
                  href="/setup/objectManager/"
                  style={{
                    textDecoration: "none",
                    fontSize: "0.75rem",
                    textTransform: "uppercase",
                    color: "var(--lwc-setupHeaderColorTextLink,#2574A9)",
                  }}
                >
                  Object Manager
                </a>
              </div>

              <h1 style={{ margin: 0 }}>
                <span
                  style={{
                    fontWeight: 700,
                    fontSize: "1.25rem",
                    color: "var(--lwc-colorTextDefault,rgb(24,24,24))",
                  }}
                >
                  {label}
                </span>
              </h1>
            </div>
          </div>

          {/* -------- help link (right) -------- */}
          <div style={{ float: "right", whiteSpace: "nowrap" }}>
            <a
              href="#"
              title="Help for this Page (New Window)"
              style={{ color: "rgb(1,91,167)", textDecoration: "none" }}
            >
              <span style={{ paddingRight: 5 }}>Help for this Page</span>
              <img
                src={`${IMAGE_ROOT}/gifs/s.gif`}
                alt=""
                style={{
                  width: 16,
                  height: 16,
                  background: `url(${IMAGE_ROOT}/help_orange.png) no-repeat`,
                  verticalAlign: "bottom",
                }}
              />
            </a>
          </div>
        </div>
      </div>

      {/* global base styles (unchanged) */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
html{box-sizing:border-box;background:var(--lwc-brandBackgroundPrimary,rgba(176,196,223,1));font-family:var(--lwc-fontFamily,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif);}
body{margin:0;background:transparent;font-size:0.8125rem;}`,
        }}
      />
    </>
  );
}
