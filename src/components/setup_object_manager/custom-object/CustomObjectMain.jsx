import React, { useContext, useEffect, useState } from "react";
import { GlobalContext } from "../../../context/GlobalContext.jsx";
import CustomObjectHeader from "./CustomObjectHeader.jsx";
import CustomObjectSideBar from "./CustomObjectSideBar.jsx";
import { Outlet, useParams, useLocation } from "react-router-dom";
import { useObjectManager } from "../../../context/ObjectManagerContext.jsx";

function SideBarPlaceholder() {
  return (
    <div
      style={{
        width: "var(--lwc-sizeSmall, 15rem)", // Match CustomObjectSideBar width
        height: "80vh", // Fill grid cell height
        backgroundColor: "var(--lwc-colorBackgroundAlt, rgb(255, 255, 255))", // Match sidebar bg
        borderRight:
          "var(--lwc-borderWidthThin, 2px) solid var(--lwc-colorBorder, rgb(229, 229, 229))", // Match border
        boxSizing: "border-box",
        // Optional: Add a subtle loading animation or visual cue if desired
        // opacity: 0.7,
      }}
    >
      {/* You could add shimmering effects here */}
    </div>
  );
}
function OutletPlaceholder() {
  return (
    <div
      style={{
        width: "100%",
        height: "100%",
        backgroundColor: "var(--lwc-colorBackgroundAlt, rgb(255, 255, 255))", // Often white background
        boxSizing: "border-box",
        padding: "var(--lwc-varSpacingMedium, 1rem)", // Add some padding like content might have
        // Optional: Add a subtle loading animation or visual cue
        // opacity: 0.7,
      }}
    >
      {/* You could add skeleton loaders here */}
    </div>
  );
}

export default function CustomObjectMain() {
  const { setIsSidebarVisible } = useContext(GlobalContext);
  const [isLoading, setIsLoading] = useState(true); // Loading state
  const { apiName: paramApiName } = useParams(); // Get apiName from URL params
  const location = useLocation(); // <-- Get location object
  const { findObjectByApiName } = useObjectManager();
  const [currentObject, setCurrentObject] = useState(null);

  useEffect(() => {
    setIsSidebarVisible(false);

    // Determine object data: Prioritize location state object data, then find by API name
    const effectiveApiName = location.state?.apiName || paramApiName;

    // If we have object data in the location state, use it directly
    if (location.state?.objectData) {
      setCurrentObject(location.state.objectData);
      setIsLoading(false);
    }
    // Otherwise, find the object using the API name
    else if (effectiveApiName) {
      const foundObject = findObjectByApiName(effectiveApiName);
      setCurrentObject(foundObject);

      // Short loading delay for UI feedback
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 800);

      // Cleanup timer on unmount
      return () => clearTimeout(timer);
    }
    // If no API name is provided, set loading to false
    else {
      setIsLoading(false);
    }
  }, [
    setIsSidebarVisible,
    paramApiName,
    location.state,
    findObjectByApiName,
  ]);

  // TODO: Fetch actual object data based on apiName if not already available

  return (
    <div
      className="tabsetBody main-content mainContentMark fullheight active fullRight isSetupApp splitViewDisabled"
      tabIndex="-1"
      style={{
        boxSizing: "border-box",
        overflow: "hidden", // Prevent outer scrolling
        position: "fixed",
        left: "0px",
        right: "0px",
        top: "5.6rem",
        bottom: "5.6rem",
        height: "100%",
        display: "flex",
      }}
    >
      <div
        className="split-right"
        role="main"
        style={{
          boxSizing: "border-box",
          height: "100%",
          position: "relative",
          left: "0px",
          width: "100%",
        }}
      >
        <div
          className="windowViewMode-maximized active lafPageHost"
          style={{
            boxSizing: "border-box",
            overflow: "hidden", // Prevent scrolling from the outer container
            height: "100%",
            width: "100%",
          }}
        >
          <div
            id="brandBand_4"
            className="slds-brand-band slds-brand-band_cover slds-brand-band_medium slds-template_default forceBrandBand"
          >
            <div
              className="slds-template__container"
              style={{ marginBottom: "5.6rem" }}
            >
              <div
                id="setupComponent"
                className="component onesetupSetupComponent objectManagerObjectList"
                style={{
                  height: "100%",
                  position: "absolute",
                  width: "100%",
                  flex: "1 1 auto",
                  overflow: "auto",
                  minHeight: "100%",
                  flexFlow: "column",
                  display: "flex",
                  alignItems: "stretch",
                }}
              >
                <CustomObjectHeader />
                <div
                  className="setupcontent"
                  style={{
                    boxSizing: "border-box",
                    flex: "1 1 auto",
                    position: "relative",
                    zIndex: 0,
                    overflowY: "auto", // Enable scrolling only here
                  }}
                >
                  <div
                    className="scrollerWrapper slds-card"
                    style={{
                      boxSizing: "border-box",
                      position: "absolute",
                      top: "0px",
                      bottom: "0px",
                      width: "100%",
                      backgroundColor:
                        "var(--lwc-colorBackgroundAlt, rgb(255, 255, 255))",
                      zIndex: 0,
                    }}
                  >
                    <div
                      className="scroller uiScroller scroller-wrapper scroll-bidirectional native"
                      tabIndex="-1"
                      style={{
                        boxSizing: "border-box",
                        position: "relative",
                        height: "100%",
                        transform: "translateZ(0px)",
                        overflow: "auto",
                        width: "100%",
                      }}
                    >
                      <div
                        className="scroller"
                        style={{
                          display: "grid",
                          gridTemplateColumns: "250px 1fr",
                          width: "100%",
                        }}
                      >
                        {/* Sidebar Area: Pass currentObject */}
                        {isLoading ? (
                          <SideBarPlaceholder />
                        ) : (
                          <CustomObjectSideBar selectedObject={currentObject} />
                        )}
                        {/* Outlet Area: Pass currentObject via context */}
                        {isLoading ? (
                          <OutletPlaceholder />
                        ) : (
                          <Outlet context={{ selectedObject: currentObject }} />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
