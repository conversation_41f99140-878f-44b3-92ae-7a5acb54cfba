import React, { useState, useEffect, useContext } from "react";
import { Link } from "react-router-dom";
import { GlobalContext } from "../../../context/GlobalContext";

export default function CustomObjectTable({ initialCustomFields = [] }) {
  const { allVariableData, saveToLocalStorage } = useContext(GlobalContext);

  // State to store all fields (system + custom)
  const [customFields, setCustomFields] = useState(initialCustomFields);

  // Load fields from global state on mount and handle initialCustomFields
  useEffect(() => {
    const globalFields = allVariableData?.objectManager?.fieldsAndRelationships;
    const tempField = allVariableData?.objectManager?.currentFieldData;
    const isEditingField = allVariableData?.objectManager?.isEditingField;

    let fieldsToDisplay = [];

    // Prioritize global state if it has fields
    if (globalFields && globalFields.length > 0) {
      fieldsToDisplay = [...globalFields]; // Use array as is, maintaining original order
    }
    // Otherwise, if initialCustomFields are provided, use them (and update global state if it was empty)
    else if (initialCustomFields && initialCustomFields.length > 0) {
      fieldsToDisplay = [...initialCustomFields]; // Use array as is, maintaining original order

      // Update global state if it was previously empty
      if (!globalFields || globalFields.length === 0) {
        const updatedObjectManager = {
          ...allVariableData.objectManager,
          fieldsAndRelationships: initialCustomFields,
        };
        saveToLocalStorage("objectManager", updatedObjectManager);
      }
    }

    // Handle the temporary field being edited
    if (tempField && isEditingField) {
      const finalFieldAlreadyExists = fieldsToDisplay.some(
        (field) =>
          !field.isTemporary &&
          field.fieldName === tempField.fieldName &&
          field.fieldName !== "" &&
          field.dataType === tempField.dataType
      );

      if (!finalFieldAlreadyExists) {
        // Remove any existing temporary fields
        fieldsToDisplay = fieldsToDisplay.filter((field) => !field.isTemporary);

        // Add the temporary field to the beginning so it appears at the top
        fieldsToDisplay = [tempField, ...fieldsToDisplay];
      }
    }

    // Set the fields directly without any sorting or reversing
    setCustomFields(fieldsToDisplay);
  }, [allVariableData, initialCustomFields, saveToLocalStorage]);

  // Function to add a new field (could be called from a parent component)
  const addToPageLayout = (fieldData) => {
    const newField = {
      fieldLabel: fieldData.fieldLabel,
      fieldName: fieldData.fieldName,
      dataType: fieldData.dataType,
      description: fieldData.description || "",
      // Picklist metadata
      picklistType: fieldData.picklistType || "",
      picklistValues: fieldData.picklistValues || [],
      // Text metadata
      length: fieldData.length,
      isRequired: fieldData.isRequired,
      isUnique: fieldData.isUnique,
      isCaseSensitive: fieldData.isCaseSensitive,
      isExternalId: fieldData.isExternalId,
      controllingField: "",
      indexed: false,
      // Number-specific data
      decimals: fieldData.decimals,
      // Field-level security data
      // profileSettings: fieldData.profileSettings || {},
      fieldLevelSecurity:
        fieldData.fieldLevelSecurity ||
        (fieldData.profileSettings
          ? Object.entries(fieldData.profileSettings).map(
              ([pid, settings]) => ({
                profileName: pid.includes("00egL")
                  ? `Profile ${pid.substring(0, 8)}`
                  : "Unknown Profile",
                visible: settings.visible,
                readOnly: settings.readOnly,
              })
            )
          : []),
      // Link this field to its owner (custom object)
      customObject: fieldData.customObject || null,
    };

    // Update local state
    setCustomFields((prev) => {
      // Check if this field already exists by fieldName
      const fieldExists = prev.some(
        (field) =>
          field.fieldName === newField.fieldName &&
          field.fieldName !== "" &&
          field.dataType === newField.dataType
      );

      // Only add if it doesn't already exist
      // Add new field at the beginning of the array so newest fields appear at the top
      return fieldExists ? prev : [newField, ...prev];
    });

    // Update global state
    const existingFields =
      allVariableData.objectManager.fieldsAndRelationships || [];

    // Check if this field already exists in global state
    const fieldExists = existingFields.some(
      (field) =>
        field.fieldName === newField.fieldName &&
        field.fieldName !== "" &&
        field.dataType === newField.dataType
    );

    // Only add if it doesn't already exist
    // Add new field at the beginning of the array so newest fields appear at the top
    const updatedFields = fieldExists
      ? existingFields
      : [newField, ...existingFields];

    const updatedObjectManager = {
      ...allVariableData.objectManager,
      fieldsAndRelationships: updatedFields,
    };

    // Save to localStorage and update global state
    saveToLocalStorage("objectManager", updatedObjectManager);
  };

  // Make addToPageLayout available globally for form submissions
  // This is a simple way to connect the form to the table without props
  if (typeof window !== "undefined") {
    window.addFieldToTable = addToPageLayout;
    window.getCustomFieldsLength = () => customFields.length;
  }
  return (
    <>
      <section
        className="related-list-card"
        style={{
          boxSizing: "border-box",
          display: "block",
          margin: "0px",
          flexFlow: "column",
          inset: "71px 0px 0px",
          backgroundColor: "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
          position: "absolute",
        }}
      >
        <div
          className="related-list-card-content"
          style={{ boxSizing: "border-box", height: "100%" }}
        >
          <div
            className="spinnerWrapper forceComponentSpinner hideSpinner hideEl"
            style={{
              boxSizing: "border-box",
              inset: "0px",
              backgroundColor:
                "var(--lwc-colorBackgroundTempModalTintAlt,rgba(255, 255, 255, 0.75))",
              justifyContent: "center",
              alignItems: "center",
              opacity: 0,
              transitionProperty: "",
              transitionDuration: "",
              transitionTimingFunction: "",
              transitionBehavior: "",
              transitionDelay: "0s, 0s",
              visibility: "hidden",
              zIndex: 4,
              position: "fixed",
              display: "none",
            }}
          >
            <div
              className="indicator hideEl forceInlineSpinner"
              aria-hidden="true"
              role="alert"
              style={{
                boxSizing: "border-box",
                textAlign: "center",
                display: "none",
              }}
            />
          </div>
          <div
            className="scrollerWrapper"
            style={{ boxSizing: "border-box", zIndex: 0, height: "100%" }}
          >
            <div
              className="scroller uiScroller scroller-wrapper scroll-bidirectional native"
              tabIndex="-1"
              style={{
                boxSizing: "border-box",
                position: "relative",
                height: "100%",
                transform: "translateZ(0px)",
                overflow: "auto",
                width: "100%",
              }}
            >
              <div
                className="scroller"
                style={{
                  boxSizing: "border-box",
                  position: "absolute",
                  WebkitTapHighlightColor:
                    "var(--slds-g-color-neutral-base-10, rgba(0,0,0,0))",
                  transform: "translateZ(0px)",
                  textSizeAdjust: "none",
                  width: "100%",
                  userSelect: "text",
                }}
              >
                <table
                  className="slds-table slds-table-bordered uiVirtualDataGrid--default uiVirtualDataGrid"
                  style={{
                    boxSizing: "border-box",
                    borderSpacing: "0px",
                    width: "100%",
                    backgroundColor:
                      "var(--slds-g-color-neutral-base-100, var(--lwc-colorBackgroundAlt,rgb(255, 255, 255)))",
                    fontSize: "inherit",
                    borderCollapse: "inherit",
                  }}
                >
                  <thead
                    style={{ boxSizing: "border-box", verticalAlign: "middle" }}
                  >
                    <tr
                      key="header-row"
                      style={{
                        boxSizing: "border-box",
                        verticalAlign: "middle",
                      }}
                    >
                      <th
                        className="slds-text-heading--label standard-col slds-truncate"
                        aria-label="Field Label"
                        scope="col"
                        title="Field Label"
                        style={{
                          boxSizing: "border-box",
                          textAlign: "left",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          fontSize: "var(--lwc-fontSize2,0.75rem)",
                          textTransform: "uppercase",
                          letterSpacing: "0.0625rem",
                          verticalAlign: "middle",
                          whiteSpace: "nowrap",
                          position: "relative",
                          padding: "var(--lwc-tableCellSpacing,0.25rem 0.5rem)",
                          backgroundColor:
                            "var(--slds-g-color-neutral-base-95, var(--lwc-tableColorBackgroundHeader,rgb(243, 243, 243)))",
                          color:
                            "var(--slds-g-color-neutral-base-30, var(--lwc-tableColorTextHeader,rgb(68, 68, 68)))",
                          fontWeight: "var(--lwc-fontWeightBold,700)",
                          lineHeight: "normal",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          paddingRight: "var(--lwc-spacingLarge,1.5rem)",
                          maxWidth: "150px",
                        }}
                      >
                        <a
                          className="toggle"
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color:
                              "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                            marginRight: "20px",
                          }}
                        >
                          <span
                            className="assistiveText"
                            style={{
                              boxSizing: "border-box",
                              border: "0px",
                              margin: "-1px",
                              padding: "0px",
                              overflow: "hidden",
                              clip: "rect(0px, 0px, 0px, 0px)",
                              width: "1px",
                              height: "1px",
                              position: "absolute",
                            }}
                          >
                            Sort{" "}
                          </span>
                          Field Label
                        </a>
                        <span
                          className="assistiveText"
                          aria-atomic="true"
                          aria-live="assertive"
                          style={{
                            boxSizing: "border-box",
                            border: "0px",
                            margin: "-1px",
                            padding: "0px",
                            overflow: "hidden",
                            clip: "rect(0px, 0px, 0px, 0px)",
                            width: "1px",
                            height: "1px",
                            position: "absolute",
                          }}
                        >
                          Sorted Ascending
                        </span>
                      </th>
                      <th
                        className="slds-text-heading--label standard-col slds-truncate"
                        aria-label="Field Name"
                        scope="col"
                        title="Field Name"
                        style={{
                          boxSizing: "border-box",
                          textAlign: "left",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          fontSize: "var(--lwc-fontSize2,0.75rem)",
                          textTransform: "uppercase",
                          letterSpacing: "0.0625rem",
                          verticalAlign: "middle",
                          whiteSpace: "nowrap",
                          position: "relative",
                          padding: "var(--lwc-tableCellSpacing,0.25rem 0.5rem)",
                          backgroundColor:
                            "var(--slds-g-color-neutral-base-95, var(--lwc-tableColorBackgroundHeader,rgb(243, 243, 243)))",
                          color:
                            "var(--slds-g-color-neutral-base-30, var(--lwc-tableColorTextHeader,rgb(68, 68, 68)))",
                          fontWeight: "var(--lwc-fontWeightBold,700)",
                          lineHeight: "normal",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          maxWidth: "150px",
                        }}
                      >
                        <a
                          className="toggle"
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color:
                              "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                          }}
                        >
                          <span
                            className="assistiveText"
                            style={{
                              boxSizing: "border-box",
                              border: "0px",
                              margin: "-1px",
                              padding: "0px",
                              overflow: "hidden",
                              clip: "rect(0px, 0px, 0px, 0px)",
                              width: "1px",
                              height: "1px",
                              position: "absolute",
                            }}
                          >
                            Sort{" "}
                          </span>
                          Field Name
                        </a>
                        <span
                          className="assistiveText"
                          aria-atomic="true"
                          aria-live="assertive"
                          style={{
                            boxSizing: "border-box",
                            border: "0px",
                            margin: "-1px",
                            padding: "0px",
                            overflow: "hidden",
                            clip: "rect(0px, 0px, 0px, 0px)",
                            width: "1px",
                            height: "1px",
                            position: "absolute",
                          }}
                        />
                      </th>
                      <th
                        className="slds-text-heading--label standard-col slds-truncate"
                        aria-label="Data Type"
                        scope="col"
                        title="Data Type"
                        style={{
                          boxSizing: "border-box",
                          textAlign: "left",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          fontSize: "var(--lwc-fontSize2,0.75rem)",
                          textTransform: "uppercase",
                          letterSpacing: "0.0625rem",
                          verticalAlign: "middle",
                          whiteSpace: "nowrap",
                          position: "relative",
                          padding: "var(--lwc-tableCellSpacing,0.25rem 0.5rem)",
                          backgroundColor:
                            "var(--slds-g-color-neutral-base-95, var(--lwc-tableColorBackgroundHeader,rgb(243, 243, 243)))",
                          color:
                            "var(--slds-g-color-neutral-base-30, var(--lwc-tableColorTextHeader,rgb(68, 68, 68)))",
                          fontWeight: "var(--lwc-fontWeightBold,700)",
                          lineHeight: "normal",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          maxWidth: "150px",
                        }}
                      >
                        <a
                          className="toggle"
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color:
                              "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                          }}
                        >
                          <span
                            className="assistiveText"
                            style={{
                              boxSizing: "border-box",
                              border: "0px",
                              margin: "-1px",
                              padding: "0px",
                              overflow: "hidden",
                              clip: "rect(0px, 0px, 0px, 0px)",
                              width: "1px",
                              height: "1px",
                              position: "absolute",
                            }}
                          >
                            Sort{" "}
                          </span>
                          Data Type
                        </a>
                        <span
                          className="assistiveText"
                          aria-atomic="true"
                          aria-live="assertive"
                          style={{
                            boxSizing: "border-box",
                            border: "0px",
                            margin: "-1px",
                            padding: "0px",
                            overflow: "hidden",
                            clip: "rect(0px, 0px, 0px, 0px)",
                            width: "1px",
                            height: "1px",
                            position: "absolute",
                          }}
                        />
                      </th>
                      <th
                        className="slds-text-heading--label standard-col slds-truncate"
                        aria-label="Controlling Field"
                        scope="col"
                        title="Controlling Field"
                        style={{
                          boxSizing: "border-box",
                          textAlign: "left",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          fontSize: "var(--lwc-fontSize2,0.75rem)",
                          textTransform: "uppercase",
                          letterSpacing: "0.0625rem",
                          verticalAlign: "middle",
                          whiteSpace: "nowrap",
                          position: "relative",
                          padding: "var(--lwc-tableCellSpacing,0.25rem 0.5rem)",
                          backgroundColor:
                            "var(--slds-g-color-neutral-base-95, var(--lwc-tableColorBackgroundHeader,rgb(243, 243, 243)))",
                          color:
                            "var(--slds-g-color-neutral-base-30, var(--lwc-tableColorTextHeader,rgb(68, 68, 68)))",
                          fontWeight: "var(--lwc-fontWeightBold,700)",
                          lineHeight: "normal",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          maxWidth: "150px",
                        }}
                      >
                        <a
                          className="toggle"
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color:
                              "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                          }}
                        >
                          <span
                            className="assistiveText"
                            style={{
                              boxSizing: "border-box",
                              border: "0px",
                              margin: "-1px",
                              padding: "0px",
                              overflow: "hidden",
                              clip: "rect(0px, 0px, 0px, 0px)",
                              width: "1px",
                              height: "1px",
                              position: "absolute",
                            }}
                          >
                            Sort{" "}
                          </span>
                          Controlling Field
                        </a>
                        <span
                          className="assistiveText"
                          aria-atomic="true"
                          aria-live="assertive"
                          style={{
                            boxSizing: "border-box",
                            border: "0px",
                            margin: "-1px",
                            padding: "0px",
                            overflow: "hidden",
                            clip: "rect(0px, 0px, 0px, 0px)",
                            width: "1px",
                            height: "1px",
                            position: "absolute",
                          }}
                        />
                      </th>
                      <th
                        className="slds-text-heading--label standard-col slds-truncate"
                        aria-label="Indexed"
                        scope="col"
                        title="Indexed"
                        style={{
                          boxSizing: "border-box",
                          textAlign: "left",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          fontSize: "var(--lwc-fontSize2,0.75rem)",
                          textTransform: "uppercase",
                          letterSpacing: "0.0625rem",
                          verticalAlign: "middle",
                          whiteSpace: "nowrap",
                          position: "relative",
                          padding: "var(--lwc-tableCellSpacing,0.25rem 0.5rem)",
                          backgroundColor:
                            "var(--slds-g-color-neutral-base-95, var(--lwc-tableColorBackgroundHeader,rgb(243, 243, 243)))",
                          color:
                            "var(--slds-g-color-neutral-base-30, var(--lwc-tableColorTextHeader,rgb(68, 68, 68)))",
                          fontWeight: "var(--lwc-fontWeightBold,700)",
                          lineHeight: "normal",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          maxWidth: "150px",
                        }}
                      >
                        <a
                          className="toggle"
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color:
                              "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                          }}
                        >
                          <span
                            className="assistiveText"
                            style={{
                              boxSizing: "border-box",
                              border: "0px",
                              margin: "-1px",
                              padding: "0px",
                              overflow: "hidden",
                              clip: "rect(0px, 0px, 0px, 0px)",
                              width: "1px",
                              height: "1px",
                              position: "absolute",
                            }}
                          >
                            Sort{" "}
                          </span>
                          Indexed
                        </a>
                        <span
                          className="assistiveText"
                          aria-atomic="true"
                          aria-live="assertive"
                          style={{
                            boxSizing: "border-box",
                            border: "0px",
                            margin: "-1px",
                            padding: "0px",
                            overflow: "hidden",
                            clip: "rect(0px, 0px, 0px, 0px)",
                            width: "1px",
                            height: "1px",
                            position: "absolute",
                          }}
                        />
                      </th>
                      <th
                        className="actionCol slds-truncate"
                        aria-label="Actions"
                        scope="col"
                        title="Actions"
                        style={{
                          boxSizing: "border-box",
                          textAlign: "left",
                          overflow: "hidden",
                          maxWidth: "100%",
                          textOverflow: "ellipsis",
                          verticalAlign: "middle",
                          whiteSpace: "nowrap",
                          position: "relative",
                          padding: "var(--lwc-tableCellSpacing,0.25rem 0.5rem)",
                          backgroundColor:
                            "var(--slds-g-color-neutral-base-95, var(--lwc-tableColorBackgroundHeader,rgb(243, 243, 243)))",
                          color:
                            "var(--slds-g-color-neutral-base-30, var(--lwc-tableColorTextHeader,rgb(68, 68, 68)))",
                          fontWeight: "var(--lwc-fontWeightBold,700)",
                          lineHeight: "normal",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                        }}
                      >
                        <span
                          className="assistiveText"
                          style={{
                            boxSizing: "border-box",
                            border: "0px",
                            margin: "-1px",
                            padding: "0px",
                            overflow: "hidden",
                            clip: "rect(0px, 0px, 0px, 0px)",
                            width: "1px",
                            height: "1px",
                            position: "absolute",
                          }}
                        >
                          Actions
                        </span>
                      </th>
                    </tr>
                  </thead>
                  {/* No footer content needed */}
                  <tbody
                    style={{ boxSizing: "border-box", verticalAlign: "middle" }}
                  >
                    {/* Render custom fields */}
                    {customFields.map((field, index) => (
                      <tr
                        key={
                          field.id || `custom-field-${index}-${field.fieldName}`
                        }
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          counterIncrement: "row-number 1",
                          // Add a subtle background color for temporary fields
                          backgroundColor: field.isTemporary
                            ? "var(--slds-g-color-neutral-base-95, rgba(176, 231, 255, 0.3))"
                            : undefined,
                        }}
                      >
                        <td
                          style={{
                            boxSizing: "border-box",
                            verticalAlign: "middle",
                            position: "relative",
                            borderBottom:
                              "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                            padding: "var(--lwc-spacingXSmall,0.5rem)",
                            whiteSpace: "pre-line",
                            maxWidth: "250px",
                            visibility: "visible",
                            fontWeight: "normal",
                            textAlign: "left",
                            backgroundColor:
                              "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                          }}
                        >
                          <Link
                            to={`/setup/objectManager/custom-object/custom-field`}
                            state={{ field }}
                            style={{
                              boxSizing: "border-box",
                              backgroundColor: "transparent",
                              textDecoration: "none",
                              transition: "color 0.1s linear",
                              color:
                                "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                              cursor: "pointer",
                            }}
                          >
                            <span
                              className="uiOutputText"
                              style={{ boxSizing: "border-box" }}
                            >
                              {field.isTemporary
                                ? `${
                                    field.fieldLabel || "New Field"
                                  } (editing...)`
                                : field.fieldLabel}
                            </span>
                          </Link>
                        </td>
                        <td
                          style={{
                            boxSizing: "border-box",
                            verticalAlign: "middle",
                            position: "relative",
                            borderBottom:
                              "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                            padding: "var(--lwc-spacingXSmall,0.5rem)",
                            whiteSpace: "pre-line",
                            maxWidth: "250px",
                            visibility: "visible",
                            fontWeight: "normal",
                            textAlign: "left",
                            backgroundColor:
                              "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                          }}
                        >
                          <span
                            className="uiOutputText"
                            style={{ boxSizing: "border-box" }}
                          >
                            {field.fieldName}
                          </span>
                        </td>
                        <td
                          style={{
                            boxSizing: "border-box",
                            verticalAlign: "middle",
                            position: "relative",
                            borderBottom:
                              "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                            padding: "var(--lwc-spacingXSmall,0.5rem)",
                            whiteSpace: "pre-line",
                            maxWidth: "250px",
                            visibility: "visible",
                            fontWeight: "normal",
                            textAlign: "left",
                            backgroundColor:
                              "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                          }}
                        >
                          <span
                            className="uiOutputText"
                            style={{ boxSizing: "border-box" }}
                          >
                            {field.dataType}
                          </span>
                        </td>
                        <td
                          style={{
                            boxSizing: "border-box",
                            verticalAlign: "middle",
                            position: "relative",
                            borderBottom:
                              "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                            padding: "var(--lwc-spacingXSmall,0.5rem)",
                            whiteSpace: "pre-line",
                            maxWidth: "250px",
                            visibility: "visible",
                            fontWeight: "normal",
                            textAlign: "left",
                            backgroundColor:
                              "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                          }}
                        >
                          <span
                            className="uiOutputText"
                            style={{ boxSizing: "border-box" }}
                          >
                            {field.controllingField}
                          </span>
                        </td>
                        <td
                          style={{
                            boxSizing: "border-box",
                            verticalAlign: "middle",
                            position: "relative",
                            borderBottom:
                              "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                            padding: "var(--lwc-spacingXSmall,0.5rem)",
                            whiteSpace: "pre-line",
                            maxWidth: "250px",
                            visibility: "visible",
                            fontWeight: "normal",
                            textAlign: "left",
                            backgroundColor:
                              "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                          }}
                        >
                          {field.indexed ? (
                            <lightning-icon
                              className="slds-icon-utility-check om-checkmark slds-button__icon slds-icon_container forceIcon objectManagerOutputCheckbox"
                              style={{
                                boxSizing: "border-box",
                                width:
                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                height:
                                  "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                borderRadius:
                                  "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                display: "inline-block",
                                lineHeight: "var(--lwc-lineHeightReset,1)",
                                backgroundColor:
                                  "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                              }}
                            >
                              <span style={{ boxSizing: "border-box" }}>
                                <lightning-primitive-icon
                                  size="xx-small"
                                  style={{ boxSizing: "border-box" }}
                                >
                                  <svg
                                    className="slds-icon slds-icon_xx-small"
                                    aria-hidden="true"
                                    focusable="false"
                                    viewBox="0 0 520 520"
                                    style={{
                                      boxSizing: "border-box",
                                      verticalAlign: "middle",
                                      lineHeight:
                                        "var(--lwc-lineHeightReset,1)",
                                      overflow: "hidden",
                                      width:
                                        "var(--lwc-squareIconXxSmallContent,.875rem)",
                                      height:
                                        "var(--lwc-squareIconXxSmallContent,.875rem)",
                                      fill: "currentcolor",
                                    }}
                                  >
                                    <g style={{ boxSizing: "border-box" }}>
                                      <path
                                        d="M191 425L26 259c-6-6-6-16 0-22l22-22c6-6 16-6 22 0l124 125a10 10 0 0015 0L452 95c6-6 16-6 22 0l22 22c6 6 6 16 0 22L213 425c-6 7-16 7-22 0z"
                                        style={{ boxSizing: "border-box" }}
                                      />
                                    </g>
                                  </svg>
                                </lightning-primitive-icon>
                                <span
                                  className="slds-assistive-text"
                                  style={{
                                    boxSizing: "border-box",
                                    whiteSpace: "nowrap",
                                    textTransform: "none",
                                    margin: "-1px",
                                    border: "var(--lwc-spacingNone, 0)",
                                    padding: "var(--lwc-spacingNone, 0)",
                                    overflow: "hidden",
                                    position: "absolute",
                                    width: "var(--lwc-borderWidthThin, 1px)",
                                    height: "var(--lwc-borderWidthThin, 1px)",
                                    clip: "rect(0px, 0px, 0px, 0px)",
                                  }}
                                >
                                  True
                                </span>
                              </span>
                            </lightning-icon>
                          ) : (
                            <span
                              className="unchecked objectManagerOutputCheckbox"
                              style={{
                                boxSizing: "border-box",
                                display: "inline-block",
                              }}
                            />
                          )}
                        </td>
                        <td
                          style={{
                            boxSizing: "border-box",
                            verticalAlign: "middle",
                            position: "relative",
                            borderBottom:
                              "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                            padding: "var(--lwc-spacingXSmall,0.5rem)",
                            whiteSpace: "pre-line",
                            maxWidth: "250px",
                            visibility: "visible",
                            fontWeight: "normal",
                            textAlign: "right",
                            backgroundColor:
                              "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                          }}
                        >
                          <div
                            className="objectManagerVirtualActionMenu"
                            style={{ boxSizing: "border-box" }}
                          >
                            <div
                              className="slds-dropdown-trigger slds-dropdown-trigger--click"
                              style={{
                                boxSizing: "border-box",
                                position: "relative",
                                display: "inline-block",
                              }}
                            >
                              <a
                                className="rowActionsPlaceHolder slds-button slds-button--icon-border-filled"
                                aria-haspopup="true"
                                href="#"
                                style={{
                                  boxSizing: "border-box",
                                  cursor: "pointer",
                                  backgroundPosition: "initial",
                                  borderStyle: "solid",
                                  borderWidth:
                                    "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                  borderRadius:
                                    "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                  textDecoration: "none",
                                  whiteSpace: "normal",
                                  position: "relative",
                                  display: "inline-flex",
                                  alignItems: "center",
                                  backgroundImage: "none",
                                  backgroundSize: "initial",
                                  backgroundRepeat: "initial",
                                  backgroundAttachment: "initial",
                                  backgroundOrigin: "initial",
                                  backgroundClip: "border-box",
                                  boxShadow:
                                    "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                  appearance: "none",
                                  userSelect: "none",
                                  justifyContent: "center",
                                  flexShrink: 0,
                                  width:
                                    "var(--lwc-squareIconMediumBoundary,2rem)",
                                  height:
                                    "var(--lwc-squareIconMediumBoundary,2rem)",
                                  transition: "border 0.15s linear",
                                  borderColor:
                                    "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                  verticalAlign: "middle",
                                  color:
                                    "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                  borderTopStyle: "",
                                  borderTopWidth: "",
                                  borderRightStyle: "",
                                  borderRightWidth: "",
                                  borderBottomStyle: "",
                                  borderBottomWidth: "",
                                  borderLeftStyle: "",
                                  borderLeftWidth: "",
                                  borderImageSource: "",
                                  borderImageSlice: "",
                                  borderImageWidth: "",
                                  borderImageOutset: "",
                                  borderImageRepeat: "",
                                  backgroundColor:
                                    "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                                  textAlign: "center",
                                  padding: "var(--lwc-borderWidthThin,1px)",
                                  lineHeight:
                                    "var(--lwc-lineHeightButton,1.875rem)",
                                  paddingTop: "",
                                  paddingRight: "",
                                  paddingBottom: "",
                                  paddingLeft: "",
                                }}
                              >
                                <lightning-icon
                                  className="slds-icon-utility-down line-height-button slds-button__icon slds-icon_container forceIcon"
                                  style={{
                                    boxSizing: "border-box",
                                    width:
                                      "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                    height:
                                      "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                    fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                    borderRadius:
                                      "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                    display: "inline-block",
                                    lineHeight: "var(--lwc-lineHeightReset,1)",
                                    backgroundColor:
                                      "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                                  }}
                                >
                                  <span style={{ boxSizing: "border-box" }}>
                                    <lightning-primitive-icon
                                      size="xx-small"
                                      style={{ boxSizing: "border-box" }}
                                    >
                                      <svg
                                        className="slds-icon slds-icon_xx-small"
                                        aria-hidden="true"
                                        focusable="false"
                                        viewBox="0 0 520 520"
                                        style={{
                                          boxSizing: "border-box",
                                          verticalAlign: "middle",
                                          lineHeight:
                                            "var(--lwc-lineHeightReset,1)",
                                          overflow: "hidden",
                                          width:
                                            "var(--lwc-squareIconXxSmallContent,.875rem)",
                                          height:
                                            "var(--lwc-squareIconXxSmallContent,.875rem)",
                                          fill: "currentcolor",
                                        }}
                                      >
                                        <g style={{ boxSizing: "border-box" }}>
                                          <path
                                            d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                            style={{ boxSizing: "border-box" }}
                                          />
                                        </g>
                                      </svg>
                                    </lightning-primitive-icon>
                                  </span>
                                  <span
                                    className="slds-assistive-text"
                                    style={{
                                      boxSizing: "border-box",
                                      margin: "-1px",
                                      border: "0px",
                                      padding: "0px",
                                      overflow: "hidden",
                                      whiteSpace: "nowrap",
                                      position: "absolute",
                                      width: "1px",
                                      height: "1px",
                                      clip: "rect(0px, 0px, 0px, 0px)",
                                      textTransform: "none",
                                    }}
                                  >
                                    Show More
                                  </span>
                                </lightning-icon>
                              </a>
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))}

                    {/* Original system fields */}
                    <tr
                      key="createdBy"
                      style={{
                        boxSizing: "border-box",
                        verticalAlign: "middle",
                        counterIncrement: "row-number 1",
                      }}
                    >
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <a
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color:
                              "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                          }}
                        >
                          <span
                            className="uiOutputText"
                            style={{ boxSizing: "border-box" }}
                          >
                            Created By
                          </span>
                        </a>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        >
                          CreatedById
                        </span>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        >
                          Lookup(User)
                        </span>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        />
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="unchecked objectManagerOutputCheckbox"
                          style={{
                            boxSizing: "border-box",
                            display: "inline-block",
                          }}
                        />
                        <span
                          className="assistiveText objectManagerOutputCheckbox"
                          style={{
                            boxSizing: "border-box",
                            border: "0px",
                            margin: "-1px",
                            padding: "0px",
                            overflow: "hidden",
                            clip: "rect(0px, 0px, 0px, 0px)",
                            width: "1px",
                            height: "1px",
                            position: "absolute",
                          }}
                        >
                          False
                        </span>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      />
                    </tr>
                    <tr
                      key="lastModifiedBy"
                      style={{
                        boxSizing: "border-box",
                        verticalAlign: "middle",
                        counterIncrement: "row-number 1",
                      }}
                    >
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <a
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color:
                              "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                          }}
                        >
                          <span
                            className="uiOutputText"
                            style={{ boxSizing: "border-box" }}
                          >
                            Last Modified By
                          </span>
                        </a>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        >
                          LastModifiedById
                        </span>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        >
                          Lookup(User)
                        </span>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        />
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="unchecked objectManagerOutputCheckbox"
                          style={{
                            boxSizing: "border-box",
                            display: "inline-block",
                          }}
                        />
                        <span
                          className="assistiveText objectManagerOutputCheckbox"
                          style={{
                            boxSizing: "border-box",
                            border: "0px",
                            margin: "-1px",
                            padding: "0px",
                            overflow: "hidden",
                            clip: "rect(0px, 0px, 0px, 0px)",
                            width: "1px",
                            height: "1px",
                            position: "absolute",
                          }}
                        >
                          False
                        </span>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      />
                    </tr>
                    <tr
                      key="owner"
                      style={{
                        boxSizing: "border-box",
                        verticalAlign: "middle",
                        counterIncrement: "row-number 1",
                      }}
                    >
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <a
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color:
                              "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                          }}
                        >
                          <span
                            className="uiOutputText"
                            style={{ boxSizing: "border-box" }}
                          >
                            Owner
                          </span>
                        </a>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        >
                          OwnerId
                        </span>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        >
                          Lookup(User,Group)
                        </span>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        />
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <lightning-icon
                          className="slds-icon-utility-check om-checkmark slds-button__icon slds-icon_container forceIcon objectManagerOutputCheckbox"
                          style={{
                            boxSizing: "border-box",
                            width:
                              "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                            height:
                              "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                            fill: "var(--slds-c-icon-color-foreground, currentColor)",
                            borderRadius:
                              "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                            display: "inline-block",
                            lineHeight: "var(--lwc-lineHeightReset,1)",
                            backgroundColor:
                              "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                          }}
                        >
                          <span style={{ boxSizing: "border-box" }}>
                            <lightning-primitive-icon
                              size="xx-small"
                              style={{ boxSizing: "border-box" }}
                            >
                              <svg
                                className="slds-icon slds-icon_xx-small"
                                aria-hidden="true"
                                focusable="false"
                                viewBox="0 0 520 520"
                                style={{
                                  boxSizing: "border-box",
                                  verticalAlign: "middle",
                                  lineHeight: "var(--lwc-lineHeightReset,1)",
                                  overflow: "hidden",
                                  width:
                                    "var(--lwc-squareIconXxSmallContent,.875rem)",
                                  height:
                                    "var(--lwc-squareIconXxSmallContent,.875rem)",
                                  fill: "currentcolor",
                                }}
                              >
                                <g style={{ boxSizing: "border-box" }}>
                                  <path
                                    d="M191 425L26 259c-6-6-6-16 0-22l22-22c6-6 16-6 22 0l124 125a10 10 0 0015 0L452 95c6-6 16-6 22 0l22 22c6 6 6 16 0 22L213 425c-6 7-16 7-22 0z"
                                    style={{ boxSizing: "border-box" }}
                                  />
                                </g>
                              </svg>
                            </lightning-primitive-icon>
                            <span
                              className="slds-assistive-text"
                              style={{
                                boxSizing: "border-box",
                                whiteSpace: "nowrap",
                                textTransform: "none",
                                margin: "-1px",
                                border: "var(--lwc-spacingNone, 0)",
                                padding: "var(--lwc-spacingNone, 0)",
                                overflow: "hidden",
                                position: "absolute",
                                width: "var(--lwc-borderWidthThin, 1px)",
                                height: "var(--lwc-borderWidthThin, 1px)",
                                clip: "rect(0px, 0px, 0px, 0px)",
                              }}
                            >
                              True
                            </span>
                          </span>
                        </lightning-icon>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      />
                    </tr>
                    <tr
                      key="project-name"
                      style={{
                        boxSizing: "border-box",
                        verticalAlign: "middle",
                        counterIncrement: "row-number 1",
                      }}
                    >
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <a
                          href="#"
                          style={{
                            boxSizing: "border-box",
                            backgroundColor: "transparent",
                            textDecoration: "none",
                            transition: "color 0.1s linear",
                            color:
                              "var(--lwc-brandTextLink,rgba(11, 92, 171, 1))",
                            cursor: "pointer",
                          }}
                        >
                          <span
                            className="uiOutputText"
                            style={{ boxSizing: "border-box" }}
                          >
                            Project Name
                          </span>
                        </a>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        >
                          Name
                        </span>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        >
                          Text(80)
                        </span>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <span
                          className="uiOutputText"
                          style={{ boxSizing: "border-box" }}
                        />
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "left",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <lightning-icon
                          className="slds-icon-utility-check om-checkmark slds-button__icon slds-icon_container forceIcon objectManagerOutputCheckbox"
                          style={{
                            boxSizing: "border-box",
                            width:
                              "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                            height:
                              "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                            fill: "var(--slds-c-icon-color-foreground, currentColor)",
                            borderRadius:
                              "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                            display: "inline-block",
                            lineHeight: "var(--lwc-lineHeightReset,1)",
                            backgroundColor:
                              "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                          }}
                        >
                          <span style={{ boxSizing: "border-box" }}>
                            <lightning-primitive-icon
                              size="xx-small"
                              style={{ boxSizing: "border-box" }}
                            >
                              <svg
                                className="slds-icon slds-icon_xx-small"
                                aria-hidden="true"
                                focusable="false"
                                viewBox="0 0 520 520"
                                style={{
                                  boxSizing: "border-box",
                                  verticalAlign: "middle",
                                  lineHeight: "var(--lwc-lineHeightReset,1)",
                                  overflow: "hidden",
                                  width:
                                    "var(--lwc-squareIconXxSmallContent,.875rem)",
                                  height:
                                    "var(--lwc-squareIconXxSmallContent,.875rem)",
                                  fill: "currentcolor",
                                }}
                              >
                                <g style={{ boxSizing: "border-box" }}>
                                  <path
                                    d="M191 425L26 259c-6-6-6-16 0-22l22-22c6-6 16-6 22 0l124 125a10 10 0 0015 0L452 95c6-6 16-6 22 0l22 22c6 6 6 16 0 22L213 425c-6 7-16 7-22 0z"
                                    style={{ boxSizing: "border-box" }}
                                  />
                                </g>
                              </svg>
                            </lightning-primitive-icon>
                            <span
                              className="slds-assistive-text"
                              style={{
                                boxSizing: "border-box",
                                whiteSpace: "nowrap",
                                textTransform: "none",
                                margin: "-1px",
                                border: "var(--lwc-spacingNone, 0)",
                                padding: "var(--lwc-spacingNone, 0)",
                                overflow: "hidden",
                                position: "absolute",
                                width: "var(--lwc-borderWidthThin, 1px)",
                                height: "var(--lwc-borderWidthThin, 1px)",
                                clip: "rect(0px, 0px, 0px, 0px)",
                              }}
                            >
                              True
                            </span>
                          </span>
                        </lightning-icon>
                      </td>
                      <td
                        style={{
                          boxSizing: "border-box",
                          verticalAlign: "middle",
                          position: "relative",
                          borderBottom:
                            "var(--lwc-borderWidthThin,1px) solid var(--lwc-colorBorder,rgb(229, 229, 229))",
                          padding: "var(--lwc-spacingXSmall,0.5rem)",
                          whiteSpace: "pre-line",
                          maxWidth: "250px",
                          visibility: "visible",
                          fontWeight: "normal",
                          textAlign: "right",
                          backgroundColor:
                            "var(--lwc-colorBackgroundAlt,rgb(255, 255, 255))",
                        }}
                      >
                        <div
                          className="objectManagerVirtualActionMenu"
                          style={{ boxSizing: "border-box" }}
                        >
                          <div
                            className="slds-dropdown-trigger slds-dropdown-trigger--click"
                            style={{
                              boxSizing: "border-box",
                              position: "relative",
                              display: "inline-block",
                            }}
                          >
                            <a
                              className="rowActionsPlaceHolder slds-button slds-button--icon-border-filled"
                              aria-haspopup="true"
                              href="#"
                              style={{
                                boxSizing: "border-box",
                                cursor: "pointer",
                                backgroundPosition: "initial",
                                borderStyle: "solid",
                                borderWidth:
                                  "var(--slds-c-button-sizing-border, var(--sds-c-button-sizing-border, var(--lwc-borderWidthThin,1px)))",
                                borderRadius:
                                  "var(--slds-c-button-radius-border, var(--sds-c-button-radius-border, var(--lwc-buttonBorderRadius,.25rem)))",
                                textDecoration: "none",
                                whiteSpace: "normal",
                                position: "relative",
                                display: "inline-flex",
                                alignItems: "center",
                                backgroundImage: "none",
                                backgroundSize: "initial",
                                backgroundRepeat: "initial",
                                backgroundAttachment: "initial",
                                backgroundOrigin: "initial",
                                backgroundClip: "border-box",
                                boxShadow:
                                  "var(--slds-c-button-shadow, var(--sds-c-button-shadow))",
                                appearance: "none",
                                userSelect: "none",
                                justifyContent: "center",
                                flexShrink: 0,
                                width:
                                  "var(--lwc-squareIconMediumBoundary,2rem)",
                                height:
                                  "var(--lwc-squareIconMediumBoundary,2rem)",
                                transition: "border 0.15s linear",
                                borderColor:
                                  "var(--slds-g-color-border-base-4, var(--lwc-buttonColorBorderPrimary,rgb(201, 201, 201)))",
                                verticalAlign: "middle",
                                color:
                                  "var(--slds-g-color-neutral-base-50, var(--lwc-colorTextIconDefault,rgb(116, 116, 116)))",
                                borderTopStyle: "",
                                borderTopWidth: "",
                                borderRightStyle: "",
                                borderRightWidth: "",
                                borderBottomStyle: "",
                                borderBottomWidth: "",
                                borderLeftStyle: "",
                                borderLeftWidth: "",
                                borderImageSource: "",
                                borderImageSlice: "",
                                borderImageWidth: "",
                                borderImageOutset: "",
                                borderImageRepeat: "",
                                backgroundColor:
                                  "var(--slds-g-color-neutral-base-100, var(--lwc-buttonColorBackgroundPrimary,rgb(255, 255, 255)))",
                                textAlign: "center",
                                padding: "var(--lwc-borderWidthThin,1px)",
                                lineHeight:
                                  "var(--lwc-lineHeightButton,1.875rem)",
                                paddingTop: "",
                                paddingRight: "",
                                paddingBottom: "",
                                paddingLeft: "",
                              }}
                            >
                              <lightning-icon
                                className="slds-icon-utility-down line-height-button slds-button__icon slds-icon_container forceIcon"
                                style={{
                                  boxSizing: "border-box",
                                  width:
                                    "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                  height:
                                    "var(--lwc-squareIconMediumContentAlt,0.875rem)",
                                  fill: "var(--slds-c-icon-color-foreground, currentColor)",
                                  borderRadius:
                                    "var(--slds-c-icon-radius-border, var(--sds-c-icon-radius-border, var(--lwc-borderRadiusMedium,0.25rem)))",
                                  display: "inline-block",
                                  lineHeight: "var(--lwc-lineHeightReset,1)",
                                  backgroundColor:
                                    "var(--slds-c-icon-color-background, var(--sds-c-icon-color-background, transparent))",
                                }}
                              >
                                <span style={{ boxSizing: "border-box" }}>
                                  <lightning-primitive-icon
                                    size="xx-small"
                                    style={{ boxSizing: "border-box" }}
                                  >
                                    <svg
                                      className="slds-icon slds-icon_xx-small"
                                      aria-hidden="true"
                                      focusable="false"
                                      viewBox="0 0 520 520"
                                      style={{
                                        boxSizing: "border-box",
                                        verticalAlign: "middle",
                                        lineHeight:
                                          "var(--lwc-lineHeightReset,1)",
                                        overflow: "hidden",
                                        width:
                                          "var(--lwc-squareIconXxSmallContent,.875rem)",
                                        height:
                                          "var(--lwc-squareIconXxSmallContent,.875rem)",
                                        fill: "currentcolor",
                                      }}
                                    >
                                      <g style={{ boxSizing: "border-box" }}>
                                        <path
                                          d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z"
                                          style={{ boxSizing: "border-box" }}
                                        />
                                      </g>
                                    </svg>
                                  </lightning-primitive-icon>
                                </span>
                              </lightning-icon>
                              <span
                                className="slds-assistive-text"
                                style={{
                                  boxSizing: "border-box",
                                  margin: "-1px",
                                  border: "0px",
                                  padding: "0px",
                                  overflow: "hidden",
                                  whiteSpace: "nowrap",
                                  position: "absolute",
                                  width: "1px",
                                  height: "1px",
                                  clip: "rect(0px, 0px, 0px, 0px)",
                                  textTransform: "none",
                                }}
                              >
                                Show More
                              </span>
                            </a>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div
                  className="infinite-loading"
                  style={{ boxSizing: "border-box" }}
                >
                  <span className="il" style={{ boxSizing: "border-box" }} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* --- Add Hover Style --- */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
                /* Target BOTH th and td cells within hovered tbody rows */
                .slds-table > tbody > tr:hover > th,
                .slds-table > tbody > tr:hover > td {
                    /* Set background to the specified gray */
                    background-color: rgb(243, 243, 243) !important;
                }
            `,
        }}
      />
      {/* --- End Hover Style --- */}
    </>
  );
}
