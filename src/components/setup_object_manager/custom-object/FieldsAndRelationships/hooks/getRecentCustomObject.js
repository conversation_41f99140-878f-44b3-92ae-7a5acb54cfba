/**
 * Helper function to get the most recent custom object
 * This is used when no specific object is selected
 */
export const getMostRecentCustomObject = () => {
  // Check if we have opportunity objects in window.allVariableData
  const opportunityObjects = [
    {
      apiName: "Opportunity",
      label: "Opportunity",
      id: "Opportunity",
    },
    {
      apiName: "OpportunityContactRole",
      label: "Opportunity Contact Role",
      id: "OpportunityContactRole",
    },
    {
      apiName: "OpportunityLineItem",
      label: "Opportunity Product",
      id: "OpportunityLineItem",
    }
  ];
  
  // Return the first opportunity object as the most recent
  return opportunityObjects[0];
};
