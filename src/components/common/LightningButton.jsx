import React from "react";
import styled from "styled-components";

// Base Button - common styles for all buttons
const BaseButton = styled.button`
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 0 1rem;
  background: transparent;
  background-clip: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  border-radius: 0.25rem;
  line-height: 1.875rem;
  text-decoration: none;
  color: #0070d2;
  appearance: none;
  white-space: normal;
  user-select: none;
  font: inherit;
  margin: 0;
  overflow: visible;
  text-transform: none;
  cursor: pointer;
  background-position: initial;
  text-align: center;
  justify-content: center;
  transition: border 0.15s linear;

  &:hover,
  &:focus {
    color: #005fb2;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 3px #0070d2;
  }
`;

// Neutral Button - standard button style
const NeutralButton = styled(BaseButton)`
  border-color: ${(props) => props.$borderColor || "rgb(1, 1, 1)"};
  border-style: solid;
  background-color: ${(props) => props.$backgroundColor || "white"};
  color: ${(props) => props.$textColor || "rgba(1, 118, 211, 1)"};
  border-radius: ${(props) => props.$borderRadius || "0.25rem"};
  border-width: ${(props) => props.$borderWidth || "1px"};
  margin-left: ${(props) => props.$marginLeft || "0"};
  height: ${(props) => props.$height || "auto"};
  min-height: ${(props) => props.$minHeight || "2rem"};

  &:hover,
  &:focus {
    border-color: rgb(201, 201, 201);
  }
`;

// Brand Button - primary action button
const BrandButton = styled(BaseButton)`
  background-color: #0070d2;
  border-color: #0070d2;
  color: white;

  &:hover,
  &:focus {
    background-color: #005fb2;
    border-color: #005fb2;
    color: white;
  }
`;

// Icon Button - button with just an icon
const IconButton = styled(BaseButton)`
  width: ${(props) => props.$size || "2rem"};
  height: ${(props) => props.$size || "2rem"};
  padding: 0;
  border-radius: ${(props) => props.$borderRadius || "0.25rem"};
  justify-content: center;

  & svg {
    width: ${(props) => props.$iconSize || "0.875rem"};
    height: ${(props) => props.$iconSize || "0.875rem"};
    fill: currentColor;
  }
`;

// Button Container - for button groups
const ButtonGroup = styled.ul`
  display: inline-flex;
  margin: 0;
  padding: 0;
  list-style: none;
  border-radius: 0.25rem;

  & li {
    display: block;
  }

  & ${NeutralButton} {
    border-radius: 0;
  }

  & li:first-child ${NeutralButton} {
    border-radius: 0.25rem 0 0 0.25rem;
  }

  & li:last-child ${NeutralButton} {
    border-radius: 0 0.25rem 0.25rem 0;
  }

  & li + li ${NeutralButton} {
    margin-left: -1px;
  }

  & ${NeutralButton}:hover {
    z-index: 1;
  }
`;

// Lightning Button Component
const LightningButton = ({
  variant = "neutral",
  label,
  name,
  onClick,
  disabled,
  className,
  style,
  children,
  borderColor,
  backgroundColor,
  textColor,
  borderRadius,
  borderWidth,
  marginLeft,
  height,
  minHeight,
  ...props
}) => {
  const ButtonComponent = variant === "brand" ? BrandButton : NeutralButton;

  return (
    <ButtonComponent
      type="button"
      name={name}
      onClick={onClick}
      disabled={disabled}
      className={`slds-button slds-button_${variant} ${className || ""}`}
      style={{
        ...style,
      }}
      aria-disabled={disabled}
      $borderColor={borderColor}
      $backgroundColor={backgroundColor}
      $textColor={textColor}
      $borderRadius={borderRadius}
      $borderWidth={borderWidth}
      $marginLeft={marginLeft}
      $height={height}
      $minHeight={minHeight}
      {...props}
    >
      {label || children}
    </ButtonComponent>
  );
};

// Dropdown Button - for the dropdown menu
const DropdownButton = styled(BaseButton)`
  border-color: rgb(4, 4, 4);
  border-style: solid;
  background-color: white;
  color: rgb(116, 116, 116);
  border-radius: ${(props) => props.$borderRadius || "0 0.25rem 0.25rem 0"};
  border-width: 1px;
  margin-left: -1px;
  width: 2rem;
  padding: 0;
  justify-content: center;

  &:hover,
  &:focus {
    background-color: rgb(244, 246, 249);
    border-color: rgb(201, 201, 201);
    color: rgb(116, 116, 116);
  }
`;

// Lightning Button Menu Component (dropdown)
const LightningButtonMenu = ({
  children,
  variant = "border-filled",
  className,
  style,
  onClick,
  borderRadius,
  ...props
}) => {
  return (
    <div
      className={`slds-dropdown-trigger slds-dropdown-trigger_click ${
        className || ""
      }`}
      style={{ position: "relative", display: "inline-block", ...style }}
      {...props}
    >
      <DropdownButton
        type="button"
        onClick={onClick}
        aria-haspopup="true"
        $borderRadius={borderRadius}
      >
        <svg
          viewBox="0 0 520 520"
          aria-hidden="true"
          focusable="false"
          style={{
            width: "0.875rem",
            height: "1.85rem",
            fill: "currentColor",
          }}
        >
          <g>
            <path d="M83 140h354c10 0 17 13 9 22L273 374c-6 8-19 8-25 0L73 162c-7-9-1-22 10-22z" />
          </g>
        </svg>
        <span className="slds-assistive-text">Show more actions</span>
      </DropdownButton>
      {children}
    </div>
  );
};

// Lightning Button Icon Component
const LightningButtonIcon = ({
  icon,
  variant = "border-filled",
  size = "medium",
  onClick,
  className,
  style,
  borderRadius,
  iconSize,
  ...props
}) => {
  return (
    <IconButton
      type="button"
      onClick={onClick}
      className={`slds-button slds-button_icon slds-button_icon-${variant} ${
        className || ""
      }`}
      style={style}
      $size={size}
      $borderRadius={borderRadius}
      $iconSize={iconSize}
      {...props}
    >
      {icon}
    </IconButton>
  );
};

export {
  LightningButton,
  LightningButtonMenu,
  LightningButtonIcon,
  ButtonGroup,
  NeutralButton,
  BrandButton,
  IconButton,
  DropdownButton,
};
