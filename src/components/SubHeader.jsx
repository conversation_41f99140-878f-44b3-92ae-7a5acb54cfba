import React, { useEffect, useState, useContext } from "react";

import AppLauncherDialog from "./app_launcher/AppLauncherDialog";
import AppLauncherPopup from "./app_launcher/AppLauncherPopup";

import { GlobalContext } from "../context/GlobalContext";

import { useNavigate, useLocation } from "react-router-dom";

export default function SubHeader() {
  const navigate = useNavigate();
  const location = useLocation();

  const appName = location.pathname.startsWith("/sales/") ? "Sales" : "Setup";

  const { activeTab, setActiveTab, tabs } = useContext(GlobalContext);

  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [isPopupVisible, setIsPopupVisible] = useState(false);

  useEffect(() => {
    const cleanPath = location.pathname.replace(/\/+$/, ""); // Remove trailing slashes

    if (
      cleanPath.startsWith("/setup/home") ||
      cleanPath.startsWith("/setup/profiles")
    ) {
      setActiveTab("Home");
    } else if (cleanPath.startsWith("/setup/objectManager")) {
      setActiveTab("Object Manager");
    } else if (cleanPath.startsWith("/sales/opportunities")) {
      setActiveTab("Opportunities");
    }
  }, [location.pathname]);

  const toggleDialog = () => {
    setIsDialogVisible(!isDialogVisible);
  };

  const closeDialog = () => {
    setIsDialogVisible(false);
  };

  const openPopup = () => {
    setIsPopupVisible(true);
    closeDialog();
  };

  const closePopup = () => {
    setIsPopupVisible(false);
  };

  const changeTab = (tab) => {
    setActiveTab(tab);

    if (tab === "Home") {
      const isSales = location.pathname.startsWith("/sales/");
      navigate(isSales ? "/sales/home/" : "/setup/home/", { replace: true });
    } else if (tab === "Object Manager") {
      navigate("/setup/objectManager/", { replace: true });
    } else if (tab === "Opportunities") {
      // when you click the Opportunities tab, go to your new page
      navigate("/sales/opportunities", { replace: true });
    } else if (tab === "Contacts") {
      navigate("/sales/contacts", { replace: true });
    } else if (tab === "Accounts") {
      // you might want to go to some generic accounts list
    }
  };

  useEffect(() => {
    const cleanPath = location.pathname.replace(/\/+$/, ""); // Remove trailing slashes

    if (
      cleanPath.startsWith("/setup/home") ||
      cleanPath.startsWith("/setup/profiles")
    ) {
      setActiveTab("Home");
    } else if (cleanPath.startsWith("/setup/objectManager")) {
      setActiveTab("Object Manager");
    } else if (cleanPath.startsWith("/sales/opportunities")) {
      setActiveTab("Opportunities");
    } else if (cleanPath.startsWith("/sales/contacts")) {
      setActiveTab("Contacts");
    } else if (cleanPath.startsWith("/sales/accounts")) {
      setActiveTab("Accounts");
    }
  }, [location.pathname]);

  return (
    <>
      <div className="slds-context-bar slds-context-bar--tabs slds-no-print noLeftSpacing">
        <div className="slds-context-bar__primary navLeft navBar">
          <div className="slds-context-bar__item slds-no-hover">
            <div className="slds-context-bar__icon-action">
              <button
                className="slds-button slds-context-bar__button slds-icon-waffle_container slds-show"
                title="App Launcher"
                onClick={toggleDialog}
              >
                <div className="slds-icon-waffle">
                  <div className="slds-r1" />
                  <div className="slds-r2" />
                  <div className="slds-r3" />
                  <div className="slds-r4" />
                  <div className="slds-r5" />
                  <div className="slds-r6" />
                  <div className="slds-r7" />
                  <div className="slds-r8" />
                  <div className="slds-r9" />
                </div>
                <span className="slds-assistive-text">App Launcher</span>
              </button>

              {isDialogVisible && (
                <AppLauncherDialog
                  closeDialog={closeDialog}
                  openPopup={openPopup}
                />
              )}
            </div>
            <div className="slds-context-bar__label-action slds-context-bar__app-name">
              <span className="slds-truncate" title={appName}>
                {appName}
              </span>
            </div>
          </div>
        </div>

        <div className="slds-context-bar__secondary navCenter tabBarContainer">
          <div
            className="slds-grid active navexConsoleTabContainer"
            role="navigation"
          >
            <ul className="slds-grid" role="tablist">
              {tabs.map((tab) => (
                <li
                  key={tab.id}
                  className={`slds-context-bar__item ${
                    activeTab === tab.label ? "slds-is-active" : ""
                  }`}
                >
                  <a
                    className="slds-context-bar__label-action"
                    role="tab"
                    tabIndex="0"
                    title={tab.label}
                    onClick={() => changeTab(tab.label)}
                    style={{ cursor: "pointer" }}
                  >
                    {/* <span className="slds-truncate">{tab.label}</span> */}

                    <span
                      className="slds-truncate"
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "4px",
                      }}
                    >
                      {tab.label}
                      {tab.hasChevron && (
                        <img
                          src="/assets/icons/utility/chevrondown_60.png"
                          alt="▼"
                          style={{
                            width: "10px",
                            height: "10px",
                            marginTop: "1px",
                          }}
                        />
                      )}
                    </span>
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {isPopupVisible && <AppLauncherPopup closePopup={closePopup} />}
    </>
  );
}
