// src/pages/AccountDetailPage.jsx
import React, { useContext, useEffect } from "react";
import { GlobalContext } from "../context/GlobalContext";
import { SalesSubHeaderTabs } from "../context/SubHeaderTabs";
import AccountsTopBar from "../components/opportunities/AccountsTopBar";
import AccountDetailBody from "../components/opportunities/AccountDetailBody";

export default function AccountDetailPage() {
  const { setTabs, setIsSidebarVisible } = useContext(GlobalContext);

  useEffect(() => {
    setTabs(SalesSubHeaderTabs);
    setIsSidebarVisible(false);
  }, [setTabs, setIsSidebarVisible]);

  // how tall your TopBar is (tweak if needed)
  const TOPBAR_HEIGHT = "8.8rem";
  // how far down on screen (e.g. to clear sub-header tabs)
  const TOP_OFFSET = "6.4rem";

  return (
    <div
      style={{
        position: "fixed",
        inset: 0,
        backgroundImage: "url('/assets/images/background/background.png')",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        zIndex: 0,
      }}
    >
      {/* 1) pin TopBar at a fixed spot */}
      <div
        style={{
          position: "fixed",
          top: TOP_OFFSET,
          left: 0,
          right: 0,
          zIndex: 1,
        }}
      >
        <AccountsTopBar />
      </div>

      {/* 2) push only the body down by the TopBar’s height */}
      <div
        style={{
          marginTop: `calc(${TOP_OFFSET} + ${TOPBAR_HEIGHT})`,
          marginLeft: "0.7rem",
          marginRight: "1rem",
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          overflowY: "auto",
          boxSizing: "border-box",
        }}
      >
        <AccountDetailBody />
      </div>
    </div>
  );
}
