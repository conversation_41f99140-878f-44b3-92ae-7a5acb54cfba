// src/pages/ContactsPage.jsx
import React, { useContext, useEffect } from "react";
import { GlobalContext } from "../context/GlobalContext";
import { SalesSubHeaderTabs } from "../context/SubHeaderTabs";
import ContactsTable from "../components/contacts/ContactsTable";
import ContactsTopBar from "../components/contacts/ContactsTopBar";

export default function ContactsPage() {
  const { setTabs, setIsSidebarVisible } = useContext(GlobalContext);

  useEffect(() => {
    setTabs(SalesSubHeaderTabs);
    setIsSidebarVisible(false);
  }, [setTabs, setIsSidebarVisible]);

  return (
    // 1) FULL‐SCREEN background image container
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100vw",
        height: "100vh",
        backgroundImage: "url('/assets/images/background/background.png')",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        zIndex: 0, // sit behind everything
      }}
    >
      {/* 2) Inner “card” with white bg, trimmed by margins */}
      <div
        role="main"
        style={{
          position: "relative",
          margin: "6.3rem 0.7rem 0.7rem", // gap under the tabs bar, and sides
          backgroundColor: "#fff",
          height: "calc(100% - 6.3rem - 0.7rem)", // fill below tabs minus bottom gap
          overflow: "hidden",
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          zIndex: 1, // content sits above the background
        }}
      >
        <section
          role="tabpanel"
          aria-expanded="true"
          className="tabContent active oneConsoleTab"
          style={{ height: "100%", display: "flex", flexDirection: "column" }}
        >
          <ContactsTopBar />
          <div
            className="slds-grid"
            style={{
              minHeight: "calc(100% - 5.5rem)", // subtract header height
              backgroundColor: "#fff",
            }}
          >
            <ContactsTable />
          </div>
        </section>
      </div>
    </div>
  );
}
