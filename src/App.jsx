import React, { useContext } from "react";
import "./App.css";
import Header from "./components/Header";
import Sidebar from "./components/Sidebar";
import MainPage from "./components/MainPage";
import SubHeader from "./components/SubHeader";

import { GlobalContext } from "./context/GlobalContext";
import { useLocation } from "react-router-dom";
const noLayOutURLS = [
  "/setup/approval-processes/lookup",
  "/NewEmailAlert/Lookup",
  "/NewFieldUpdate/Lookup",
  "/setup/approval-processes/picklist-lookup",
];
export default function App() {
  const { isSidebarVisible } = useContext(GlobalContext);
  const location = useLocation();
  if (noLayOutURLS.includes(location.pathname)) {
    return <MainPage />;
  }
  return (
    <>
      <header className="slds-global-header_container">
        <Header />
        <SubHeader />
      </header>

      <main className={`${isSidebarVisible ? `slds-has-sidebar` : ""}`}>
        {isSidebarVisible && <Sidebar />}
        <MainPage />
      </main>
    </>
  );
}
