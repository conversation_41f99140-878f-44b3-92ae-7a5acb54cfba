import sys
from pathlib import Path
# allow importing prompt_components (in ../) and utils (in ../../)
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))
sys.path.insert(0, str(Path(__file__).resolve().parents[2]))

from prompt_components import (
    gen_email_template_prompt_and_data,
    gen_approval_process_prompt_and_data
)
from utils.helper import write_json_file, INITIAL_OUTPUT_VALUE

def sampler(output_path, num_prompts):
    results = []
    for _ in range(num_prompts):
        # 1) generate the two pieces
        email    = gen_email_template_prompt_and_data()
        approval = gen_approval_process_prompt_and_data(email["result"])

        # 2) stitch together the prompt text
        full_prompt = f"{email['prompt']} {approval['prompt']}"

        # 3) reshape into your exact global schemas
        email_data = email["result"]
        classic_entry = {
            "templateType":       email_data["templateType"],
            "folder":             email_data["folder"],
            "availableForUse":    False,
            "emailTemplateName":  email_data["name"],
            "templateUniqueName": email_data["name"],
            "encoding":           "Unicode (UTF-8)",
            "description":        "",
            "subject":            email_data["subject"],
            "body":               email_data["body"]
        }

        proc = approval["result"]
        filters = [{
            "field":    proc["entryCriteria"]["field"],
            "operator": proc["entryCriteria"]["operator"],
            "value":    str(proc["entryCriteria"]["value"])
        }] + [{"field":"--None--","operator":"--None--","value":""} for _ in range(4)]

        approval_entry = {
            "step1": {
                "name":       proc["processName"],
                "uniqueName": "_".join(proc["processName"].split(" ")),
                "desc":       ""
            },
            "step2": {"approvalProcessCriteria": "criteria are met", "filterLogics": filters},
            "step3": {
                "nextAutomatedApproverDeterminedBy": proc["approverField"],
                "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.",
                "useApproverFieldOfOpportunityOwner": False
            },
            "step4": {"approvalAssignmentEmailTemplate": proc["emailTemplate"]},
            "step5": {
                "selectedFields": ["Opportunity Name","Opportunity Owner"] + proc["visibleFields"],
                "approvalPageFields": True,
                "securitySettings":      "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"
            },
            "step6": proc["step6"]["result"],
            "approvalStep": proc["newStep"],
            "active": True,
            "hasCreatedSteps": True,
        }

        final_result = {
            **INITIAL_OUTPUT_VALUE,
            "classicEmailTemplate": {},
            "classicEmailTemplateList": [classic_entry],
            "fieldUpdate": {},
            "fieldUpdateList": [],
            "approvalProcesses": {
                "step1": {},
                "step2": {},
                "step3": {},
                "step4": {},
                "step5": {},
                "step6": {},
                "active": False,
                "hasCreatedSteps": False,
            },
            "approvalProcessesList": [approval_entry],
            "opportunity": {},
            "opportunityList": []
        }

        # <<< Make sure this append is INSIDE the loop! >>>
        results.append({"prompt": full_prompt, "result": final_result})

    # <<< And write out AFTER the loop, not inside it >>>
    write_json_file(results, output_path)

