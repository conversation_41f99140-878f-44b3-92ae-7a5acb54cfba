# scripts/workflow-a139/script.py
#!/usr/bin/env python3
import sys
import argparse
from pathlib import Path

sys.path.insert(0, str(Path(__file__).resolve().parents[1]))
sys.path.insert(0, str(Path(__file__).resolve().parents[2]))

# from prompt_components import gen_a139_prompt_and_data
from sampler import sampler

def main():
    parser = argparse.ArgumentParser(description="Generate A139 prompts")
    parser.add_argument(
        "-o", "--output",
        default="generated_workflows/a139/prompts.json",
        help="Path to write the generated prompts.json"
    )
    parser.add_argument(
        "-n", "--number",
        type=int,
        default=20,
        help="Number of prompts to generate"
    )
    args = parser.parse_args()

    # Ensure the target directory exists
    Path(args.output).parent.mkdir(parents=True, exist_ok=True)
    sampler(args.output, args.number)
    
    
if __name__ == "__main__":
    main()