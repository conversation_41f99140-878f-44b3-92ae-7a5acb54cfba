#!/usr/bin/env python3
import sys
import argparse
from pathlib import Path

# Allow imports from ../components and ../utils
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))
sys.path.insert(0, str(Path(__file__).resolve().parents[2]))

from prompt_components import gen_a139_prompt_and_data
from utils.helper       import write_json_file, INITIAL_OUTPUT_VALUE

def sampler(output_path: str, num_prompts: int):
    """
    Generate `num_prompts` A139 samples and write them to disk.
    Each sample has the shape:
      {
        "prompt": <string>,
        "result": {
          "approvalProcesses": { step1…step6, active, hasCreatedSteps },
          "approvalProcessesList": [ { … } ],
          "emailAlert": { … },
          "opportunity": { … }
        }
      }
    """
    results = []
    for _ in range(num_prompts):
        sample = gen_a139_prompt_and_data()
        prompt = sample["prompt"]
        data   = sample["result"]

        ap = data["approvalProcess"]
        ea = data["emailAlert"]
        opp = data["opportunity"]

        # approvalProcesses summary (empty shells)
        approval_processes = {
            "step1": {}, "step2": {}, "step3": {},
            "step4": {}, "step5": {}, "step6": {},
            "active": False, "hasCreatedSteps": False
        }

        # Build the single approvalProcessesList entry
        filters = (
            [{"field": ap["entryCriteria"]["field"],
              "operator": ap["entryCriteria"]["operator"],
              "value": str(ap["entryCriteria"]["value"])}]
            + [{"field":"--None--","operator":"--None--","value":""}
               for _ in range(4)]
        )
        approval_entry = {
            "step1": {
                "name":       ap["processName"],
                "uniqueName": "_".join(ap["processName"].split()),
                "desc":       ""
            },
            "step2": {
                "approvalProcessCriteria": "criteria are met",
                "filterLogics":            filters
            },
            "step3": {
                "nextAutomatedApproverDeterminedBy": ap["approverField"],
                "recordEditabilityProperties":
                    "Administrators ONLY can edit records during the approval process.",
                "useApproverFieldOfOpportunityOwner": False
            },
            "step4": {
                "approvalAssignmentEmailTemplate": ap["emailTemplate"]
            },
            "step5": {
                "selectedFields":     ap.get("visibleFields", []),
                "approvalPageFields": True,
                "securitySettings":
                    "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"
            },
            "step6":       ap.get("step6", {}),
            "approvalStep": ap.get("newStep", {}),
            "active":      True,
            "hasCreatedSteps": True
        }

        # Compose the final result object
        final_result = {
            **INITIAL_OUTPUT_VALUE,
            "approvalProcesses":     approval_processes,
            "approvalProcessesList": [approval_entry],
            "emailAlert":            ea,
            "opportunity":           opp
        }

        results.append({"prompt": prompt, "result": final_result})

    write_json_file(results, output_path)
    print(f"{num_prompts} {'prompts were' if num_prompts > 1 or num_prompts == 0 else 'prompt was'} generated and can be found in '{output_path}'.")