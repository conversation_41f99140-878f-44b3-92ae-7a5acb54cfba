import random
from typing import Any, Dict
from utils.helper import read_json_file

def final_rejection_field_update():
    name = random.choice([
        "High Value Status",
        "Budget Constraints",
        "No Response",
        "Duplicate Lead",
        "Invalid Email Address",
        "Out of Service Area",
        "Requested No Contact",
        "Product Mismatch",
        "Lead Not Interested",
        "Low Lead Score",
        "Communication Failure",
        "Other"
    ])
    data = {
        "name": name,
        "uniqueName": "_".join(name.split(" ")),
        "description": f"This is for {name}.",
        "fieldToUpdate": "Closed Won",
        "reEvaluateWorkflowRules": False,
        "textOptions": "A blank value (null)"
    }
    prompt = (" Add a final rejection action for field update where name is '{name}', unique name is '{uniqueName}', description '{description}', and the 'Field to Update' should be {fieldToUpdate}. The 'Re-evaluate Workflow Rules after Field Change' should be unchecked, and text options should be '{textOptions}'.")
    return {
        "prompt": prompt.format(**data),
        "data": data
    }

def gen_email_alert_prompt_and_data() -> Dict[str, Any]:
    """
    Generates the prompt and payload for the "Email Alert" approval action.
    """
    # 1) Load and sample recipients
    recipients = read_json_file("AvailableRecipients.json")
    sample_size = min(len(recipients), 3)
    selected_recipients = random.sample(recipients, sample_size)

    # 2) Randomize names
    name        = f"EmailAlert_{random.randint(1000,9999)}"
    unique_name = name
    description = f"Alert for {name}"

    # 3) Pick a template (could be any existing template name)
    templates = ["High-value Opportunity Notification",
                 "Rescheduled Service Appointment Confirmation Email"]
    template   = random.choice(templates)

    # 4) Pick a field for the final‐rejection Field Update
    #    Reuse your APFieldOptions.json
    fields     = read_json_file("APFieldOptions.json")
    field_to_update = random.choice(fields)
    
    _additional_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ]


    # 5) Build the human‐language “what to do” prompt
    prompt = (
        "Then, create a new approval action “Email Alert.” "
        f"Enter Description “{description}”, Unique Name “{unique_name}”, and select Email Template “{template}”. "
        "Check “Protected Component”. "
        f"Move {', '.join(selected_recipients)} from Available Recipients to Selected Recipients, then Save. "
        "Under Final Rejection Actions, add a new Field Update: "
        f"Name “FU_{random.randint(1000,9999)}” and select field “{field_to_update}”. "
        "Activate the action."
    )
    additional_emails = random.sample(_additional_emails, random.randint(0,5))
    __additional_emails = [f"'{email}'" for email in additional_emails]
    make_default = random.choice([False, True])
    from_email_address = "Current User's email address"
    prompt += "" if len(additional_emails) == 0 else f" Add the {', '.join(__additional_emails)} emails to additional emails field."

    prompt += f"Select '{from_email_address}' from the 'From Email Address' field and {'check' if make_default else 'uncehck'} the 'Make this address default' option."

    fru = final_rejection_field_update()
    
    prompt += fru["prompt"]

    # 6) Construct the machine‐readable result
    result = {
        "emailAlert": {
            "description":        description,
            "uniqueName":         unique_name,
            "emailTemplate":      template,
            "protectedComponent": True,
            "selectedRecipients": selected_recipients,
            "additionalEmails": additional_emails,
            "makeDefault": make_default,
            "from_email_address": from_email_address
        },
        "finalRejectionFieldUpdate": fru["data"],
        "activated": True
    }

    return {"prompt": prompt, "result": result}