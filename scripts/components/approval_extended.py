# scripts/components/approval_extended.py

import random
from typing import Any, Dict

from components.approval_process      import gen_approval_process_prompt_and_data
from components.email_alert          import gen_email_alert_prompt_and_data
from components.opportunity_creation import gen_opportunity_creation_prompt_and_data

def gen_a139_prompt_and_data() -> Dict[str, Any]:
    """
    Workflow A139 combined prompt+result:
      • approvalProcess  – from approval_process.py
      • emailAlert       – from email_alert.py
      • opportunity      – from opportunity_creation.py
    """
   
    email_tpl   = {"name": "High-value Opportunity Notification"}
    ap_base     = gen_approval_process_prompt_and_data(email_tpl)
    ap_prompt   = ap_base["prompt"]
    ap_result   = ap_base["result"]

    
    ea          = gen_email_alert_prompt_and_data()
    ea_prompt   = ea["prompt"]
    ea_result   = ea["result"]

    
    opp         = gen_opportunity_creation_prompt_and_data()
    opp_prompt  = opp["prompt"]
    opp_obj     = opp["result"]["opportunity"]

    
    full_prompt = " ".join([ap_prompt, ea_prompt, opp_prompt])

    
    result: Dict[str, Any] = {
        "approvalProcess": ap_result,
        "emailAlert":      ea_result,
        "opportunity":     opp_obj
    }

    return {"prompt": full_prompt, "result": result}