# scripts/components/approval_process.py
import random
from utils.helper import read_json_file, join_with_commas_and
from typing import Any, Dict

import random
from typing import Any, Dict

def gen_approval_process_step_creation_prompt_and_data() -> Dict[str, Any]:
    """
    Generates a prompt and corresponding data payload for creating
    the first step of an approval process.
    """
    # Define possible approval process names
    name_choices = ["Manager Approval", "GM Approval", "CEO Approval", "CFO Approval"]
    name = random.choice(name_choices)
    unique_name = "_".join(name.split())
    description = f"This is for {name}."

    # Build the data structure
    data: Dict[str, Any] = {
        "approvalStep": {
            "approvalStepOption": "Yes, I'd like to create an approval step now."
        },
        "approvalStep1": {
            "approvalProcessName": name,
            "approvalProcessUniqueName": "_".join(unique_name.split(" ")),
            "approvalProcessDesc": description,
            "approvalProcessStepNumber": "1"
        },
        "approvalStep2": {
            "stepCriteria": "All records should enter this step."
        },
        "approvalStep3": {
            "approverAction": "Automatically assign using the user field selected earlier. (Manager)",
            "requestApproval": False
        }
    }

    # Single prompt template
    prompt_template = (
        "Then create an approval step with the name {approvalProcessName}, "
        "description '{approvalProcessDesc}', and step number {approvalProcessStepNumber}. "
        "For the 'Specify Step Criteria' select the '{stepCriteria}' and then for the 'Select Approver' "
        "select the '{approverAction}'."
    )

    # Merge the relevant pieces for formatting
    merged_fields = {
        **data["approvalStep1"],
        **data["approvalStep2"],
        **data["approvalStep3"]
    }

    prompt = prompt_template.format(**merged_fields)

    return {
        "prompt": prompt,
        "result": data
    }

    

def gen_step_6_prompt_and_data(picklist_file: str = "APStep6PicklistData.json") -> Dict[str, Any]:
    """
    Generates a Step 6 prompt and corresponding data payload for approval process submission.
    """
    # Load the picklist options from JSON
    picklist_data = read_json_file(picklist_file)

    # Randomly pick one submitter type
    submitter_type = random.choice(list(picklist_data.keys()))

    # Determine how many options to sample (up to 3, but not more than exist)
    choices = picklist_data[submitter_type]
    sample_size = min(len(choices), 3)
    selected_options = random.sample(choices, sample_size)

    # Human-readable labels for each submitter type
    LABELS = {
        "creator": "Creator",
        "owner": "Owner",
        "publicGroups": "Public Groups",
        "role": "Role",
        "roleAndInternal": "Role and Internal Subordinates",
        "user": "User"
    }

    # Build the user prompt
    quoted = ", ".join(f"'{opt}'" for opt in selected_options)
    prompt = (
        f"Select {quoted} from '{LABELS.get(submitter_type, submitter_type)}' "
        "option of the submitter type."
    )

    # Construct the result payload
    result = {
        "submitterType": LABELS[submitter_type],
        "allowedSubmitters": selected_options,
        "pageLayoutSettings": True,
        "allowSubmittersToRecallApprovalRequests": False
    }

    return {"prompt": prompt, "result": result}
    

def gen_approval_process_prompt_and_data(email_tpl):
    fields    = read_json_file("APFieldOptions.json")
    operators = read_json_file("APOperatorOptions.json")

    # Random selections
    entryField    = "Opportunity: Amount"
    operator      = random.choice([o for o in operators if o != "--None--"])
    threshold     = random.randint(100, 5000)
    processName   = f"{random.choice(['Expense','Expenditure','Spendings'])} Report Approval"
    approverField = "Manager"
    visibleFields = ["Account Name", "Amount", "Contract"]
    submitter     = random.choice(["Owner","Record Creator","Last Modified By"])
    stepName      = random.choice(["First Approval","Manager Approval","Lead Approval"])

    # Generating data and prompt for step 6
    step6 = gen_step_6_prompt_and_data()
    new_step = gen_approval_process_step_creation_prompt_and_data()
    # Build concise, “what to do” prompt, section by section
    prompt_sections = [
        f"Create an approval process using Standard Setup Wizard named “{processName}.”",
        f"Set entry criteria where {entryField} {operator} {threshold}.",
        f"Specify the approver field as {approverField}.",
        f"Use the email template “{email_tpl['name']}” for assignment emails.",
        f"Include fields {join_with_commas_and(visibleFields)} in the approval layout.",
        step6["prompt"],
        f"{new_step['prompt']}"
        "Activate the approval process."
    ]
    
    # Note: we now have 1 fewer section because “Use Standard Wizard” and “When prompted…” merged into “Create”
    prompt = " ".join(prompt_sections)

    result = {
        "processName":     processName,
        "entryCriteria":   {"field": entryField, "operator": operator, "value": threshold},
        "approverField":   approverField,
        "emailTemplate":   email_tpl["name"],
        "visibleFields":   visibleFields,
        "initialSubmitter": submitter,
        "approvalStep":    {
            "name": stepName,
            "stepNumber": 1,
            "criteria": "All records enter this step",
            "approverField": approverField
        },
        "step6": step6,
        "newStep": new_step["result"],
        "activated": True
    }
    return {"prompt": prompt, "result": result}