import random
from datetime import date, timedelta
from typing import Any, Dict

from utils.helper import read_json_file

def gen_opportunity_creation_prompt_and_data() -> Dict[str, Any]:
    """
    Generates a prompt + data payload for creating a new Opportunity.
    """
    # 1️⃣ load picklists
    types    = read_json_file("OpportunityTypeOptions.json")
    stages   = read_json_file("OpportunityStageOptions.json")
    accounts = read_json_file("AccountNames.json")

    # 2️⃣ random values
    name        = f"Opportunity_{random.randint(1000,9999)}"
    amount      = random.randint(1000, 100000)
    close_date  = (date.today() + timedelta(days=random.randint(1, 60))).isoformat()
    account     = random.choice(accounts)
    type_       = random.choice([t for t in types if t != "--None--"])
    stage       = random.choice([s for s in stages if s != "--None--"])
    probability = random.randint(0, 100)

    # 3️⃣ build the “what to do” prompt
    prompt = (
        f"From Sales in App Launcher, "
        f"create a new Opportunity named '{name}' with amount {amount}, "
        f"close date {close_date}, account name '{account}', "
        f"type '{type_}', stage '{stage}', and probability '{probability}%' and save the opportunity."
    )

    # 4️⃣ machine-readable result
    result = {
        "opportunity": {
            "name":         name,
            "amount":       amount,
            "closeDate":    close_date,
            "accountName":  account,
            "type":         type_,
            "stage":        stage,
            "probability":  f"{probability}%"
        }
    }

    return {"prompt": prompt, "result": result}