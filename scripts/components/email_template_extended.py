# scripts/components/email_template_extended.py

from typing import Any, Dict
from components.email_template             import gen_email_template_prompt_and_data
from components.new_contact_creation      import gen_new_contact_creation_prompt_and_data

def gen_email_template_extended_prompt_and_data() -> Dict[str, Any]:
    """
    Builds on the classic email-template prompt by:
      1) Verifying and updating the template’s fields (Fax Opt Out)
      2) Creating a new Contact under Express Logistics and Transport
    """
    
    base         = gen_email_template_prompt_and_data()
    prompt       = base["prompt"]
    result       = base["result"]

   
    extension    = (
        "Then edit the created email template, and then "
        "select 'Contact Fields' as the field type, choose 'Fax Opt Out' as the field, "
        "and save the updated template."
    )

   
    contact      = gen_new_contact_creation_prompt_and_data()
    contact_prompt = contact["prompt"]
    contact_data   = contact["result"]

   
    full_prompt  = f"{prompt} {extension} {contact_prompt}"

   
    merged_result = { **result, **contact_data }

    return {"prompt": full_prompt, "result": merged_result}