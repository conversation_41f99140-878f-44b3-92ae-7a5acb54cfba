import random
from typing import Any, Dict
from utils.helper import read_json_file

def gen_email_alert_prompt_and_data() -> Dict[str, Any]:
    """
    Generates the prompt and payload for the “Field Update” approval action,
    using the real Opportunity field picklist and matching the final result shape.
    """
    # 1) Load & sample recipients
    recipients = read_json_file("AvailableRecipients.json")
    

    # 2) Randomize the action’s metadata
    name        = f"FieldUpdate_{random.randint(1000,9999)}"
    unique_name = name
    description = f"Field update for {name}"

    # 3) Load the true picklist of Opportunity fields
    fields     = read_json_file("OpportunityFieldOptions.json")
    # drop the “--None--” option
    choices    = [f for f in fields if f != "--None--"]
    field_pick = random.choice(choices)

    # 4) Build the “what to do” prompt
    prompt = (
        "Then create a new approval action “Field Update.” "
        f"Set Description to “{description}” and Unique Name to “{unique_name},” then choose the field “{field_pick}.” "
        "Under Final Rejection Actions, add another Field Update: "
        f"Name “FU_{random.randint(1000,9999)}” and select “{field_pick},” then save. "
        "Finally, add Amount, Stage and Close Date to the Approval Page Fields and activate the action."
    )

    # 5) Construct the machine-readable result
    result: Dict[str, Any] = {
        "approvalAction":            "Field Update",
        "description":               description,
        "uniqueName":                unique_name,
        "fieldToUpdate":             field_pick,
        "finalRejectionFieldUpdate": {
            "name":          f"FU_{random.randint(1000,9999)}",
            "fieldToUpdate": field_pick
        },
        "approvalPageFields":        ["Amount", "Stage", "Close Date"],
        "activated":                 True
    }

    return {"prompt": prompt, "result": result}