# scripts/components/new_contact_creation.py

import random
from pathlib import Path
from typing import Any, Dict
from utils.helper import read_json_file

def gen_new_contact_creation_prompt_and_data() -> Dict[str, Any]:
    """
    Generates the prompt and payload for creating a new Contact
    under the Account 'Express Logistics and Transport'.
    """
    # 1️⃣ load salutations and optionally first/last name lists
    salutations = read_json_file("SalutationOptions.json")

    # Use custom pools if you’ve dropped these files in utils/data/
    first_names_path = Path(__file__).parent / "data" / "FirstNames.json"
    last_names_path  = Path(__file__).parent / "data" / "LastNames.json"

    if first_names_path.exists():
        first_names = read_json_file("FirstNames.json")
    else:
        first_names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]

    if last_names_path.exists():
        last_names = read_json_file("LastNames.json")
    else:
        last_names = ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"]

    # 2️⃣ pick random values
    salutation = random.choice(salutations)
    first_name = random.choice(first_names)
    last_name  = random.choice(last_names)

    # 3️⃣ build the “what to do” prompt
    prompt = (
        "Open Sales from the App Launcher. "
        "In Contacts, edit the 'Express Logistics and Transport' record and "
        f"create a new Contact with salutation '{salutation}', first name '{first_name}', "
        f"and last name '{last_name}'. Save the contact."
    )

    # 4️⃣ machine-readable result
    result = {
        "contact": {
            "account":      "Express Logistics and Transport",
            "salutation":   salutation,
            "firstName":    first_name,
            "lastName":     last_name
        }
    }

    return {"prompt": prompt, "result": result}
