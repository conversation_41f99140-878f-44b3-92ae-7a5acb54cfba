import json
import os
import logging

def join_with_commas_and(items):
    """
    Convert a list of strings into a human-friendly comma-and list.
    E.g. ['A','B','C'] → 'A, B and C'
    """
    if not items:
        return ''
    if len(items) == 1:
        return items[0]
    if len(items) == 2:
        return f'{items[0]} and {items[1]}'
    return ', '.join(items[:-1]) + ' and ' + items[-1]

def read_json_file(relative_path):
    """
    Load and return JSON data from utils/data/<relative_path>.
    Example: read_json_file("APFieldOptions.json") will open
    utils/data/APFieldOptions.json.
    """
    base_dir = os.path.dirname(__file__)
    file_path = os.path.join(base_dir, 'data', relative_path)
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def write_json_file(data, output_path):
    """
    Write `data` as pretty-printed JSON to `output_path`.
    Ensures parent directories exist and appends a newline.
    """
    directory = os.path.dirname(output_path)
    if directory:
        os.makedirs(directory, exist_ok=True)
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
            f.write('\n')
    except Exception as e:
        logging.error(f"Failed to write JSON to {output_path}: {e}")
        raise
    
INITIAL_OUTPUT_VALUE = {
    "classicEmailTemplate": {},
    "classicEmailTemplateList": [],
    "fieldUpdate": {},
    "fieldUpdateList": [],
    "approvalProcesses": {},
    "approvalProcessesList": [],
    "opportunity": {},
    "opportunityList": [],
    "contact": {},
    "contactList": [],
    "emailAlert": {}
}
