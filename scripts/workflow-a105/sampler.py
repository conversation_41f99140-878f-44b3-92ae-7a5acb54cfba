#!/usr/bin/env python3
import sys
import argparse
import re
from pathlib import Path

# allow imports from ../components and ../utils
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))
sys.path.insert(0, str(Path(__file__).resolve().parents[2]))

from prompt_components        import gen_approval_process_prompt_and_data
from components.field_update import gen_email_alert_prompt_and_data
from utils.helper             import write_json_file

def sampler(output_path: str, num_prompts: int):
    """
    Generate `num_prompts` A105 samples and write them to disk.
    Each sample’s `result` will have:
      - a nested `approvalProcesses` template
      - one-entry `approvalProcessesList` built from the gen_approval_process data
      - a `fieldUpdate` object built from the gen_email_alert data
    """
    results = []
    for _ in range(num_prompts):
        # 1️⃣ Approval process
        approval   = gen_approval_process_prompt_and_data({"name": "High-value Opportunity Notification"})
        ap_prompt  = approval["prompt"]
        ap_result  = approval["result"]

        # 2️⃣ Strip out unwanted sentences
        ap_prompt = re.sub(r"Include fields [^.]*? in the approval layout\.\s*", "", ap_prompt)
        ap_prompt = re.sub(r"Select [^.]*?option of the submitter type\.\s*", "", ap_prompt)

        # 3️⃣ Insert save-defaults instruction immediately after the email-template sentence
        ap_prompt = re.sub(
            r"(Use the email template “[^”]+” for assignment emails\.)",
            r"\1 Save all default settings and move to the next step.",
            ap_prompt
        )

        # 4️⃣ Build the nested approvalProcesses template
        nested = {
            "approvalProcesses": {
                "step1": {},
                "step2": {},
                "step3": {},
                "step4": {},
                "step5": {},
                "step6": {},
                "active": False,
                "hasCreatedSteps": False
            },
            "approvalProcessesList": []
        }

        # 5️⃣ Construct the single-entry approvalProcessesList item
        entry = ap_result["entryCriteria"]
        filters = (
            [{"field": entry["field"], "operator": entry["operator"], "value": str(entry["value"])}] +
            [{"field": "--None--", "operator": "--None--", "value": ""} for _ in range(4)]
        )

        one_process = {
            "step1": {
                "name": ap_result["processName"],
                "uniqueName": "_".join(ap_result["processName"].split()),
                "desc": ""
            },
            "step2": {
                "approvalProcessCriteria": "criteria are met",
                "filterLogics": filters
            },
            "step3": {
                "nextAutomatedApproverDeterminedBy": ap_result["approverField"],
                "recordEditabilityProperties": "Administrators ONLY can edit records during the approval process.",
                "useApproverFieldOfOpportunityOwner": False
            },
            "step4": {
                "approvalAssignmentEmailTemplate": ap_result["emailTemplate"]
            },
            "step5": {
                "selectedFields": [],
                "approvalPageFields": True,
                "securitySettings": "Allow approvers to access the approval page only from within the Salesforce application. (Recommended)"
            },
            # no step6 since we stripped it
            "approvalStep": ap_result["approvalStep"],
            "active": True,
            "hasCreatedSteps": True
        }

        nested["approvalProcessesList"].append(one_process)

        # 6️⃣ Field Update action
        field_update = gen_email_alert_prompt_and_data()
        fu_prompt    = field_update["prompt"]
        fu_result    = field_update["result"]

        # 7️⃣ Wrap the field‐update result under "fieldUpdate"
        wrapped_fu = {"fieldUpdate": fu_result}

        # 8️⃣ Final prompt and result assembly
        full_prompt = f"{ap_prompt.strip()} {fu_prompt}".strip()
        full_result = {**nested, **wrapped_fu}

        results.append({"prompt": full_prompt, "result": full_result})

    write_json_file(results, output_path)

def main():
    parser = argparse.ArgumentParser(description="Generate A105 prompts")
    parser.add_argument(
        "-o", "--output",
        default="generated_workflows/a105/prompts.json",
        help="Path to write the generated prompts.json"
    )
    parser.add_argument(
        "-n", "--number",
        type=int,
        default=20,
        help="How many prompts to generate"
    )
    args = parser.parse_args()

    # ensure output folder exists
    Path(args.output).parent.mkdir(parents=True, exist_ok=True)
    sampler(args.output, args.number)

if __name__ == "__main__":
    main()