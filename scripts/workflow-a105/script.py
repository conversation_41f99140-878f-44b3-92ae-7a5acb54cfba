#!/usr/bin/env python3
import sys
from pathlib import Path

# allow imports from ../components and ../utils
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))
sys.path.insert(0, str(Path(__file__).resolve().parents[2]))

from prompt_components       import gen_approval_process_prompt_and_data
from components.field_update import gen_email_alert_prompt_and_data

def main():
    # Generate a single example
    approval     = gen_approval_process_prompt_and_data({"name": "High-value Opportunity Notification"})
    field_update = gen_email_alert_prompt_and_data()

    prompt = f"{approval['prompt']} {field_update['prompt']}"
    result = {**approval["result"], **field_update["result"]}

    print("\n=== FULL PROMPT ===\n")
    print(prompt)
    print("\n=== RESULT ===\n")
    print(result)

if __name__ == "__main__":
    main()