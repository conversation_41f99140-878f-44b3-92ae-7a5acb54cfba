#!/usr/bin/env python3
import sys
import argparse
from pathlib import Path

# Allow imports from ../components and ../utils
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))
sys.path.insert(0, str(Path(__file__).resolve().parents[2]))

from prompt_components import gen_email_template_extended_prompt_and_data
from utils.helper       import write_json_file, INITIAL_OUTPUT_VALUE

def sampler(output_path: str, num_prompts: int):
    """
    Generate `num_prompts` A61 samples and write them to disk.
    Each sample has the structure:
      {
        "prompt": <string>,
        "result": {
          "classicEmailTemplate": {},
          "classicEmailTemplatesList": [ { … } ],
          "fieldUpdate": {},
          "fieldUpdateList": [],
          # plus any A61-specific fields
        }
      }
    """
    results = []
    for _ in range(num_prompts):
        sample = gen_email_template_extended_prompt_and_data()
        prompt = sample["prompt"]
        data   = sample["result"]

        # Build the result structure exactly as defined by A61
        final_result = {
            **INITIAL_OUTPUT_VALUE,
            "classicEmailTemplate": {},
            "classicEmailTemplateList": [
                {
                    "templateType":       data["templateType"],
                    "folder":             data["folder"],
                    "availableForUse":    False,
                    "emailTemplateName":  data["name"],
                    "templateUniqueName": data["name"],
                    "encoding":           "Unicode (UTF-8)",
                    "description":        "",
                    "subject":            data["subject"],
                    "body":               data["body"],
                }
            ],
            "fieldUpdate":     {},
            "fieldUpdateList": [],
            "contact": {},
            "contactList": [
                data["contact"]
            ]
        }

        results.append({"prompt": prompt, "result": final_result})

    write_json_file(results, output_path)
    

def main():
    parser = argparse.ArgumentParser(description="Generate A61 prompts")
    parser.add_argument(
        "-o", "--output",
        default="generated_workflows/a61/prompts.json",
        help="Path to write prompts.json"
    )
    parser.add_argument(
        "-n", "--number",
        type=int,
        default=20,
        help="Number of prompts to generate"
    )
    args = parser.parse_args()

    # Ensure the target directory exists
    Path(args.output).parent.mkdir(parents=True, exist_ok=True)
    sampler(args.output, args.number)

if __name__ == "__main__":
    main()